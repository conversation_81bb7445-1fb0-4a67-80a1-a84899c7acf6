import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Series from '@/models/Series';
import Episode from '@/models/Episode';
import VidSrcAPI from '@/lib/vidsrc';
import IMDbEpisodeScraper from '@/lib/imdbEpisodeScraper';

interface EpisodeStats {
  totalFound: number;
  newEpisodesAdded: number;
  existingEpisodesUpdated: number;
  totalEpisodesInDB: number;
  seasonBreakdown: Record<number, {
    episodeCount: number;
    episodes: number[];
    missingEpisodes: number[];
    hasGaps: boolean;
    completionPercentage: number;
    expectedEpisodes?: number;
  }>;
  episodeRange: {
    minSeason: number;
    maxSeason: number;
    minEpisode: number;
    maxEpisode: number;
  };
  duplicatesRemoved: number;
  errors: string[];
  verificationResults: {
    totalSeasonsFound: number;
    totalSeasonsWithGaps: number;
    overallCompletionPercentage: number;
    missingSeasonRanges: string[];
    suspiciousGaps: Array<{
      season: number;
      missingRange: string;
      severity: 'minor' | 'major' | 'critical';
    }>;
  };
}

interface VidSrcEpisode {
  season: number;
  episode: number;
  embed_url: string;
  embed_url_tmdb?: string;
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await connectDB();
    const { id } = await params;

    // Get the series by IMDb ID (the id parameter is actually the IMDb ID)
    const series = await Series.findOne({ imdbId: id });
    if (!series) {
      return NextResponse.json({ error: 'Series not found' }, { status: 404 });
    }

    console.log(`🔍 Advanced Episode Analysis for: ${series.title} (${series.imdbId})`);

    // Initialize advanced statistics
    const stats: EpisodeStats = {
      totalFound: 0,
      newEpisodesAdded: 0,
      existingEpisodesUpdated: 0,
      totalEpisodesInDB: 0,
      seasonBreakdown: {},
      episodeRange: {
        minSeason: Infinity,
        maxSeason: 0,
        minEpisode: Infinity,
        maxEpisode: 0
      },
      duplicatesRemoved: 0,
      errors: [],
      verificationResults: {
        totalSeasonsFound: 0,
        totalSeasonsWithGaps: 0,
        overallCompletionPercentage: 0,
        missingSeasonRanges: [],
        suspiciousGaps: []
      }
    };

    // Get existing episodes from database using both seriesId and imdbId for accuracy
    const existingEpisodes = await Episode.find({
      $or: [
        { seriesId: series._id },
        { imdbId: series.imdbId }
      ]
    });
    const existingEpisodeMap = new Map<string, any>();

    existingEpisodes.forEach(ep => {
      const key = `S${ep.season}E${ep.episode}`;
      existingEpisodeMap.set(key, ep);
    });

    console.log(`📊 Current database state: ${existingEpisodes.length} episodes (seriesId: ${series._id}, imdbId: ${series.imdbId})`);

    // ADVANCED: Get comprehensive episode data from IMDb (most reliable source)
    console.log(`🎬 ADVANCED: IMDb-based episode verification for series: ${series.title} (${series.imdbId})`);
    const imdbScraper = IMDbEpisodeScraper.getInstance();
    const imdbEpisodeData = await imdbScraper.getSeriesEpisodes(series.imdbId);

    if (!imdbEpisodeData) {
      console.log(`❌ Could not retrieve episode data from IMDb for ${series.title}`);
      return NextResponse.json({
        message: 'Could not retrieve episode data from IMDb',
        stats: {
          ...stats,
          totalEpisodesInDB: existingEpisodes.length
        }
      });
    }

    console.log(`🎯 IMDb Analysis: Found ${imdbEpisodeData.totalEpisodes} episodes across ${imdbEpisodeData.totalSeasons} seasons`);
    console.log(`📊 Season breakdown: ${imdbEpisodeData.seasons.map(s => `S${s.seasonNumber}(${s.episodeCount})`).join(', ')}`);

    // Step 2: Generate VidSrc embed URLs for all IMDb episodes
    console.log(`🔗 Generating VidSrc embed URLs for all IMDb episodes...`);

    // Step 3: Process ALL episodes from IMDb data (IMDb is the authoritative source)
    const allIMDbEpisodes: Array<{
      season: number;
      episode: number;
      title: string;
      description?: string;
      airDate?: string;
      rating?: number;
      embedUrl?: string;
      embedUrlTmdb?: string;
    }> = [];

    // Process every episode that IMDb says exists and generate VidSrc URLs
    imdbEpisodeData.seasons.forEach(season => {
      season.episodes.forEach(episode => {
        // Generate VidSrc embed URL directly
        const vidsrcEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${episode.season}&episode=${episode.episode}`;
        const vidsrcTmdbUrl = `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId || ''}&season=${episode.season}&episode=${episode.episode}`;

        allIMDbEpisodes.push({
          season: episode.season,
          episode: episode.episode,
          title: episode.title,
          description: episode.description,
          airDate: episode.airDate,
          rating: episode.rating && !isNaN(episode.rating) ? episode.rating : undefined,
          embedUrl: vidsrcEmbedUrl,
          embedUrlTmdb: vidsrcTmdbUrl
        });

        console.log(`🔗 Generated VidSrc URL for S${episode.season}E${episode.episode}: ${vidsrcEmbedUrl}`);
      });
    });

    stats.totalFound = allIMDbEpisodes.length;
    console.log(`✅ IMDb-Based Analysis: ${allIMDbEpisodes.length} episodes from IMDb, all with generated VidSrc streaming links`);

    // Analyze episode range from IMDb data
    if (allIMDbEpisodes.length > 0) {
      stats.episodeRange.minSeason = Math.min(...allIMDbEpisodes.map(e => e.season));
      stats.episodeRange.maxSeason = Math.max(...allIMDbEpisodes.map(e => e.season));
      stats.episodeRange.minEpisode = Math.min(...allIMDbEpisodes.map(e => e.episode));
      stats.episodeRange.maxEpisode = Math.max(...allIMDbEpisodes.map(e => e.episode));
    }

    console.log(`📈 Episode Range: S${stats.episodeRange.minSeason}-S${stats.episodeRange.maxSeason}, E${stats.episodeRange.minEpisode}-E${stats.episodeRange.maxEpisode}`);

    // Process each episode from IMDb data (authoritative source)
    for (const imdbEpisode of allIMDbEpisodes) {
      try {
        const episodeKey = `S${imdbEpisode.season}E${imdbEpisode.episode}`;
        const existingEpisode = existingEpisodeMap.get(episodeKey);

        if (existingEpisode) {
          // Update existing episode with new data from IMDb and VidSrc
          let updated = false;

          // Update title if different
          if (existingEpisode.episodeTitle !== imdbEpisode.title) {
            existingEpisode.episodeTitle = imdbEpisode.title;
            updated = true;
          }

          // Update description if available
          if (imdbEpisode.description && existingEpisode.description !== imdbEpisode.description) {
            existingEpisode.description = imdbEpisode.description;
            updated = true;
          }

          // Update air date if available
          if (imdbEpisode.airDate && existingEpisode.airDate !== imdbEpisode.airDate) {
            existingEpisode.airDate = imdbEpisode.airDate;
            updated = true;
          }

          // Update IMDb rating if available and valid
          if (imdbEpisode.rating && !isNaN(imdbEpisode.rating) && existingEpisode.imdbRating !== imdbEpisode.rating) {
            existingEpisode.imdbRating = imdbEpisode.rating;
            updated = true;
          }

          // Update embed URLs if available
          if (imdbEpisode.embedUrl && existingEpisode.embedUrl !== imdbEpisode.embedUrl) {
            existingEpisode.embedUrl = imdbEpisode.embedUrl;
            existingEpisode.vidsrcUrl = imdbEpisode.embedUrl;
            updated = true;
          }

          if (imdbEpisode.embedUrlTmdb && existingEpisode.embedUrlTmdb !== imdbEpisode.embedUrlTmdb) {
            existingEpisode.embedUrlTmdb = imdbEpisode.embedUrlTmdb;
            existingEpisode.vidsrcTmdbUrl = imdbEpisode.embedUrlTmdb;
            updated = true;
          }

          if (updated) {
            existingEpisode.updatedAt = new Date();
            await existingEpisode.save();
            stats.existingEpisodesUpdated++;
            console.log(`🔄 Updated episode ${episodeKey} with IMDb data`);
          }

          continue;
        }

        // Create new episode with complete IMDb metadata and generated VidSrc embed URL
        const newEpisode = new Episode({
          seriesId: series._id,
          seriesTitle: series.title,
          imdbId: series.imdbId,
          season: imdbEpisode.season,
          episode: imdbEpisode.episode,
          episodeTitle: imdbEpisode.title,
          description: imdbEpisode.description || `${series.title} - Season ${imdbEpisode.season}, Episode ${imdbEpisode.episode}`,
          posterUrl: series.posterUrl, // Use series poster as fallback
          genres: series.genres || [], // Populate genres from series
          embedUrl: imdbEpisode.embedUrl,
          embedUrlTmdb: imdbEpisode.embedUrlTmdb || '',
          vidsrcUrl: imdbEpisode.embedUrl,
          vidsrcTmdbUrl: imdbEpisode.embedUrlTmdb || '',
          airDate: imdbEpisode.airDate || new Date().toISOString(),
          runtime: series.runtime || '45 min',
          imdbRating: imdbEpisode.rating && !isNaN(imdbEpisode.rating) ? imdbEpisode.rating : undefined,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        await newEpisode.save();
        stats.newEpisodesAdded++;

        console.log(`✅ Added new episode ${episodeKey} with IMDb data and VidSrc embed URL`);
      } catch (error) {
        const errorMsg = `Failed to process episode S${imdbEpisode.season}E${imdbEpisode.episode}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        stats.errors.push(errorMsg);
        console.error(`❌ ${errorMsg}`);
      }
    }

    // Advanced season analysis and gap detection
    const allEpisodesInDB = await Episode.find({
      $or: [
        { seriesId: series._id },
        { imdbId: series.imdbId }
      ]
    }).sort({ season: 1, episode: 1 });
    stats.totalEpisodesInDB = allEpisodesInDB.length;

    console.log(`📊 Final database query: Found ${allEpisodesInDB.length} episodes for series ${series.title}`);

    // ADVANCED: Build comprehensive season breakdown with intelligent gap analysis
    const seasonMap = new Map<number, number[]>();
    allEpisodesInDB.forEach(ep => {
      if (!seasonMap.has(ep.season)) {
        seasonMap.set(ep.season, []);
      }
      seasonMap.get(ep.season)!.push(ep.episode);
    });

    // Use IMDb data as the authoritative source for expected episodes
    const imdbSeasonMap = new Map<number, number>();
    if (imdbEpisodeData) {
      imdbEpisodeData.seasons.forEach(season => {
        imdbSeasonMap.set(season.seasonNumber, season.episodeCount);
      });
    }

    console.log(`🔍 ADVANCED: Analyzing ${imdbSeasonMap.size} seasons based on IMDb data...`);

    // Advanced gap analysis - ONLY analyze seasons that exist in IMDb data
    for (const [season, expectedEpisodes] of imdbSeasonMap) {
      episodes.sort((a, b) => a - b);
      const completionPercentage = Math.round((episodes.length / expectedEpisodes) * 100);
      console.log(`📊 Season ${season}: ${episodes.length}/${expectedEpisodes} episodes (${completionPercentage}% complete)`);

      // Find all missing episodes based on IMDb data
      const allMissingEpisodes: number[] = [];
      // Check against IMDb episode count
      for (let i = 1; i <= expectedEpisodes; i++) {
        if (!episodes.includes(i)) {
          allMissingEpisodes.push(i);
        }
      // Analyze gap severity based on IMDb reference
      let gapSeverity: 'minor' | 'major' | 'critical' = 'minor';
      const missingPercentage = (allMissingEpisodes.length / expectedEpisodes) * 100;
      if (missingPercentage > 50) {
        gapSeverity = 'critical'; // More than 50% missing
      } else if (missingPercentage > 20) {
        gapSeverity = 'major'; // More than 20% missing
      }

      stats.seasonBreakdown[season] = {
        episodeCount: episodes.length,
        episodes: episodes,
        missingEpisodes: allMissingEpisodes,
        hasGaps: allMissingEpisodes.length > 0,
        completionPercentage: completionPercentage,
        expectedEpisodes: expectedEpisodes
      };

      // Log detailed analysis
      if (allMissingEpisodes.length > 0) {
        console.log(`⚠️ Season ${season} Analysis:`);
        console.log(`   📊 Episodes Found: ${episodes.length} (${episodes.join(', ')})`);
        console.log(`   ❌ Missing Episodes: ${allMissingEpisodes.length} (${allMissingEpisodes.join(', ')})`);
        console.log(`   📈 Completion: ${completionPercentage}%${expectedEpisodes ? ` of estimated ${expectedEpisodes} episodes` : ''}`);
        console.log(`   🚨 Severity: ${gapSeverity.toUpperCase()}`);

        // Add to suspicious gaps
        if (allMissingEpisodes.length > 0) {
          stats.verificationResults.suspiciousGaps.push({
            season: season,
            missingRange: allMissingEpisodes.length > 5
              ? `${allMissingEpisodes[0]}-${allMissingEpisodes[allMissingEpisodes.length - 1]} (${allMissingEpisodes.length} episodes)`
              : allMissingEpisodes.join(', '),
            severity: gapSeverity
          });
        }
      } else {
        console.log(`✅ Season ${season}: Complete (${episodes.length} episodes: ${episodes.join(', ')})`);
      }
    }

    // Check for missing season ranges
    const seasonNumbers = Array.from(seasonMap.keys()).sort((a, b) => a - b);
    const missingSeasonRanges: string[] = [];

    if (seasonNumbers.length > 1) {
      const minSeason = seasonNumbers[0];
      const maxSeason = seasonNumbers[seasonNumbers.length - 1];

      for (let s = minSeason; s <= maxSeason; s++) {
        if (!seasonNumbers.includes(s)) {
          missingSeasonRanges.push(`Season ${s}`);
        }
      }
    }

    // Calculate overall verification results
    const totalSeasonsWithGaps = Object.values(stats.seasonBreakdown).filter(s => s.hasGaps).length;
    const totalEpisodesExpected = Object.values(stats.seasonBreakdown)
      .reduce((sum, s) => sum + (s.expectedEpisodes || s.episodeCount), 0);
    const overallCompletionPercentage = totalEpisodesExpected > 0
      ? Math.round((stats.totalEpisodesInDB / totalEpisodesExpected) * 100)
      : 100;

    stats.verificationResults = {
      totalSeasonsFound: seasonMap.size,
      totalSeasonsWithGaps: totalSeasonsWithGaps,
      overallCompletionPercentage: overallCompletionPercentage,
      missingSeasonRanges: missingSeasonRanges,
      suspiciousGaps: stats.verificationResults.suspiciousGaps
    };

    // Update series total episodes count
    if (series.totalEpisodes !== stats.totalEpisodesInDB) {
      series.totalEpisodes = stats.totalEpisodesInDB;
      series.updatedAt = new Date();
      await series.save();
      console.log(`📊 Updated series total episodes: ${series.totalEpisodes} → ${stats.totalEpisodesInDB}`);
    }

    // ADVANCED: IMDb-based verification summary
    console.log(`🎯 IMDB-BASED EPISODE VERIFICATION COMPLETE:`);
    console.log(`   🎬 IMDb Episodes Found: ${imdbEpisodeData.totalEpisodes} across ${imdbEpisodeData.totalSeasons} seasons`);
    console.log(`   📺 Episodes in Database: ${stats.totalEpisodesInDB}`);
    console.log(`   🔗 All Episodes have Generated VidSrc URLs: YES`);
    console.log(`   ➕ New Episodes Added: ${stats.newEpisodesAdded}`);
    console.log(`   🔄 Existing Episodes Updated: ${stats.existingEpisodesUpdated}`);
    console.log(`   🗂️ Total Seasons Found: ${stats.verificationResults.totalSeasonsFound}`);
    console.log(`   ⚠️ Seasons with Gaps: ${stats.verificationResults.totalSeasonsWithGaps}`);
    console.log(`   📊 Overall Completion: ${stats.verificationResults.overallCompletionPercentage}%`);
    console.log(`   ❌ Processing Errors: ${stats.errors.length}`);

    if (stats.verificationResults.missingSeasonRanges.length > 0) {
      console.log(`   🚨 Missing Seasons: ${stats.verificationResults.missingSeasonRanges.join(', ')}`);
    }

    if (stats.verificationResults.suspiciousGaps.length > 0) {
      console.log(`   🔍 Suspicious Gaps Detected:`);
      stats.verificationResults.suspiciousGaps.forEach(gap => {
        console.log(`      Season ${gap.season}: ${gap.missingRange} (${gap.severity.toUpperCase()})`);
      });
    }

    // Quality assessment
    let qualityAssessment = 'EXCELLENT';
    if (stats.verificationResults.overallCompletionPercentage < 90) qualityAssessment = 'GOOD';
    if (stats.verificationResults.overallCompletionPercentage < 70) qualityAssessment = 'FAIR';
    if (stats.verificationResults.overallCompletionPercentage < 50) qualityAssessment = 'POOR';
    if (stats.verificationResults.suspiciousGaps.some(g => g.severity === 'critical')) qualityAssessment = 'CRITICAL';

    console.log(`   🏆 Episode Collection Quality: ${qualityAssessment}`);

    return NextResponse.json({
      message: 'Advanced episode analysis completed successfully',
      stats: stats,
      episodes: allEpisodesInDB,
      lastChecked: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Advanced episode analysis failed:', error);
    return NextResponse.json(
      { error: 'Failed to perform advanced episode analysis' },
      { status: 500 }
    );
  }
}
