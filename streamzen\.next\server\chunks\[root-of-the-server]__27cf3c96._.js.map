{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Movie.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IMovie extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string; // MPAA rating (R, PG-13, etc.)\n  runtime?: string; // e.g., \"2h 22m\"\n  imdbRating?: number;\n  imdbVotes?: string; // e.g., \"3.1M\"\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst MovieSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: { \n    type: String, \n    required: true,\n    index: true \n  },\n  year: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  rating: String,\n  runtime: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  trailerRuntime: String,\n  trailerLikes: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  director: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  }\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nMovieSchema.index({ year: -1, imdbRating: -1 });\nMovieSchema.index({ genres: 1, year: -1 });\n// Removed text index to avoid language override issues\nMovieSchema.index({ title: 1 });\nMovieSchema.index({ language: 1, country: 1 });\n\nexport default mongoose.models.Movie || mongoose.model<IMovie>('Movie', MovieSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAgCA,MAAM,cAAsB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACrC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;IACR,SAAS;IACT,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,UAAU;IACV,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;AACF,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,YAAY,KAAK,CAAC;IAAE,MAAM,CAAC;IAAG,YAAY,CAAC;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,MAAM,CAAC;AAAE;AACxC,uDAAuD;AACvD,YAAY,KAAK,CAAC;IAAE,OAAO;AAAE;AAC7B,YAAY,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;uCAE7B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAS,SAAS", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Series.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface ISeries extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string; // MPAA rating\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string; // 'ongoing', 'ended', 'cancelled'\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst SeriesSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: { \n    type: String, \n    required: true,\n    index: true \n  },\n  startYear: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  endYear: { \n    type: Number,\n    index: true \n  },\n  rating: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  creator: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  totalSeasons: Number,\n  status: { \n    type: String,\n    enum: ['ongoing', 'ended', 'cancelled'],\n    index: true \n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String // VidSrc TMDB embed URL\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nSeriesSchema.index({ startYear: -1, imdbRating: -1 });\nSeriesSchema.index({ genres: 1, startYear: -1 });\nSeriesSchema.index({ status: 1, startYear: -1 });\n// Removed text index to avoid language override issues\nSeriesSchema.index({ title: 1 });\nSeriesSchema.index({ language: 1, country: 1 });\n\nexport default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA+BA,MAAM,eAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;IACR,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,SAAS;IACT,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,cAAc;IACd,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAS;SAAY;QACvC,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe,OAAO,wBAAwB;AAChD,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,aAAa,KAAK,CAAC;IAAE,WAAW,CAAC;IAAG,YAAY,CAAC;AAAE;AACnD,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,uDAAuD;AACvD,aAAa,KAAK,CAAC;IAAE,OAAO;AAAE;AAC9B,aAAa,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;uCAE9B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Episode.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IEpisode extends Document {\n  imdbId: string; // Series IMDb ID\n  tmdbId?: string; // Series TMDB ID\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  genres?: string[]; // Genres inherited from series\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst EpisodeSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  seriesTitle: { \n    type: String, \n    required: true,\n    index: true \n  },\n  season: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episode: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episodeTitle: String,\n  airDate: { \n    type: Date,\n    index: true \n  },\n  runtime: String,\n  imdbRating: Number,\n  description: String,\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  },\n  genres: [{\n    type: String,\n    index: true\n  }]\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nEpisodeSchema.index({ imdbId: 1, season: 1, episode: 1 }, { unique: true });\nEpisodeSchema.index({ airDate: -1 });\nEpisodeSchema.index({ seriesTitle: 1, season: 1, episode: 1 });\nEpisodeSchema.index({ createdAt: -1 }); // For latest episodes\n\nexport default mongoose.models.Episode || mongoose.model<IEpisode>('Episode', EpisodeSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAuBA,MAAM,gBAAwB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACvC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,cAAc;IACd,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,SAAS;IACT,YAAY;IACZ,aAAa;IACb,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;AACJ,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,cAAc,KAAK,CAAC;IAAE,QAAQ;IAAG,QAAQ;IAAG,SAAS;AAAE,GAAG;IAAE,QAAQ;AAAK;AACzE,cAAc,KAAK,CAAC;IAAE,SAAS,CAAC;AAAE;AAClC,cAAc,KAAK,CAAC;IAAE,aAAa;IAAG,QAAQ;IAAG,SAAS;AAAE;AAC5D,cAAc,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE,IAAI,sBAAsB;uCAE/C,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAW,WAAW", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/search/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Movie from '@/models/Movie';\nimport Series from '@/models/Series';\nimport Episode from '@/models/Episode';\n\nexport async function GET(request: NextRequest) {\n  try {\n    await connectDB();\n    \n    const { searchParams } = new URL(request.url);\n    const query = searchParams.get('q');\n    const type = searchParams.get('type') || 'all'; // all, movies, series, episodes\n    const limit = parseInt(searchParams.get('limit') || '20');\n    const page = parseInt(searchParams.get('page') || '1');\n    \n    if (!query || query.trim().length < 2) {\n      return NextResponse.json({\n        results: [],\n        pagination: {\n          page: 1,\n          limit,\n          total: 0,\n          pages: 0\n        }\n      });\n    }\n\n    const searchQuery = query.trim();\n    const skip = (page - 1) * limit;\n    \n    // Create search regex for flexible matching\n    const searchRegex = new RegExp(searchQuery.split(' ').join('|'), 'i');\n    const exactRegex = new RegExp(searchQuery, 'i');\n    \n    let results: any[] = [];\n    let totalCount = 0;\n\n    if (type === 'all' || type === 'movies') {\n      // Search movies with scoring\n      const moviePipeline = [\n        {\n          $match: {\n            $or: [\n              { title: exactRegex },\n              { description: exactRegex },\n              { genres: { $in: [exactRegex] } },\n              { cast: { $in: [exactRegex] } },\n              { director: exactRegex },\n              { title: searchRegex },\n              { description: searchRegex }\n            ]\n          }\n        },\n        {\n          $addFields: {\n            score: {\n              $add: [\n                // Exact title match gets highest score\n                { $cond: [{ $regexMatch: { input: \"$title\", regex: exactRegex } }, 100, 0] },\n                // Title contains search gets high score\n                { $cond: [{ $regexMatch: { input: \"$title\", regex: searchRegex } }, 50, 0] },\n                // Genre match gets medium score\n                { $cond: [{ $in: [searchRegex, \"$genres\"] }, 30, 0] },\n                // Cast match gets medium score\n                { $cond: [{ $in: [searchRegex, \"$cast\"] }, 25, 0] },\n                // Director match gets medium score\n                { $cond: [{ $regexMatch: { input: \"$director\", regex: exactRegex } }, 20, 0] },\n                // Description match gets low score\n                { $cond: [{ $regexMatch: { input: \"$description\", regex: searchRegex } }, 10, 0] },\n                // IMDb rating bonus (only for numeric ratings)\n                { $multiply: [\n                  { $cond: [\n                    { $regexMatch: { input: { $toString: \"$rating\" }, regex: \"^[0-9]+(\\\\.[0-9]+)?$\" } },\n                    { $toDouble: \"$rating\" },\n                    0\n                  ]},\n                  2\n                ]}\n              ]\n            },\n            type: { $literal: \"movie\" }\n          }\n        },\n        { $sort: { score: -1, rating: -1, createdAt: -1 } }\n      ];\n\n      if (type === 'movies') {\n        const movieCount = await Movie.aggregate([\n          ...moviePipeline,\n          { $count: \"total\" }\n        ]);\n        totalCount = movieCount[0]?.total || 0;\n        \n        const movies = await Movie.aggregate([\n          ...moviePipeline,\n          { $skip: skip },\n          { $limit: limit }\n        ]);\n        results = movies;\n      } else {\n        const movies = await Movie.aggregate([\n          ...moviePipeline,\n          { $limit: Math.ceil(limit / 3) }\n        ]);\n        results.push(...movies);\n      }\n    }\n\n    if (type === 'all' || type === 'series') {\n      // Search series with scoring\n      const seriesPipeline = [\n        {\n          $match: {\n            $or: [\n              { title: exactRegex },\n              { description: exactRegex },\n              { genres: { $in: [exactRegex] } },\n              { cast: { $in: [exactRegex] } },\n              { title: searchRegex },\n              { description: searchRegex }\n            ]\n          }\n        },\n        {\n          $addFields: {\n            score: {\n              $add: [\n                // Exact title match gets highest score\n                { $cond: [{ $regexMatch: { input: \"$title\", regex: exactRegex } }, 100, 0] },\n                // Title contains search gets high score\n                { $cond: [{ $regexMatch: { input: \"$title\", regex: searchRegex } }, 50, 0] },\n                // Genre match gets medium score\n                { $cond: [{ $in: [searchRegex, \"$genres\"] }, 30, 0] },\n                // Cast match gets medium score\n                { $cond: [{ $in: [searchRegex, \"$cast\"] }, 25, 0] },\n                // Description match gets low score\n                { $cond: [{ $regexMatch: { input: \"$description\", regex: searchRegex } }, 10, 0] },\n                // IMDb rating bonus (only for numeric ratings)\n                { $multiply: [\n                  { $cond: [\n                    { $regexMatch: { input: { $toString: \"$rating\" }, regex: \"^[0-9]+(\\\\.[0-9]+)?$\" } },\n                    { $toDouble: \"$rating\" },\n                    0\n                  ]},\n                  2\n                ]},\n                // Total seasons bonus\n                { $multiply: [{ $toInt: { $ifNull: [\"$totalSeasons\", 1] } }, 1] }\n              ]\n            },\n            type: { $literal: \"series\" }\n          }\n        },\n        { $sort: { score: -1, rating: -1, createdAt: -1 } }\n      ];\n\n      if (type === 'series') {\n        const seriesCount = await Series.aggregate([\n          ...seriesPipeline,\n          { $count: \"total\" }\n        ]);\n        totalCount = seriesCount[0]?.total || 0;\n        \n        const series = await Series.aggregate([\n          ...seriesPipeline,\n          { $skip: skip },\n          { $limit: limit }\n        ]);\n        results = series;\n      } else {\n        const series = await Series.aggregate([\n          ...seriesPipeline,\n          { $limit: Math.ceil(limit / 3) }\n        ]);\n        results.push(...series);\n      }\n    }\n\n    if (type === 'all' || type === 'episodes') {\n      // Search episodes with scoring\n      const episodePipeline = [\n        {\n          $match: {\n            $or: [\n              { title: exactRegex },\n              { description: exactRegex },\n              { title: searchRegex },\n              { description: searchRegex }\n            ]\n          }\n        },\n        {\n          $lookup: {\n            from: 'series',\n            localField: 'seriesImdbId',\n            foreignField: 'imdbId',\n            as: 'series'\n          }\n        },\n        {\n          $addFields: {\n            score: {\n              $add: [\n                // Exact title match gets highest score\n                { $cond: [{ $regexMatch: { input: \"$title\", regex: exactRegex } }, 100, 0] },\n                // Title contains search gets high score\n                { $cond: [{ $regexMatch: { input: \"$title\", regex: searchRegex } }, 50, 0] },\n                // Description match gets medium score\n                { $cond: [{ $regexMatch: { input: \"$description\", regex: searchRegex } }, 20, 0] },\n                // Episode rating bonus (only for numeric ratings)\n                { $multiply: [\n                  { $cond: [\n                    { $regexMatch: { input: { $toString: \"$rating\" }, regex: \"^[0-9]+(\\\\.[0-9]+)?$\" } },\n                    { $toDouble: \"$rating\" },\n                    0\n                  ]},\n                  2\n                ]},\n                // Recent episodes get bonus\n                { $cond: [{ $gte: [\"$createdAt\", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)] }, 10, 0] }\n              ]\n            },\n            type: { $literal: \"episode\" },\n            seriesTitle: { $arrayElemAt: [\"$series.title\", 0] },\n            seriesPoster: { $arrayElemAt: [\"$series.posterUrl\", 0] }\n          }\n        },\n        { $sort: { score: -1, rating: -1, createdAt: -1 } }\n      ];\n\n      if (type === 'episodes') {\n        const episodeCount = await Episode.aggregate([\n          ...episodePipeline,\n          { $count: \"total\" }\n        ]);\n        totalCount = episodeCount[0]?.total || 0;\n        \n        const episodes = await Episode.aggregate([\n          ...episodePipeline,\n          { $skip: skip },\n          { $limit: limit }\n        ]);\n        results = episodes;\n      } else {\n        const episodes = await Episode.aggregate([\n          ...episodePipeline,\n          { $limit: Math.ceil(limit / 3) }\n        ]);\n        results.push(...episodes);\n      }\n    }\n\n    // For 'all' type, sort combined results by score\n    if (type === 'all') {\n      results.sort((a, b) => {\n        if (b.score !== a.score) return b.score - a.score;\n        if (b.rating !== a.rating) return (b.rating || 0) - (a.rating || 0);\n        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();\n      });\n      \n      totalCount = results.length;\n      results = results.slice(skip, skip + limit);\n    }\n\n    const totalPages = Math.ceil(totalCount / limit);\n\n    return NextResponse.json({\n      results,\n      pagination: {\n        page,\n        limit,\n        total: totalCount,\n        pages: totalPages\n      },\n      query: searchQuery\n    });\n\n  } catch (error) {\n    console.error('Search error:', error);\n    return NextResponse.json(\n      { error: 'Search failed' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,OAAO,aAAa,GAAG,CAAC,WAAW,OAAO,gCAAgC;QAChF,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAElD,IAAI,CAAC,SAAS,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS,EAAE;gBACX,YAAY;oBACV,MAAM;oBACN;oBACA,OAAO;oBACP,OAAO;gBACT;YACF;QACF;QAEA,MAAM,cAAc,MAAM,IAAI;QAC9B,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,4CAA4C;QAC5C,MAAM,cAAc,IAAI,OAAO,YAAY,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM;QACjE,MAAM,aAAa,IAAI,OAAO,aAAa;QAE3C,IAAI,UAAiB,EAAE;QACvB,IAAI,aAAa;QAEjB,IAAI,SAAS,SAAS,SAAS,UAAU;YACvC,6BAA6B;YAC7B,MAAM,gBAAgB;gBACpB;oBACE,QAAQ;wBACN,KAAK;4BACH;gCAAE,OAAO;4BAAW;4BACpB;gCAAE,aAAa;4BAAW;4BAC1B;gCAAE,QAAQ;oCAAE,KAAK;wCAAC;qCAAW;gCAAC;4BAAE;4BAChC;gCAAE,MAAM;oCAAE,KAAK;wCAAC;qCAAW;gCAAC;4BAAE;4BAC9B;gCAAE,UAAU;4BAAW;4BACvB;gCAAE,OAAO;4BAAY;4BACrB;gCAAE,aAAa;4BAAY;yBAC5B;oBACH;gBACF;gBACA;oBACE,YAAY;wBACV,OAAO;4BACL,MAAM;gCACJ,uCAAuC;gCACvC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAU,OAAO;4CAAW;wCAAE;wCAAG;wCAAK;qCAAE;gCAAC;gCAC3E,wCAAwC;gCACxC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAU,OAAO;4CAAY;wCAAE;wCAAG;wCAAI;qCAAE;gCAAC;gCAC3E,gCAAgC;gCAChC;oCAAE,OAAO;wCAAC;4CAAE,KAAK;gDAAC;gDAAa;6CAAU;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACpD,+BAA+B;gCAC/B;oCAAE,OAAO;wCAAC;4CAAE,KAAK;gDAAC;gDAAa;6CAAQ;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCAClD,mCAAmC;gCACnC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAa,OAAO;4CAAW;wCAAE;wCAAG;wCAAI;qCAAE;gCAAC;gCAC7E,mCAAmC;gCACnC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAgB,OAAO;4CAAY;wCAAE;wCAAG;wCAAI;qCAAE;gCAAC;gCACjF,+CAA+C;gCAC/C;oCAAE,WAAW;wCACX;4CAAE,OAAO;gDACP;oDAAE,aAAa;wDAAE,OAAO;4DAAE,WAAW;wDAAU;wDAAG,OAAO;oDAAuB;gDAAE;gDAClF;oDAAE,WAAW;gDAAU;gDACvB;6CACD;wCAAA;wCACD;qCACD;gCAAA;6BACF;wBACH;wBACA,MAAM;4BAAE,UAAU;wBAAQ;oBAC5B;gBACF;gBACA;oBAAE,OAAO;wBAAE,OAAO,CAAC;wBAAG,QAAQ,CAAC;wBAAG,WAAW,CAAC;oBAAE;gBAAE;aACnD;YAED,IAAI,SAAS,UAAU;gBACrB,MAAM,aAAa,MAAM,wHAAA,CAAA,UAAK,CAAC,SAAS,CAAC;uBACpC;oBACH;wBAAE,QAAQ;oBAAQ;iBACnB;gBACD,aAAa,UAAU,CAAC,EAAE,EAAE,SAAS;gBAErC,MAAM,SAAS,MAAM,wHAAA,CAAA,UAAK,CAAC,SAAS,CAAC;uBAChC;oBACH;wBAAE,OAAO;oBAAK;oBACd;wBAAE,QAAQ;oBAAM;iBACjB;gBACD,UAAU;YACZ,OAAO;gBACL,MAAM,SAAS,MAAM,wHAAA,CAAA,UAAK,CAAC,SAAS,CAAC;uBAChC;oBACH;wBAAE,QAAQ,KAAK,IAAI,CAAC,QAAQ;oBAAG;iBAChC;gBACD,QAAQ,IAAI,IAAI;YAClB;QACF;QAEA,IAAI,SAAS,SAAS,SAAS,UAAU;YACvC,6BAA6B;YAC7B,MAAM,iBAAiB;gBACrB;oBACE,QAAQ;wBACN,KAAK;4BACH;gCAAE,OAAO;4BAAW;4BACpB;gCAAE,aAAa;4BAAW;4BAC1B;gCAAE,QAAQ;oCAAE,KAAK;wCAAC;qCAAW;gCAAC;4BAAE;4BAChC;gCAAE,MAAM;oCAAE,KAAK;wCAAC;qCAAW;gCAAC;4BAAE;4BAC9B;gCAAE,OAAO;4BAAY;4BACrB;gCAAE,aAAa;4BAAY;yBAC5B;oBACH;gBACF;gBACA;oBACE,YAAY;wBACV,OAAO;4BACL,MAAM;gCACJ,uCAAuC;gCACvC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAU,OAAO;4CAAW;wCAAE;wCAAG;wCAAK;qCAAE;gCAAC;gCAC3E,wCAAwC;gCACxC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAU,OAAO;4CAAY;wCAAE;wCAAG;wCAAI;qCAAE;gCAAC;gCAC3E,gCAAgC;gCAChC;oCAAE,OAAO;wCAAC;4CAAE,KAAK;gDAAC;gDAAa;6CAAU;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCACpD,+BAA+B;gCAC/B;oCAAE,OAAO;wCAAC;4CAAE,KAAK;gDAAC;gDAAa;6CAAQ;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;gCAClD,mCAAmC;gCACnC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAgB,OAAO;4CAAY;wCAAE;wCAAG;wCAAI;qCAAE;gCAAC;gCACjF,+CAA+C;gCAC/C;oCAAE,WAAW;wCACX;4CAAE,OAAO;gDACP;oDAAE,aAAa;wDAAE,OAAO;4DAAE,WAAW;wDAAU;wDAAG,OAAO;oDAAuB;gDAAE;gDAClF;oDAAE,WAAW;gDAAU;gDACvB;6CACD;wCAAA;wCACD;qCACD;gCAAA;gCACD,sBAAsB;gCACtB;oCAAE,WAAW;wCAAC;4CAAE,QAAQ;gDAAE,SAAS;oDAAC;oDAAiB;iDAAE;4CAAC;wCAAE;wCAAG;qCAAE;gCAAC;6BACjE;wBACH;wBACA,MAAM;4BAAE,UAAU;wBAAS;oBAC7B;gBACF;gBACA;oBAAE,OAAO;wBAAE,OAAO,CAAC;wBAAG,QAAQ,CAAC;wBAAG,WAAW,CAAC;oBAAE;gBAAE;aACnD;YAED,IAAI,SAAS,UAAU;gBACrB,MAAM,cAAc,MAAM,yHAAA,CAAA,UAAM,CAAC,SAAS,CAAC;uBACtC;oBACH;wBAAE,QAAQ;oBAAQ;iBACnB;gBACD,aAAa,WAAW,CAAC,EAAE,EAAE,SAAS;gBAEtC,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,SAAS,CAAC;uBACjC;oBACH;wBAAE,OAAO;oBAAK;oBACd;wBAAE,QAAQ;oBAAM;iBACjB;gBACD,UAAU;YACZ,OAAO;gBACL,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,SAAS,CAAC;uBACjC;oBACH;wBAAE,QAAQ,KAAK,IAAI,CAAC,QAAQ;oBAAG;iBAChC;gBACD,QAAQ,IAAI,IAAI;YAClB;QACF;QAEA,IAAI,SAAS,SAAS,SAAS,YAAY;YACzC,+BAA+B;YAC/B,MAAM,kBAAkB;gBACtB;oBACE,QAAQ;wBACN,KAAK;4BACH;gCAAE,OAAO;4BAAW;4BACpB;gCAAE,aAAa;4BAAW;4BAC1B;gCAAE,OAAO;4BAAY;4BACrB;gCAAE,aAAa;4BAAY;yBAC5B;oBACH;gBACF;gBACA;oBACE,SAAS;wBACP,MAAM;wBACN,YAAY;wBACZ,cAAc;wBACd,IAAI;oBACN;gBACF;gBACA;oBACE,YAAY;wBACV,OAAO;4BACL,MAAM;gCACJ,uCAAuC;gCACvC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAU,OAAO;4CAAW;wCAAE;wCAAG;wCAAK;qCAAE;gCAAC;gCAC3E,wCAAwC;gCACxC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAU,OAAO;4CAAY;wCAAE;wCAAG;wCAAI;qCAAE;gCAAC;gCAC3E,sCAAsC;gCACtC;oCAAE,OAAO;wCAAC;4CAAE,aAAa;gDAAE,OAAO;gDAAgB,OAAO;4CAAY;wCAAE;wCAAG;wCAAI;qCAAE;gCAAC;gCACjF,kDAAkD;gCAClD;oCAAE,WAAW;wCACX;4CAAE,OAAO;gDACP;oDAAE,aAAa;wDAAE,OAAO;4DAAE,WAAW;wDAAU;wDAAG,OAAO;oDAAuB;gDAAE;gDAClF;oDAAE,WAAW;gDAAU;gDACvB;6CACD;wCAAA;wCACD;qCACD;gCAAA;gCACD,4BAA4B;gCAC5B;oCAAE,OAAO;wCAAC;4CAAE,MAAM;gDAAC;gDAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;6CAAM;wCAAC;wCAAG;wCAAI;qCAAE;gCAAC;6BAC7F;wBACH;wBACA,MAAM;4BAAE,UAAU;wBAAU;wBAC5B,aAAa;4BAAE,cAAc;gCAAC;gCAAiB;6BAAE;wBAAC;wBAClD,cAAc;4BAAE,cAAc;gCAAC;gCAAqB;6BAAE;wBAAC;oBACzD;gBACF;gBACA;oBAAE,OAAO;wBAAE,OAAO,CAAC;wBAAG,QAAQ,CAAC;wBAAG,WAAW,CAAC;oBAAE;gBAAE;aACnD;YAED,IAAI,SAAS,YAAY;gBACvB,MAAM,eAAe,MAAM,0HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;uBACxC;oBACH;wBAAE,QAAQ;oBAAQ;iBACnB;gBACD,aAAa,YAAY,CAAC,EAAE,EAAE,SAAS;gBAEvC,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;uBACpC;oBACH;wBAAE,OAAO;oBAAK;oBACd;wBAAE,QAAQ;oBAAM;iBACjB;gBACD,UAAU;YACZ,OAAO;gBACL,MAAM,WAAW,MAAM,0HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;uBACpC;oBACH;wBAAE,QAAQ,KAAK,IAAI,CAAC,QAAQ;oBAAG;iBAChC;gBACD,QAAQ,IAAI,IAAI;YAClB;QACF;QAEA,iDAAiD;QACjD,IAAI,SAAS,OAAO;YAClB,QAAQ,IAAI,CAAC,CAAC,GAAG;gBACf,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;gBACjD,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC;gBAClE,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACxE;YAEA,aAAa,QAAQ,MAAM;YAC3B,UAAU,QAAQ,KAAK,CAAC,MAAM,OAAO;QACvC;QAEA,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa;QAE1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA,OAAO;gBACP,OAAO;YACT;YACA,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAgB,GACzB;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}