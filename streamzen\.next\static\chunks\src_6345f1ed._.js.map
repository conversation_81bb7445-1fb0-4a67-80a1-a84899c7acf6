{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatYear(year: number): string {\n  return year.toString();\n}\n\nexport function formatRating(rating: number): string {\n  return rating.toFixed(1);\n}\n\nexport function formatRuntime(runtime: string): string {\n  return runtime;\n}\n\nexport function getImageUrl(url: string | undefined, fallback: string = '/placeholder-poster.jpg'): string {\n  if (!url) return fallback;\n  \n  // If it's already a full URL, return as is\n  if (url.startsWith('http')) return url;\n  \n  // If it's a relative path, make it absolute\n  return url.startsWith('/') ? url : `/${url}`;\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).trim() + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAY;IACrC,OAAO,KAAK,QAAQ;AACtB;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,OAAO,OAAO,CAAC;AACxB;AAEO,SAAS,cAAc,OAAe;IAC3C,OAAO;AACT;AAEO,SAAS,YAAY,GAAuB,EAAE,WAAmB,yBAAyB;IAC/F,IAAI,CAAC,KAAK,OAAO;IAEjB,2CAA2C;IAC3C,IAAI,IAAI,UAAU,CAAC,SAAS,OAAO;IAEnC,4CAA4C;IAC5C,OAAO,IAAI,UAAU,CAAC,OAAO,MAAM,CAAC,CAAC,EAAE,KAAK;AAC9C;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AAC/C", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Search, Home, Film, Tv, Calendar, Plus, Play, X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport SearchBar from './SearchBar';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Home },\n    { href: '/movies', label: 'Movies', icon: Film },\n    { href: '/series', label: 'Series', icon: Tv },\n    { href: '/episodes', label: 'Episodes', icon: Calendar },\n  ];\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 glass border-b border-white/5 backdrop-blur-xl\">\n      <div className=\"max-w-[1920px] mx-auto px-8 lg:px-16\">\n        <div className=\"flex items-center justify-between h-24\">\n          {/* Apple TV + Netflix Style Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-4 focus-ring rounded-2xl px-3 py-2 transition-all duration-300 hover:bg-white/5\">\n            <div className=\"relative group\">\n              <div className=\"w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-2xl transition-all duration-300 group-hover:scale-110\">\n                <Play size={24} className=\"text-black fill-current ml-1\" />\n              </div>\n              <div className=\"absolute -inset-2 bg-white/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-300\" />\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-white text-3xl font-black tracking-tight\">\n                Stream<span className=\"text-red-600\">Zen</span>\n              </span>\n              <span className=\"text-gray-400 text-sm font-medium -mt-1 tracking-wide\">\n                Premium Entertainment\n              </span>\n            </div>\n          </Link>\n\n          {/* Ultra Premium Navigation Links */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={cn(\n                    'relative flex items-center space-x-3 px-8 py-4 rounded-2xl transition-all duration-300 focus-ring group',\n                    isActive\n                      ? 'bg-gradient-to-r from-gray-800/40 to-gray-700/40 text-white shadow-2xl border border-white/15 backdrop-blur-sm'\n                      : 'text-gray-300 hover:text-white hover:bg-white/10'\n                  )}\n                >\n                  <Icon size={22} className={cn(\n                    'transition-all duration-300',\n                    isActive ? 'text-gray-300' : 'group-hover:text-white'\n                  )} />\n                  <span className=\"font-semibold text-lg\">{item.label}</span>\n                  {isActive && (\n                    <div className=\"absolute -inset-1 bg-gradient-to-r from-gray-600/20 to-gray-500/20 rounded-2xl blur opacity-75\" />\n                  )}\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Ultra Premium Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Premium Search Button */}\n            <button\n              onClick={() => setIsSearchOpen(!isSearchOpen)}\n              className=\"p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group\"\n            >\n              {isSearchOpen ? (\n                <X size={24} className=\"group-hover:scale-110 transition-transform duration-300\" />\n              ) : (\n                <Search size={24} className=\"group-hover:scale-110 transition-transform duration-300\" />\n              )}\n            </button>\n\n            {/* Apple TV + Netflix Style Request Button */}\n            <Link\n              href=\"/request\"\n              className=\"hidden sm:flex items-center space-x-3 px-8 py-4 bg-red-600 rounded-2xl text-white hover:bg-red-700 transition-all duration-300 focus-ring font-semibold shadow-2xl hover:scale-105\"\n            >\n              <Plus size={20} />\n              <span className=\"text-lg\">Request</span>\n            </Link>\n\n            {/* Premium Mobile Menu Button */}\n            <button className=\"lg:hidden p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group\">\n              <div className=\"w-6 h-6 flex flex-col justify-center space-y-1.5\">\n                <div className=\"w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4\" />\n                <div className=\"w-full h-0.5 bg-current rounded\" />\n                <div className=\"w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4 group-hover:ml-auto\" />\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Premium Mobile Navigation */}\n      <div className=\"lg:hidden border-t border-white/5 glass\">\n        <div className=\"flex items-center justify-around py-4 px-2\">\n          {navItems.map((item) => {\n            const Icon = item.icon;\n            const isActive = pathname === item.href;\n\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  'flex flex-col items-center space-y-2 px-4 py-3 rounded-xl transition-all duration-300 focus-ring',\n                  isActive\n                    ? 'text-white bg-white/15'\n                    : 'text-gray-400 hover:text-white hover:bg-white/10'\n                )}\n              >\n                <Icon size={20} />\n                <span className=\"text-xs font-medium\">{item.label}</span>\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AASA,MAAM,aAAuB;;IAC3B,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,sMAAA,CAAA,OAAI;QAAC;QACvC;YAAE,MAAM;YAAW,OAAO;YAAU,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC/C;YAAE,MAAM;YAAW,OAAO;YAAU,MAAM,iMAAA,CAAA,KAAE;QAAC;QAC7C;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACxD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE5B,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAAgD;8DACxD,6LAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEvC,6LAAC;4CAAK,WAAU;sDAAwD;;;;;;;;;;;;;;;;;;sCAO5E,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2GACA,WACI,mHACA;;sDAGN,6LAAC;4CAAK,MAAM;4CAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAC1B,+BACA,WAAW,kBAAkB;;;;;;sDAE/B,6LAAC;4CAAK,WAAU;sDAAyB,KAAK,KAAK;;;;;;wCAClD,0BACC,6LAAC;4CAAI,WAAU;;;;;;;mCAfZ,KAAK,IAAI;;;;;4BAmBpB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;8CAET,6BACC,6LAAC,+LAAA,CAAA,IAAC;wCAAC,MAAM;wCAAI,WAAU;;;;;6DAEvB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAKhC,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAI5B,6LAAC;oCAAO,WAAU;8CAChB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oGACA,WACI,2BACA;;8CAGN,6LAAC;oCAAK,MAAM;;;;;;8CACZ,6LAAC;oCAAK,WAAU;8CAAuB,KAAK,KAAK;;;;;;;2BAV5C,KAAK,IAAI;;;;;oBAapB;;;;;;;;;;;;;;;;;AAKV;GA7HM;;QACa,qIAAA,CAAA,cAAW;;;KADxB;uCA+HS", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Github, Twitter, Instagram, Youtube, Mail, Heart, Zap, Play } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    platform: [\n      { label: 'Movies', href: '/movies' },\n      { label: 'TV Series', href: '/series' },\n      { label: 'Episodes', href: '/episodes' },\n      { label: 'Request Content', href: '/request' }\n    ],\n    company: [\n      { label: 'About Us', href: '/about' },\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Contact', href: '/contact' }\n    ],\n    support: [\n      { label: 'Help Center', href: '/help' },\n      { label: 'Community', href: '/community' },\n      { label: 'API Documentation', href: '/api-docs' },\n      { label: 'Status', href: '/status' }\n    ]\n  };\n\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com', label: 'GitHub' },\n    { icon: Twitter, href: 'https://twitter.com', label: 'Twitter' },\n    { icon: Instagram, href: 'https://instagram.com', label: 'Instagram' },\n    { icon: Youtube, href: 'https://youtube.com', label: 'YouTube' }\n  ];\n\n  return (\n    <footer className=\"relative bg-black border-t border-white/5\">\n      {/* Premium Background Effects */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -left-40 w-80 h-80 bg-gray-800/15 rounded-full blur-3xl\" />\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gray-700/15 rounded-full blur-3xl\" />\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gray-600/10 rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"relative max-w-[1920px] mx-auto px-8 lg:px-16\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-12\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"flex items-center space-x-4 mb-6\">\n                <div className=\"relative group\">\n                  <div className=\"w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-2xl\">\n                    <Play size={24} className=\"text-black fill-current ml-1\" />\n                  </div>\n                  <div className=\"absolute -inset-1 bg-white/20 rounded-2xl blur opacity-75\" />\n                </div>\n                <div>\n                  <h3 className=\"text-white text-2xl font-black tracking-tight\">Stream<span className=\"text-red-600\">Zen</span></h3>\n                  <p className=\"text-gray-400 text-sm font-medium\">Premium Entertainment</p>\n                </div>\n              </div>\n              \n              <p className=\"text-gray-400 text-lg leading-relaxed mb-8\">\n                Experience the future of streaming with our premium platform. Unlimited movies, series, and episodes at your fingertips.\n              </p>\n\n              {/* Social Links */}\n              <div className=\"flex items-center space-x-4\">\n                {socialLinks.map((social) => {\n                  const Icon = social.icon;\n                  return (\n                    <a\n                      key={social.label}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-12 h-12 glass-elevated rounded-2xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-white/10 transition-all duration-300 focus-ring group\"\n                    >\n                      <Icon size={20} className=\"group-hover:scale-110 transition-transform duration-300\" />\n                    </a>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* Links Sections */}\n            <div className=\"lg:col-span-3\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                {/* Platform Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6 flex items-center\">\n                    <Zap size={20} className=\"mr-2 text-blue-400\" />\n                    Platform\n                  </h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.platform.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Company Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6\">Company</h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.company.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Support Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6\">Support</h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.support.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Section */}\n        <div className=\"py-12 border-t border-white/10\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0\">\n            <div className=\"text-center lg:text-left\">\n              <h4 className=\"text-white text-2xl font-bold mb-2\">Stay Updated</h4>\n              <p className=\"text-gray-400 text-lg\">Get notified about new releases and features</p>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"relative\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"w-80 px-6 py-4 bg-white/5 border border-white/10 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:bg-white/10 transition-all duration-300\"\n                />\n                <Mail size={20} className=\"absolute right-4 top-1/2 -translate-y-1/2 text-gray-400\" />\n              </div>\n              <button className=\"px-8 py-4 bg-red-600 rounded-2xl text-white font-semibold hover:bg-red-700 transition-all duration-300 focus-ring shadow-2xl hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0\">\n            <div className=\"flex items-center space-x-2 text-gray-400 text-lg\">\n              <span>© {currentYear} StreamZen. Made with</span>\n              <Heart size={16} className=\"text-red-500 animate-pulse\" />\n              <span>for entertainment lovers.</span>\n            </div>\n            \n            <div className=\"flex items-center space-x-8 text-gray-400\">\n              <span className=\"text-lg\">All rights reserved</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\" />\n                <span className=\"text-lg font-medium\">All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAMA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,UAAU;YACR;gBAAE,OAAO;gBAAU,MAAM;YAAU;YACnC;gBAAE,OAAO;gBAAa,MAAM;YAAU;YACtC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAmB,MAAM;YAAW;SAC9C;QACD,SAAS;YACP;gBAAE,OAAO;gBAAY,MAAM;YAAS;YACpC;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAW,MAAM;YAAW;SACtC;QACD,SAAS;YACP;gBAAE,OAAO;gBAAe,MAAM;YAAQ;YACtC;gBAAE,OAAO;gBAAa,MAAM;YAAa;YACzC;gBAAE,OAAO;gBAAqB,MAAM;YAAY;YAChD;gBAAE,OAAO;gBAAU,MAAM;YAAU;SACpC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,yMAAA,CAAA,SAAM;YAAE,MAAM;YAAsB,OAAO;QAAS;QAC5D;YAAE,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;YAAuB,OAAO;QAAU;QAC/D;YAAE,MAAM,+MAAA,CAAA,YAAS;YAAE,MAAM;YAAyB,OAAO;QAAY;QACrE;YAAE,MAAM,2MAAA,CAAA,UAAO;YAAE,MAAM;YAAuB,OAAO;QAAU;KAChE;IAED,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;;;;;;sEAE5B,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;;gEAAgD;8EAAM,6LAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEACnG,6LAAC;4DAAE,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;sDAIrD,6LAAC;4CAAE,WAAU;sDAA6C;;;;;;sDAK1D,6LAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC;gDAChB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,6LAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC;wDAAK,MAAM;wDAAI,WAAU;;;;;;mDANrB,OAAO,KAAK;;;;;4CASvB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC,mMAAA,CAAA,MAAG;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAuB;;;;;;;kEAGlD,6LAAC;wDAAG,WAAU;kEACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC;0EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;;+DALN,KAAK,IAAI;;;;;;;;;;;;;;;;0DAaxB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAG,WAAU;kEACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;;+DALN,KAAK,IAAI;;;;;;;;;;;;;;;;0DAaxB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,6LAAC;wDAAG,WAAU;kEACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,6LAAC;0EACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;;+DALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiBhC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;8DAEZ,6LAAC,qMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;sDAE5B,6LAAC;4CAAO,WAAU;sDAA+I;;;;;;;;;;;;;;;;;;;;;;;kCAQvK,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAK;gDAAG;gDAAY;;;;;;;sDACrB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC3B,6LAAC;sDAAK;;;;;;;;;;;;8CAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;KA5LM;uCA8LS", "debugId": null}}, {"offset": {"line": 1012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/SyncInitializer.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nconst SyncInitializer: React.FC = () => {\n  useEffect(() => {\n    // Initialize VidSrc sync service on client side\n    const initializeSync = async () => {\n      try {\n        // Import dynamically to avoid SSR issues\n        const { default: VidSrcSyncService } = await import('@/lib/vidsrcSync');\n        const syncService = VidSrcSyncService.getInstance();\n        await syncService.initialize();\n        console.log('✅ VidSrc Sync Service initialized successfully');\n      } catch (error) {\n        console.error('❌ Failed to initialize VidSrc Sync Service:', error);\n      }\n    };\n\n    initializeSync();\n\n    // Cleanup on unmount\n    return () => {\n      import('@/lib/vidsrcSync').then(({ default: VidSrcSyncService }) => {\n        const syncService = VidSrcSyncService.getInstance();\n        syncService.destroy();\n      }).catch(console.error);\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n};\n\nexport default SyncInitializer;\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIA,MAAM,kBAA4B;;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,gDAAgD;YAChD,MAAM;4DAAiB;oBACrB,IAAI;wBACF,yCAAyC;wBACzC,MAAM,EAAE,SAAS,iBAAiB,EAAE,GAAG;wBACvC,MAAM,cAAc,kBAAkB,WAAW;wBACjD,MAAM,YAAY,UAAU;wBAC5B,QAAQ,GAAG,CAAC;oBACd,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,+CAA+C;oBAC/D;gBACF;;YAEA;YAEA,qBAAqB;YACrB;6CAAO;oBACL,4HAA2B,IAAI;qDAAC,CAAC,EAAE,SAAS,iBAAiB,EAAE;4BAC7D,MAAM,cAAc,kBAAkB,WAAW;4BACjD,YAAY,OAAO;wBACrB;oDAAG,KAAK,CAAC,QAAQ,KAAK;gBACxB;;QACF;oCAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;GA3BM;KAAA;uCA6BS", "debugId": null}}]}