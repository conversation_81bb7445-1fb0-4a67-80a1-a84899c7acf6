{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatYear(year: number): string {\n  return year.toString();\n}\n\nexport function formatRating(rating: number): string {\n  return rating.toFixed(1);\n}\n\nexport function formatRuntime(runtime: string): string {\n  return runtime;\n}\n\nexport function getImageUrl(url: string | undefined, fallback: string = '/placeholder-poster.jpg'): string {\n  if (!url) return fallback;\n  \n  // If it's already a full URL, return as is\n  if (url.startsWith('http')) return url;\n  \n  // If it's a relative path, make it absolute\n  return url.startsWith('/') ? url : `/${url}`;\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength).trim() + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,WAAW,IAAY;IACrC,OAAO,KAAK,QAAQ;AACtB;AAEO,SAAS,aAAa,MAAc;IACzC,OAAO,OAAO,OAAO,CAAC;AACxB;AAEO,SAAS,cAAc,OAAe;IAC3C,OAAO;AACT;AAEO,SAAS,YAAY,GAAuB,EAAE,WAAmB,yBAAyB;IAC/F,IAAI,CAAC,KAAK,OAAO;IAEjB,2CAA2C;IAC3C,IAAI,IAAI,UAAU,CAAC,SAAS,OAAO;IAEnC,4CAA4C;IAC5C,OAAO,IAAI,UAAU,CAAC,OAAO,MAAM,CAAC,CAAC,EAAE,KAAK;AAC9C;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AAC/C", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n\nexport interface Movie {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Series {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Episode {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: string;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n}\n\nexport interface ContentFilters {\n  genre?: string;\n  year?: number;\n  language?: string;\n  country?: string;\n  rating?: string;\n  quality?: string;\n  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n  search?: string;\n}\n\nexport interface GenreCount {\n  genre: string;\n  count: number;\n}\n\nexport interface LanguageCount {\n  language: string;\n  count: number;\n}\n\nexport interface CountryCount {\n  country: string;\n  count: number;\n}\n\nexport interface FilterOptions {\n  genres: GenreCount[];\n  languages: LanguageCount[];\n  countries: CountryCount[];\n  years: number[];\n  ratings: string[];\n  qualities: string[];\n}\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    // For server-side rendering, use absolute URL\n    const baseUrl = this.baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed: ${url}`, error);\n      throw error;\n    }\n  }\n\n  // Movies\n  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<Movie>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Movie>>(`/api/movies?${params.toString()}`);\n  }\n\n  async getMovie(id: string): Promise<Movie> {\n    return this.request<Movie>(`/api/movies/${id}`);\n  }\n\n  // Series\n  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<Series>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Series>>(`/api/series?${params.toString()}`);\n  }\n\n  async getSeriesById(id: string): Promise<Series> {\n    return this.request<Series>(`/api/series/${id}`);\n  }\n\n  async getSeriesEpisodes(id: string, season?: number): Promise<Episode[]> {\n    const params = season ? `?season=${season}` : '';\n    return this.request<Episode[]>(`/api/series/${id}/episodes${params}`);\n  }\n\n  // Episodes\n  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<Episode>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Episode>>(`/api/episodes?${params.toString()}`);\n  }\n\n  // Requests\n  async createBulkRequest(imdbIds: string[], contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<{ requestId: string; status: string; totalCount: number; message: string }> {\n    return this.request('/api/requests', {\n      method: 'POST',\n      body: JSON.stringify({ imdbIds, contentType }),\n    });\n  }\n\n  async getRequestStatus(requestId: string): Promise<any> {\n    return this.request(`/api/requests/${requestId}`);\n  }\n\n  // Filter Options\n  async getMovieFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/movies/filters');\n  }\n\n  async getSeriesFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/series/filters');\n  }\n\n  async getEpisodeFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/episodes/filters');\n  }\n\n  // Sync\n  async syncContent(): Promise<{ success: boolean; message: string; counts: { movies: number; series: number; episodes: number } }> {\n    return this.request('/api/sync', {\n      method: 'POST',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default ApiClient;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI,CAAC,6EAAyD,uBAAuB;AA8HzI,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,8CAA8C;QAC9C,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,6EAAyD,uBAAuB;QACjH,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,UAAU,UAAU;QAE5E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE;YAC5C,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAqC;QAC/E,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA2B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IAClF;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,YAAY,EAAE,IAAI;IAChD;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAsC;QAChF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA4B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACnF;IAEA,MAAM,cAAc,EAAU,EAAmB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAS,CAAC,YAAY,EAAE,IAAI;IACjD;IAEA,MAAM,kBAAkB,EAAU,EAAE,MAAe,EAAsB;QACvE,MAAM,SAAS,SAAS,CAAC,QAAQ,EAAE,QAAQ,GAAG;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAY,CAAC,YAAY,EAAE,GAAG,SAAS,EAAE,QAAQ;IACtE;IAEA,WAAW;IACX,MAAM,YAAY,UAA0B,CAAC,CAAC,EAAuC;QACnF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA6B,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;IACtF;IAEA,WAAW;IACX,MAAM,kBAAkB,OAAiB,EAAE,cAA2C,MAAM,EAAuF;QACjL,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAY;QAC9C;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAAgB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,WAAW;IAClD;IAEA,iBAAiB;IACjB,MAAM,wBAAgD;QACpD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,0BAAkD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,OAAO;IACP,MAAM,cAA4H;QAChI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/hooks/useSearch.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { apiClient } from '@/lib/api';\n\nexport interface SearchResult {\n  _id: string;\n  title: string;\n  type: 'movie' | 'series' | 'episode';\n  posterUrl?: string;\n  rating?: number;\n  releaseDate?: string;\n  imdbId?: string;\n  score: number;\n  \n  // Series specific\n  totalSeasons?: number;\n  \n  // Episode specific\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n  seriesPoster?: string;\n  seriesImdbId?: string;\n  airDate?: string;\n}\n\nexport interface SearchResponse {\n  results: SearchResult[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n  query: string;\n}\n\nexport interface SearchSuggestion {\n  _id: string;\n  title: string;\n  type: 'movie' | 'series' | 'episode';\n  posterUrl?: string;\n  rating?: number;\n  releaseDate?: string;\n  imdbId?: string;\n  score: number;\n  \n  // Series specific\n  totalSeasons?: number;\n  \n  // Episode specific\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n  seriesPoster?: string;\n  seriesImdbId?: string;\n  airDate?: string;\n}\n\nexport interface SuggestionsResponse {\n  suggestions: SearchSuggestion[];\n  query: string;\n}\n\nexport const useSearch = () => {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const search = useCallback(async (\n    query: string,\n    type: 'all' | 'movies' | 'series' | 'episodes' = 'all',\n    page: number = 1,\n    limit: number = 20\n  ): Promise<SearchResponse | null> => {\n    if (!query.trim()) {\n      return {\n        results: [],\n        pagination: { page: 1, limit, total: 0, pages: 0 },\n        query: ''\n      };\n    }\n\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const params = new URLSearchParams({\n        q: query.trim(),\n        type,\n        page: page.toString(),\n        limit: limit.toString()\n      });\n\n      const response = await apiClient.get(`/search?${params}`);\n      return response.data;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Search failed';\n      setError(errorMessage);\n      console.error('Search error:', err);\n      return null;\n    } finally {\n      setIsLoading(false);\n    }\n  }, []);\n\n  const getSuggestions = useCallback(async (\n    query: string,\n    limit: number = 8\n  ): Promise<SuggestionsResponse | null> => {\n    if (!query.trim() || query.trim().length < 2) {\n      return { suggestions: [], query: '' };\n    }\n\n    try {\n      const params = new URLSearchParams({\n        q: query.trim(),\n        limit: limit.toString()\n      });\n\n      const response = await apiClient.get(`/search/suggestions?${params}`);\n      return response.data;\n    } catch (err) {\n      console.error('Suggestions error:', err);\n      return { suggestions: [], query: query.trim() };\n    }\n  }, []);\n\n  return {\n    search,\n    getSuggestions,\n    isLoading,\n    error\n  };\n};\n\nexport const useSearchSuggestions = (query: string, limit: number = 8) => {\n  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const { getSuggestions } = useSearch();\n\n  useEffect(() => {\n    const fetchSuggestions = async () => {\n      if (!query.trim() || query.trim().length < 2) {\n        setSuggestions([]);\n        return;\n      }\n\n      setIsLoading(true);\n      const result = await getSuggestions(query, limit);\n      if (result) {\n        setSuggestions(result.suggestions);\n      }\n      setIsLoading(false);\n    };\n\n    const timeoutId = setTimeout(fetchSuggestions, 300); // Debounce\n    return () => clearTimeout(timeoutId);\n  }, [query, limit, getSuggestions]);\n\n  return {\n    suggestions,\n    isLoading\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AA8DO,MAAM,YAAY;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACzB,OACA,OAAiD,KAAK,EACtD,OAAe,CAAC,EAChB,QAAgB,EAAE;QAElB,IAAI,CAAC,MAAM,IAAI,IAAI;YACjB,OAAO;gBACL,SAAS,EAAE;gBACX,YAAY;oBAAE,MAAM;oBAAG;oBAAO,OAAO;oBAAG,OAAO;gBAAE;gBACjD,OAAO;YACT;QACF;QAEA,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG,MAAM,IAAI;gBACb;gBACA,MAAM,KAAK,QAAQ;gBACnB,OAAO,MAAM,QAAQ;YACvB;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OACjC,OACA,QAAgB,CAAC;QAEjB,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;YAC5C,OAAO;gBAAE,aAAa,EAAE;gBAAE,OAAO;YAAG;QACtC;QAEA,IAAI;YACF,MAAM,SAAS,IAAI,gBAAgB;gBACjC,GAAG,MAAM,IAAI;gBACb,OAAO,MAAM,QAAQ;YACvB;YAEA,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,QAAQ;YACpE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,sBAAsB;YACpC,OAAO;gBAAE,aAAa,EAAE;gBAAE,OAAO,MAAM,IAAI;YAAG;QAChD;IACF,GAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,uBAAuB,CAAC,OAAe,QAAgB,CAAC;IACnE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,cAAc,EAAE,GAAG;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,IAAI,CAAC,MAAM,IAAI,MAAM,MAAM,IAAI,GAAG,MAAM,GAAG,GAAG;gBAC5C,eAAe,EAAE;gBACjB;YACF;YAEA,aAAa;YACb,MAAM,SAAS,MAAM,eAAe,OAAO;YAC3C,IAAI,QAAQ;gBACV,eAAe,OAAO,WAAW;YACnC;YACA,aAAa;QACf;QAEA,MAAM,YAAY,WAAW,kBAAkB,MAAM,WAAW;QAChE,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;QAAO;QAAO;KAAe;IAEjC,OAAO;QACL;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 297, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/SearchBar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Search, X, Clock, Star, Calendar, Play } from 'lucide-react';\nimport { useRouter } from 'next/navigation';\nimport { useSearchSuggestions, SearchSuggestion } from '@/hooks/useSearch';\nimport Image from 'next/image';\n\ninterface SearchBarProps {\n  placeholder?: string;\n  className?: string;\n  showSuggestions?: boolean;\n  onSearch?: (query: string) => void;\n}\n\nconst SearchBar: React.FC<SearchBarProps> = ({\n  placeholder = \"Search movies, series, episodes...\",\n  className = \"\",\n  showSuggestions = true,\n  onSearch\n}) => {\n  const [query, setQuery] = useState('');\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const router = useRouter();\n  const inputRef = useRef<HTMLInputElement>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  \n  const { suggestions, isLoading } = useSearchSuggestions(query, 8);\n\n  // Close suggestions when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setSelectedIndex(-1);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const handleSearch = (searchQuery: string = query) => {\n    if (!searchQuery.trim()) return;\n    \n    setIsOpen(false);\n    setSelectedIndex(-1);\n    \n    if (onSearch) {\n      onSearch(searchQuery.trim());\n    } else {\n      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);\n    }\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!showSuggestions || !isOpen) {\n      if (e.key === 'Enter') {\n        handleSearch();\n      }\n      return;\n    }\n\n    switch (e.key) {\n      case 'ArrowDown':\n        e.preventDefault();\n        setSelectedIndex(prev => \n          prev < suggestions.length - 1 ? prev + 1 : prev\n        );\n        break;\n      case 'ArrowUp':\n        e.preventDefault();\n        setSelectedIndex(prev => prev > -1 ? prev - 1 : -1);\n        break;\n      case 'Enter':\n        e.preventDefault();\n        if (selectedIndex >= 0 && suggestions[selectedIndex]) {\n          handleSuggestionClick(suggestions[selectedIndex]);\n        } else {\n          handleSearch();\n        }\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        inputRef.current?.blur();\n        break;\n    }\n  };\n\n  const handleSuggestionClick = (suggestion: SearchSuggestion) => {\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    \n    // Navigate based on content type\n    if (suggestion.type === 'movie') {\n      router.push(`/watch/movie/${suggestion.imdbId}`);\n    } else if (suggestion.type === 'series') {\n      router.push(`/watch/series/${suggestion.imdbId}`);\n    } else if (suggestion.type === 'episode') {\n      router.push(`/watch/series/${suggestion.seriesImdbId}?season=${suggestion.season}&episode=${suggestion.episode}`);\n    }\n  };\n\n  const clearSearch = () => {\n    setQuery('');\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    inputRef.current?.focus();\n  };\n\n  const formatRating = (rating?: number) => {\n    return rating ? rating.toFixed(1) : 'N/A';\n  };\n\n  const formatDate = (date?: string) => {\n    if (!date) return '';\n    return new Date(date).getFullYear().toString();\n  };\n\n  return (\n    <div ref={containerRef} className={`relative ${className}`}>\n      {/* Search Input */}\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none\">\n          <Search className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        \n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => {\n            setQuery(e.target.value);\n            setIsOpen(showSuggestions && e.target.value.trim().length >= 2);\n            setSelectedIndex(-1);\n          }}\n          onFocus={() => {\n            if (showSuggestions && query.trim().length >= 2) {\n              setIsOpen(true);\n            }\n          }}\n          onKeyDown={handleKeyDown}\n          placeholder={placeholder}\n          className=\"w-full h-12 pl-12 pr-12 bg-gray-800/50 border border-gray-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200\"\n        />\n        \n        {query && (\n          <button\n            onClick={clearSearch}\n            className=\"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-white transition-colors\"\n          >\n            <X className=\"h-5 w-5\" />\n          </button>\n        )}\n      </div>\n\n      {/* Search Suggestions */}\n      {showSuggestions && isOpen && (\n        <div className=\"absolute top-full left-0 right-0 mt-2 bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl z-50 max-h-96 overflow-y-auto\">\n          {isLoading ? (\n            <div className=\"p-4 text-center text-gray-400\">\n              <div className=\"animate-spin w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full mx-auto\"></div>\n              <p className=\"mt-2 text-sm\">Searching...</p>\n            </div>\n          ) : suggestions.length > 0 ? (\n            <div className=\"py-2\">\n              {suggestions.map((suggestion, index) => (\n                <button\n                  key={`${suggestion.type}-${suggestion._id}`}\n                  onClick={() => handleSuggestionClick(suggestion)}\n                  className={`w-full px-4 py-3 flex items-center space-x-3 hover:bg-gray-800/50 transition-colors text-left ${\n                    selectedIndex === index ? 'bg-gray-800/50' : ''\n                  }`}\n                >\n                  {/* Poster/Thumbnail */}\n                  <div className=\"flex-shrink-0 w-12 h-16 bg-gray-800 rounded-lg overflow-hidden\">\n                    {(suggestion.posterUrl || suggestion.seriesPoster) ? (\n                      <Image\n                        src={suggestion.posterUrl || suggestion.seriesPoster || ''}\n                        alt={suggestion.title}\n                        width={48}\n                        height={64}\n                        className=\"w-full h-full object-cover\"\n                        unoptimized\n                      />\n                    ) : (\n                      <div className=\"w-full h-full flex items-center justify-center\">\n                        <Play className=\"w-6 h-6 text-gray-600\" />\n                      </div>\n                    )}\n                  </div>\n\n                  {/* Content Info */}\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <h4 className=\"text-white font-medium truncate\">\n                        {suggestion.title}\n                      </h4>\n                      <span className={`px-2 py-0.5 text-xs rounded-full ${\n                        suggestion.type === 'movie' ? 'bg-blue-600/20 text-blue-400' :\n                        suggestion.type === 'series' ? 'bg-green-600/20 text-green-400' :\n                        'bg-purple-600/20 text-purple-400'\n                      }`}>\n                        {suggestion.type === 'episode' ? 'EP' : suggestion.type.toUpperCase()}\n                      </span>\n                    </div>\n\n                    {/* Episode specific info */}\n                    {suggestion.type === 'episode' && (\n                      <p className=\"text-gray-400 text-sm truncate mb-1\">\n                        {suggestion.seriesTitle} • S{suggestion.season}E{suggestion.episode}\n                      </p>\n                    )}\n\n                    {/* Series specific info */}\n                    {suggestion.type === 'series' && suggestion.totalSeasons && (\n                      <p className=\"text-gray-400 text-sm mb-1\">\n                        {suggestion.totalSeasons} Season{suggestion.totalSeasons > 1 ? 's' : ''}\n                      </p>\n                    )}\n\n                    {/* Rating and Date */}\n                    <div className=\"flex items-center space-x-3 text-xs text-gray-500\">\n                      {suggestion.rating && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Star className=\"w-3 h-3 text-yellow-500\" />\n                          <span>{formatRating(suggestion.rating)}</span>\n                        </div>\n                      )}\n                      {(suggestion.releaseDate || suggestion.airDate) && (\n                        <div className=\"flex items-center space-x-1\">\n                          <Calendar className=\"w-3 h-3\" />\n                          <span>{formatDate(suggestion.releaseDate || suggestion.airDate)}</span>\n                        </div>\n                      )}\n                    </div>\n                  </div>\n                </button>\n              ))}\n              \n              {/* View All Results */}\n              <div className=\"border-t border-gray-700/50 mt-2 pt-2\">\n                <button\n                  onClick={() => handleSearch()}\n                  className=\"w-full px-4 py-2 text-center text-red-400 hover:text-red-300 text-sm font-medium transition-colors\"\n                >\n                  View all results for \"{query}\"\n                </button>\n              </div>\n            </div>\n          ) : query.trim().length >= 2 ? (\n            <div className=\"p-4 text-center text-gray-400\">\n              <Search className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\n              <p className=\"text-sm\">No results found for \"{query}\"</p>\n              <p className=\"text-xs mt-1\">Try different keywords or check spelling</p>\n            </div>\n          ) : null}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default SearchBar;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AANA;;;;;;;AAeA,MAAM,YAAsC,CAAC,EAC3C,cAAc,oCAAoC,EAClD,YAAY,EAAE,EACd,kBAAkB,IAAI,EACtB,QAAQ,EACT;IACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;IAE/D,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAChF,UAAU;gBACV,iBAAiB,CAAC;YACpB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC,cAAsB,KAAK;QAC/C,IAAI,CAAC,YAAY,IAAI,IAAI;QAEzB,UAAU;QACV,iBAAiB,CAAC;QAElB,IAAI,UAAU;YACZ,SAAS,YAAY,IAAI;QAC3B,OAAO;YACL,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,YAAY,IAAI,KAAK;QACnE;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,mBAAmB,CAAC,QAAQ;YAC/B,IAAI,EAAE,GAAG,KAAK,SAAS;gBACrB;YACF;YACA;QACF;QAEA,OAAQ,EAAE,GAAG;YACX,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OACf,OAAO,YAAY,MAAM,GAAG,IAAI,OAAO,IAAI;gBAE7C;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,iBAAiB,CAAA,OAAQ,OAAO,CAAC,IAAI,OAAO,IAAI,CAAC;gBACjD;YACF,KAAK;gBACH,EAAE,cAAc;gBAChB,IAAI,iBAAiB,KAAK,WAAW,CAAC,cAAc,EAAE;oBACpD,sBAAsB,WAAW,CAAC,cAAc;gBAClD,OAAO;oBACL;gBACF;gBACA;YACF,KAAK;gBACH,UAAU;gBACV,iBAAiB,CAAC;gBAClB,SAAS,OAAO,EAAE;gBAClB;QACJ;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,UAAU;QACV,iBAAiB,CAAC;QAElB,iCAAiC;QACjC,IAAI,WAAW,IAAI,KAAK,SAAS;YAC/B,OAAO,IAAI,CAAC,CAAC,aAAa,EAAE,WAAW,MAAM,EAAE;QACjD,OAAO,IAAI,WAAW,IAAI,KAAK,UAAU;YACvC,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,WAAW,MAAM,EAAE;QAClD,OAAO,IAAI,WAAW,IAAI,KAAK,WAAW;YACxC,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,WAAW,YAAY,CAAC,QAAQ,EAAE,WAAW,MAAM,CAAC,SAAS,EAAE,WAAW,OAAO,EAAE;QAClH;IACF;IAEA,MAAM,cAAc;QAClB,SAAS;QACT,UAAU;QACV,iBAAiB,CAAC;QAClB,SAAS,OAAO,EAAE;IACpB;IAEA,MAAM,eAAe,CAAC;QACpB,OAAO,SAAS,OAAO,OAAO,CAAC,KAAK;IACtC;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,IAAI,KAAK,MAAM,WAAW,GAAG,QAAQ;IAC9C;IAEA,qBACE,8OAAC;QAAI,KAAK;QAAc,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAExD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAGpB,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC;4BACT,SAAS,EAAE,MAAM,CAAC,KAAK;4BACvB,UAAU,mBAAmB,EAAE,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,MAAM,IAAI;4BAC7D,iBAAiB,CAAC;wBACpB;wBACA,SAAS;4BACP,IAAI,mBAAmB,MAAM,IAAI,GAAG,MAAM,IAAI,GAAG;gCAC/C,UAAU;4BACZ;wBACF;wBACA,WAAW;wBACX,aAAa;wBACb,WAAU;;;;;;oBAGX,uBACC,8OAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAMlB,mBAAmB,wBAClB,8OAAC;gBAAI,WAAU;0BACZ,0BACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;2BAE5B,YAAY,MAAM,GAAG,kBACvB,8OAAC;oBAAI,WAAU;;wBACZ,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;gCAEC,SAAS,IAAM,sBAAsB;gCACrC,WAAW,CAAC,8FAA8F,EACxG,kBAAkB,QAAQ,mBAAmB,IAC7C;;kDAGF,8OAAC;wCAAI,WAAU;kDACZ,AAAC,WAAW,SAAS,IAAI,WAAW,YAAY,iBAC/C,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,WAAW,SAAS,IAAI,WAAW,YAAY,IAAI;4CACxD,KAAK,WAAW,KAAK;4CACrB,OAAO;4CACP,QAAQ;4CACR,WAAU;4CACV,WAAW;;;;;iEAGb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAMtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,WAAW,KAAK;;;;;;kEAEnB,8OAAC;wDAAK,WAAW,CAAC,iCAAiC,EACjD,WAAW,IAAI,KAAK,UAAU,iCAC9B,WAAW,IAAI,KAAK,WAAW,mCAC/B,oCACA;kEACC,WAAW,IAAI,KAAK,YAAY,OAAO,WAAW,IAAI,CAAC,WAAW;;;;;;;;;;;;4CAKtE,WAAW,IAAI,KAAK,2BACnB,8OAAC;gDAAE,WAAU;;oDACV,WAAW,WAAW;oDAAC;oDAAK,WAAW,MAAM;oDAAC;oDAAE,WAAW,OAAO;;;;;;;4CAKtE,WAAW,IAAI,KAAK,YAAY,WAAW,YAAY,kBACtD,8OAAC;gDAAE,WAAU;;oDACV,WAAW,YAAY;oDAAC;oDAAQ,WAAW,YAAY,GAAG,IAAI,MAAM;;;;;;;0DAKzE,8OAAC;gDAAI,WAAU;;oDACZ,WAAW,MAAM,kBAChB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;0EAChB,8OAAC;0EAAM,aAAa,WAAW,MAAM;;;;;;;;;;;;oDAGxC,CAAC,WAAW,WAAW,IAAI,WAAW,OAAO,mBAC5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;0EAAM,WAAW,WAAW,WAAW,IAAI,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;+BAhEjE,GAAG,WAAW,IAAI,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE;;;;;sCAyE/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM;gCACf,WAAU;;oCACX;oCACwB;oCAAM;;;;;;;;;;;;;;;;;2BAIjC,MAAM,IAAI,GAAG,MAAM,IAAI,kBACzB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;;gCAAU;gCAAuB;gCAAM;;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAe;;;;;;;;;;;2BAE5B;;;;;;;;;;;;AAKd;uCAEe", "debugId": null}}, {"offset": {"line": 731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Search, Home, Film, Tv, Calendar, Plus, Play, X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport SearchBar from './SearchBar';\n\nconst Navigation: React.FC = () => {\n  const pathname = usePathname();\n  const [isSearchOpen, setIsSearchOpen] = useState(false);\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Home },\n    { href: '/movies', label: 'Movies', icon: Film },\n    { href: '/series', label: 'Series', icon: Tv },\n    { href: '/episodes', label: 'Episodes', icon: Calendar },\n  ];\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 glass border-b border-white/5 backdrop-blur-xl\">\n      <div className=\"max-w-[1920px] mx-auto px-8 lg:px-16\">\n        <div className=\"flex items-center justify-between h-24\">\n          {/* Apple TV + Netflix Style Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-4 focus-ring rounded-2xl px-3 py-2 transition-all duration-300 hover:bg-white/5\">\n            <div className=\"relative group\">\n              <div className=\"w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-2xl transition-all duration-300 group-hover:scale-110\">\n                <Play size={24} className=\"text-black fill-current ml-1\" />\n              </div>\n              <div className=\"absolute -inset-2 bg-white/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-300\" />\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-white text-3xl font-black tracking-tight\">\n                Stream<span className=\"text-red-600\">Zen</span>\n              </span>\n              <span className=\"text-gray-400 text-sm font-medium -mt-1 tracking-wide\">\n                Premium Entertainment\n              </span>\n            </div>\n          </Link>\n\n          {/* Ultra Premium Navigation Links */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = pathname === item.href;\n\n              return (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className={cn(\n                    'relative flex items-center space-x-3 px-8 py-4 rounded-2xl transition-all duration-300 focus-ring group',\n                    isActive\n                      ? 'bg-gradient-to-r from-gray-800/40 to-gray-700/40 text-white shadow-2xl border border-white/15 backdrop-blur-sm'\n                      : 'text-gray-300 hover:text-white hover:bg-white/10'\n                  )}\n                >\n                  <Icon size={22} className={cn(\n                    'transition-all duration-300',\n                    isActive ? 'text-gray-300' : 'group-hover:text-white'\n                  )} />\n                  <span className=\"font-semibold text-lg\">{item.label}</span>\n                  {isActive && (\n                    <div className=\"absolute -inset-1 bg-gradient-to-r from-gray-600/20 to-gray-500/20 rounded-2xl blur opacity-75\" />\n                  )}\n                </Link>\n              );\n            })}\n          </div>\n\n          {/* Ultra Premium Actions */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Premium Search Button */}\n            <button\n              onClick={() => setIsSearchOpen(!isSearchOpen)}\n              className=\"p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group\"\n            >\n              {isSearchOpen ? (\n                <X size={24} className=\"group-hover:scale-110 transition-transform duration-300\" />\n              ) : (\n                <Search size={24} className=\"group-hover:scale-110 transition-transform duration-300\" />\n              )}\n            </button>\n\n            {/* Apple TV + Netflix Style Request Button */}\n            <Link\n              href=\"/request\"\n              className=\"hidden sm:flex items-center space-x-3 px-8 py-4 bg-red-600 rounded-2xl text-white hover:bg-red-700 transition-all duration-300 focus-ring font-semibold shadow-2xl hover:scale-105\"\n            >\n              <Plus size={20} />\n              <span className=\"text-lg\">Request</span>\n            </Link>\n\n            {/* Premium Mobile Menu Button */}\n            <button className=\"lg:hidden p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group\">\n              <div className=\"w-6 h-6 flex flex-col justify-center space-y-1.5\">\n                <div className=\"w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4\" />\n                <div className=\"w-full h-0.5 bg-current rounded\" />\n                <div className=\"w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4 group-hover:ml-auto\" />\n              </div>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Premium Mobile Navigation */}\n      <div className=\"lg:hidden border-t border-white/5 glass\">\n        <div className=\"flex items-center justify-around py-4 px-2\">\n          {navItems.map((item) => {\n            const Icon = item.icon;\n            const isActive = pathname === item.href;\n\n            return (\n              <Link\n                key={item.href}\n                href={item.href}\n                className={cn(\n                  'flex flex-col items-center space-y-2 px-4 py-3 rounded-xl transition-all duration-300 focus-ring',\n                  isActive\n                    ? 'text-white bg-white/15'\n                    : 'text-gray-400 hover:text-white hover:bg-white/10'\n                )}\n              >\n                <Icon size={20} />\n                <span className=\"text-xs font-medium\">{item.label}</span>\n              </Link>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Search Overlay */}\n      {isSearchOpen && (\n        <div className=\"absolute top-full left-0 right-0 bg-black/95 backdrop-blur-xl border-b border-gray-800/50 z-40\">\n          <div className=\"max-w-[1920px] mx-auto px-8 lg:px-16 py-6\">\n            <div className=\"max-w-2xl mx-auto\">\n              <SearchBar\n                placeholder=\"Search movies, series, episodes...\"\n                className=\"w-full\"\n                onSearch={() => setIsSearchOpen(false)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,aAAuB;IAC3B,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,mMAAA,CAAA,OAAI;QAAC;QACvC;YAAE,MAAM;YAAW,OAAO;YAAU,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC/C;YAAE,MAAM;YAAW,OAAO;YAAU,MAAM,8LAAA,CAAA,KAAE;QAAC;QAC7C;YAAE,MAAM;YAAa,OAAO;YAAY,MAAM,0MAAA,CAAA,WAAQ;QAAC;KACxD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,MAAM;gDAAI,WAAU;;;;;;;;;;;sDAE5B,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDAAgD;8DACxD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEvC,8OAAC;4CAAK,WAAU;sDAAwD;;;;;;;;;;;;;;;;;;sCAO5E,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC;gCACb,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,aAAa,KAAK,IAAI;gCAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2GACA,WACI,mHACA;;sDAGN,8OAAC;4CAAK,MAAM;4CAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAC1B,+BACA,WAAW,kBAAkB;;;;;;sDAE/B,8OAAC;4CAAK,WAAU;sDAAyB,KAAK,KAAK;;;;;;wCAClD,0BACC,8OAAC;4CAAI,WAAU;;;;;;;mCAfZ,KAAK,IAAI;;;;;4BAmBpB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,SAAS,IAAM,gBAAgB,CAAC;oCAChC,WAAU;8CAET,6BACC,8OAAC,4LAAA,CAAA,IAAC;wCAAC,MAAM;wCAAI,WAAU;;;;;6DAEvB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,MAAM;wCAAI,WAAU;;;;;;;;;;;8CAKhC,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;8CAI5B,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC;wBACb,MAAM,OAAO,KAAK,IAAI;wBACtB,MAAM,WAAW,aAAa,KAAK,IAAI;wBAEvC,qBACE,8OAAC,4JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oGACA,WACI,2BACA;;8CAGN,8OAAC;oCAAK,MAAM;;;;;;8CACZ,8OAAC;oCAAK,WAAU;8CAAuB,KAAK,KAAK;;;;;;;2BAV5C,KAAK,IAAI;;;;;oBAapB;;;;;;;;;;;YAKH,8BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,+HAAA,CAAA,UAAS;4BACR,aAAY;4BACZ,WAAU;4BACV,UAAU,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;uCAEe", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Github, Twitter, Instagram, Youtube, Mail, Heart, Zap, Play } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerLinks = {\n    platform: [\n      { label: 'Movies', href: '/movies' },\n      { label: 'TV Series', href: '/series' },\n      { label: 'Episodes', href: '/episodes' },\n      { label: 'Request Content', href: '/request' }\n    ],\n    company: [\n      { label: 'About Us', href: '/about' },\n      { label: 'Privacy Policy', href: '/privacy' },\n      { label: 'Terms of Service', href: '/terms' },\n      { label: 'Contact', href: '/contact' }\n    ],\n    support: [\n      { label: 'Help Center', href: '/help' },\n      { label: 'Community', href: '/community' },\n      { label: 'API Documentation', href: '/api-docs' },\n      { label: 'Status', href: '/status' }\n    ]\n  };\n\n  const socialLinks = [\n    { icon: Github, href: 'https://github.com', label: 'GitHub' },\n    { icon: Twitter, href: 'https://twitter.com', label: 'Twitter' },\n    { icon: Instagram, href: 'https://instagram.com', label: 'Instagram' },\n    { icon: Youtube, href: 'https://youtube.com', label: 'YouTube' }\n  ];\n\n  return (\n    <footer className=\"relative bg-black border-t border-white/5\">\n      {/* Premium Background Effects */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -left-40 w-80 h-80 bg-gray-800/15 rounded-full blur-3xl\" />\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-gray-700/15 rounded-full blur-3xl\" />\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gray-600/10 rounded-full blur-3xl\" />\n      </div>\n\n      <div className=\"relative max-w-[1920px] mx-auto px-8 lg:px-16\">\n        {/* Main Footer Content */}\n        <div className=\"py-16\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-4 gap-12\">\n            {/* Brand Section */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"flex items-center space-x-4 mb-6\">\n                <div className=\"relative group\">\n                  <div className=\"w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-2xl\">\n                    <Play size={24} className=\"text-black fill-current ml-1\" />\n                  </div>\n                  <div className=\"absolute -inset-1 bg-white/20 rounded-2xl blur opacity-75\" />\n                </div>\n                <div>\n                  <h3 className=\"text-white text-2xl font-black tracking-tight\">Stream<span className=\"text-red-600\">Zen</span></h3>\n                  <p className=\"text-gray-400 text-sm font-medium\">Premium Entertainment</p>\n                </div>\n              </div>\n              \n              <p className=\"text-gray-400 text-lg leading-relaxed mb-8\">\n                Experience the future of streaming with our premium platform. Unlimited movies, series, and episodes at your fingertips.\n              </p>\n\n              {/* Social Links */}\n              <div className=\"flex items-center space-x-4\">\n                {socialLinks.map((social) => {\n                  const Icon = social.icon;\n                  return (\n                    <a\n                      key={social.label}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"w-12 h-12 glass-elevated rounded-2xl flex items-center justify-center text-gray-400 hover:text-white hover:bg-white/10 transition-all duration-300 focus-ring group\"\n                    >\n                      <Icon size={20} className=\"group-hover:scale-110 transition-transform duration-300\" />\n                    </a>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* Links Sections */}\n            <div className=\"lg:col-span-3\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                {/* Platform Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6 flex items-center\">\n                    <Zap size={20} className=\"mr-2 text-blue-400\" />\n                    Platform\n                  </h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.platform.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Company Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6\">Company</h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.company.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Support Links */}\n                <div>\n                  <h4 className=\"text-white text-xl font-bold mb-6\">Support</h4>\n                  <ul className=\"space-y-4\">\n                    {footerLinks.support.map((link) => (\n                      <li key={link.href}>\n                        <Link\n                          href={link.href}\n                          className=\"text-gray-400 hover:text-white transition-colors duration-300 text-lg font-medium hover:translate-x-1 transform transition-transform\"\n                        >\n                          {link.label}\n                        </Link>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Newsletter Section */}\n        <div className=\"py-12 border-t border-white/10\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between space-y-6 lg:space-y-0\">\n            <div className=\"text-center lg:text-left\">\n              <h4 className=\"text-white text-2xl font-bold mb-2\">Stay Updated</h4>\n              <p className=\"text-gray-400 text-lg\">Get notified about new releases and features</p>\n            </div>\n            \n            <div className=\"flex items-center space-x-4\">\n              <div className=\"relative\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"w-80 px-6 py-4 bg-white/5 border border-white/10 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 focus:bg-white/10 transition-all duration-300\"\n                />\n                <Mail size={20} className=\"absolute right-4 top-1/2 -translate-y-1/2 text-gray-400\" />\n              </div>\n              <button className=\"px-8 py-4 bg-red-600 rounded-2xl text-white font-semibold hover:bg-red-700 transition-all duration-300 focus-ring shadow-2xl hover:scale-105\">\n                Subscribe\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"py-8 border-t border-white/10\">\n          <div className=\"flex flex-col lg:flex-row items-center justify-between space-y-4 lg:space-y-0\">\n            <div className=\"flex items-center space-x-2 text-gray-400 text-lg\">\n              <span>© {currentYear} StreamZen. Made with</span>\n              <Heart size={16} className=\"text-red-500 animate-pulse\" />\n              <span>for entertainment lovers.</span>\n            </div>\n            \n            <div className=\"flex items-center space-x-8 text-gray-400\">\n              <span className=\"text-lg\">All rights reserved</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\" />\n                <span className=\"text-lg font-medium\">All systems operational</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;AAMA,MAAM,SAAmB;IACvB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,UAAU;YACR;gBAAE,OAAO;gBAAU,MAAM;YAAU;YACnC;gBAAE,OAAO;gBAAa,MAAM;YAAU;YACtC;gBAAE,OAAO;gBAAY,MAAM;YAAY;YACvC;gBAAE,OAAO;gBAAmB,MAAM;YAAW;SAC9C;QACD,SAAS;YACP;gBAAE,OAAO;gBAAY,MAAM;YAAS;YACpC;gBAAE,OAAO;gBAAkB,MAAM;YAAW;YAC5C;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAW,MAAM;YAAW;SACtC;QACD,SAAS;YACP;gBAAE,OAAO;gBAAe,MAAM;YAAQ;YACtC;gBAAE,OAAO;gBAAa,MAAM;YAAa;YACzC;gBAAE,OAAO;gBAAqB,MAAM;YAAY;YAChD;gBAAE,OAAO;gBAAU,MAAM;YAAU;SACpC;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM,sMAAA,CAAA,SAAM;YAAE,MAAM;YAAsB,OAAO;QAAS;QAC5D;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAuB,OAAO;QAAU;QAC/D;YAAE,MAAM,4MAAA,CAAA,YAAS;YAAE,MAAM;YAAyB,OAAO;QAAY;QACrE;YAAE,MAAM,wMAAA,CAAA,UAAO;YAAE,MAAM;YAAuB,OAAO;QAAU;KAChE;IAED,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gEAAC,MAAM;gEAAI,WAAU;;;;;;;;;;;sEAE5B,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;gEAAgD;8EAAM,8OAAC;oEAAK,WAAU;8EAAe;;;;;;;;;;;;sEACnG,8OAAC;4DAAE,WAAU;sEAAoC;;;;;;;;;;;;;;;;;;sDAIrD,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;sDAK1D,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC;gDAChB,MAAM,OAAO,OAAO,IAAI;gDACxB,qBACE,8OAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,8OAAC;wDAAK,MAAM;wDAAI,WAAU;;;;;;mDANrB,OAAO,KAAK;;;;;4CASvB;;;;;;;;;;;;8CAKJ,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC,gMAAA,CAAA,MAAG;gEAAC,MAAM;gEAAI,WAAU;;;;;;4DAAuB;;;;;;;kEAGlD,8OAAC;wDAAG,WAAU;kEACX,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,qBACzB,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;;+DALN,KAAK,IAAI;;;;;;;;;;;;;;;;0DAaxB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAG,WAAU;kEACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;;+DALN,KAAK,IAAI;;;;;;;;;;;;;;;;0DAaxB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAG,WAAU;kEACX,YAAY,OAAO,CAAC,GAAG,CAAC,CAAC,qBACxB,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEACH,MAAM,KAAK,IAAI;oEACf,WAAU;8EAET,KAAK,KAAK;;;;;;+DALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAiBhC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,aAAY;oDACZ,WAAU;;;;;;8DAEZ,8OAAC,kMAAA,CAAA,OAAI;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;sDAE5B,8OAAC;4CAAO,WAAU;sDAA+I;;;;;;;;;;;;;;;;;;;;;;;kCAQvK,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAK;gDAAG;gDAAY;;;;;;;sDACrB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,MAAM;4CAAI,WAAU;;;;;;sDAC3B,8OAAC;sDAAK;;;;;;;;;;;;8CAGR,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD;uCAEe", "debugId": null}}, {"offset": {"line": 1705, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/SyncInitializer.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\n\nconst SyncInitializer: React.FC = () => {\n  useEffect(() => {\n    // Initialize VidSrc sync service on client side\n    const initializeSync = async () => {\n      try {\n        // Import dynamically to avoid SSR issues\n        const { default: VidSrcSyncService } = await import('@/lib/vidsrcSync');\n        const syncService = VidSrcSyncService.getInstance();\n        await syncService.initialize();\n        console.log('✅ VidSrc Sync Service initialized successfully');\n      } catch (error) {\n        console.error('❌ Failed to initialize VidSrc Sync Service:', error);\n      }\n    };\n\n    initializeSync();\n\n    // Cleanup on unmount\n    return () => {\n      import('@/lib/vidsrcSync').then(({ default: VidSrcSyncService }) => {\n        const syncService = VidSrcSyncService.getInstance();\n        syncService.destroy();\n      }).catch(console.error);\n    };\n  }, []);\n\n  return null; // This component doesn't render anything\n};\n\nexport default SyncInitializer;\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAIA,MAAM,kBAA4B;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,MAAM,iBAAiB;YACrB,IAAI;gBACF,yCAAyC;gBACzC,MAAM,EAAE,SAAS,iBAAiB,EAAE,GAAG;gBACvC,MAAM,cAAc,kBAAkB,WAAW;gBACjD,MAAM,YAAY,UAAU;gBAC5B,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;QACF;QAEA;QAEA,qBAAqB;QACrB,OAAO;YACL,yHAA2B,IAAI,CAAC,CAAC,EAAE,SAAS,iBAAiB,EAAE;gBAC7D,MAAM,cAAc,kBAAkB,WAAW;gBACjD,YAAY,OAAO;YACrB,GAAG,KAAK,CAAC,QAAQ,KAAK;QACxB;IACF,GAAG,EAAE;IAEL,OAAO,MAAM,yCAAyC;AACxD;uCAEe", "debugId": null}}]}