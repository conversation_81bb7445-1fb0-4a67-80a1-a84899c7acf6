import { notFound } from 'next/navigation';
import { apiClient } from '@/lib/api';
import VideoPlayer from '@/components/VideoPlayer';
import ContentInfo from '@/components/ContentInfo';
import VidSrcAPI from '@/lib/vidsrc';

interface MovieWatchPageProps {
  params: Promise<{
    id: string;
  }>;
}

async function getMovie(id: string) {
  try {
    const movie = await apiClient.getMovie(id);
    return movie;
  } catch (error) {
    console.error('Error fetching movie:', error);
    return null;
  }
}

export default async function MovieWatchPage({ params }: MovieWatchPageProps) {
  const { id } = await params;
  const movie = await getMovie(id);

  if (!movie) {
    notFound();
  }

  // Generate all streaming sources
  const vidsrc = VidSrcAPI.getInstance();
  const streamingSources = vidsrc.generateAllMovieEmbedUrls(movie.imdbId, movie.tmdbId);

  const contentInfo = {
    title: movie.title,
    year: movie.year,
    rating: movie.rating,
    runtime: movie.runtime,
    imdbRating: movie.imdbRating,
    description: movie.description,
    genres: movie.genres,
    director: movie.director,
    cast: movie.cast,
    language: movie.language,
    country: movie.country,
    posterUrl: movie.posterUrl,
    type: 'movie' as const
  };

  return (
    <div className="min-h-screen bg-black">
      <VideoPlayer
        streamingSources={streamingSources}
        title={movie.title}
        type="movie"
      />

      <div className="max-w-[2560px] mx-auto px-8 lg:px-24 py-12">
        <ContentInfo content={contentInfo} />
      </div>
    </div>
  );
}
