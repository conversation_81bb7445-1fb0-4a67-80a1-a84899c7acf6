import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatYear(year: number): string {
  return year.toString();
}

export function formatRating(rating: number): string {
  return rating.toFixed(1);
}

export function formatRuntime(runtime: string): string {
  return runtime;
}

export function getImageUrl(url: string | undefined, fallback: string = '/placeholder-poster.jpg'): string {
  if (!url) return fallback;
  
  // If it's already a full URL, return as is
  if (url.startsWith('http')) return url;
  
  // If it's a relative path, make it absolute
  return url.startsWith('/') ? url : `/${url}`;
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength).trim() + '...';
}
