{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/SyncStatus.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface ISyncStatus extends Document {\n  lastSyncTime?: Date;\n  nextSyncTime: Date;\n  isRunning: boolean;\n  lastSyncResults: {\n    movies: number;\n    series: number;\n    episodes: number;\n  };\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst SyncStatusSchema: Schema = new Schema({\n  lastSyncTime: {\n    type: Date,\n    default: null\n  },\n  nextSyncTime: {\n    type: Date,\n    required: true,\n    index: true\n  },\n  isRunning: {\n    type: Boolean,\n    default: false,\n    index: true\n  },\n  lastSyncResults: {\n    movies: {\n      type: Number,\n      default: 0\n    },\n    series: {\n      type: Number,\n      default: 0\n    },\n    episodes: {\n      type: Number,\n      default: 0\n    }\n  }\n}, {\n  timestamps: true\n});\n\n// Index for querying sync status\nSyncStatusSchema.index({ createdAt: -1 });\nSyncStatusSchema.index({ nextSyncTime: 1, isRunning: 1 });\n\nexport default mongoose.models.SyncStatus || mongoose.model<ISyncStatus>('SyncStatus', SyncStatusSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAeA,MAAM,mBAA2B,IAAI,yGAAA,CAAA,SAAM,CAAC;IAC1C,cAAc;QACZ,MAAM;QACN,SAAS;IACX;IACA,cAAc;QACZ,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,SAAS;QACT,OAAO;IACT;IACA,iBAAiB;QACf,QAAQ;YACN,MAAM;YACN,SAAS;QACX;QACA,QAAQ;YACN,MAAM;YACN,SAAS;QACX;QACA,UAAU;YACR,MAAM;YACN,SAAS;QACX;IACF;AACF,GAAG;IACD,YAAY;AACd;AAEA,iCAAiC;AACjC,iBAAiB,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE;AACvC,iBAAiB,KAAK,CAAC;IAAE,cAAc;IAAG,WAAW;AAAE;uCAExC,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,UAAU,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAc,cAAc", "debugId": null}}]}