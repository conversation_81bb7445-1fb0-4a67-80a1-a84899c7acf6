module.exports = {

"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Movie.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MovieSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    title: {
        type: String,
        required: true,
        index: true
    },
    year: {
        type: Number,
        required: true,
        index: true
    },
    rating: String,
    runtime: String,
    imdbRating: {
        type: Number,
        index: true
    },
    imdbVotes: String,
    popularity: {
        type: Number,
        index: true
    },
    popularityDelta: Number,
    posterUrl: String,
    trailerUrl: String,
    trailerRuntime: String,
    trailerLikes: String,
    description: String,
    genres: [
        {
            type: String,
            index: true
        }
    ],
    director: String,
    cast: [
        String
    ],
    language: {
        type: String,
        index: true
    },
    country: {
        type: String,
        index: true
    },
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    quality: {
        type: String,
        index: true
    }
}, {
    timestamps: true
});
// Compound indexes for better query performance
MovieSchema.index({
    year: -1,
    imdbRating: -1
});
MovieSchema.index({
    genres: 1,
    year: -1
});
// Removed text index to avoid language override issues
MovieSchema.index({
    title: 1
});
MovieSchema.index({
    language: 1,
    country: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Movie || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Movie', MovieSchema);
}}),
"[project]/src/models/Series.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const SeriesSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    title: {
        type: String,
        required: true,
        index: true
    },
    startYear: {
        type: Number,
        required: true,
        index: true
    },
    endYear: {
        type: Number,
        index: true
    },
    rating: String,
    imdbRating: {
        type: Number,
        index: true
    },
    imdbVotes: String,
    popularity: {
        type: Number,
        index: true
    },
    popularityDelta: Number,
    posterUrl: String,
    trailerUrl: String,
    description: String,
    genres: [
        {
            type: String,
            index: true
        }
    ],
    creator: String,
    cast: [
        String
    ],
    language: {
        type: String,
        index: true
    },
    country: {
        type: String,
        index: true
    },
    totalSeasons: Number,
    status: {
        type: String,
        enum: [
            'ongoing',
            'ended',
            'cancelled'
        ],
        index: true
    },
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String // VidSrc TMDB embed URL
}, {
    timestamps: true
});
// Compound indexes for better query performance
SeriesSchema.index({
    startYear: -1,
    imdbRating: -1
});
SeriesSchema.index({
    genres: 1,
    startYear: -1
});
SeriesSchema.index({
    status: 1,
    startYear: -1
});
// Removed text index to avoid language override issues
SeriesSchema.index({
    title: 1
});
SeriesSchema.index({
    language: 1,
    country: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Series || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Series', SeriesSchema);
}}),
"[project]/src/models/Episode.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const EpisodeSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    seriesTitle: {
        type: String,
        required: true,
        index: true
    },
    season: {
        type: Number,
        required: true,
        index: true
    },
    episode: {
        type: Number,
        required: true,
        index: true
    },
    episodeTitle: String,
    airDate: {
        type: Date,
        index: true
    },
    runtime: String,
    imdbRating: Number,
    description: String,
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    quality: {
        type: String,
        index: true
    },
    genres: [
        {
            type: String,
            index: true
        }
    ]
}, {
    timestamps: true
});
// Compound indexes for better query performance
EpisodeSchema.index({
    imdbId: 1,
    season: 1,
    episode: 1
}, {
    unique: true
});
EpisodeSchema.index({
    airDate: -1
});
EpisodeSchema.index({
    seriesTitle: 1,
    season: 1,
    episode: 1
});
EpisodeSchema.index({
    createdAt: -1
}); // For latest episodes
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Episode || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Episode', EpisodeSchema);
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/node:assert [external] (node:assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:assert", () => require("node:assert"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:querystring [external] (node:querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:querystring", () => require("node:querystring"));

module.exports = mod;
}}),
"[externals]/node:events [external] (node:events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:events", () => require("node:events"));

module.exports = mod;
}}),
"[externals]/node:diagnostics_channel [external] (node:diagnostics_channel, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:diagnostics_channel", () => require("node:diagnostics_channel"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:tls [external] (node:tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:tls", () => require("node:tls"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:util/types [external] (node:util/types, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util/types", () => require("node:util/types"));

module.exports = mod;
}}),
"[externals]/node:worker_threads [external] (node:worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:worker_threads", () => require("node:worker_threads"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/node:http2 [external] (node:http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http2", () => require("node:http2"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:console [external] (node:console, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:console", () => require("node:console"));

module.exports = mod;
}}),
"[externals]/node:dns [external] (node:dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:dns", () => require("node:dns"));

module.exports = mod;
}}),
"[project]/src/lib/scraper.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/load-parse.js [app-ssr] (ecmascript)");
;
;
class IMDbScraper {
    static instance;
    requestCount = 0;
    lastRequestTime = 0;
    RATE_LIMIT = 30;
    MIN_DELAY = 2000;
    static getInstance() {
        if (!IMDbScraper.instance) {
            IMDbScraper.instance = new IMDbScraper();
        }
        return IMDbScraper.instance;
    }
    async rateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.MIN_DELAY) {
            const delay = this.MIN_DELAY - timeSinceLastRequest;
            // Add random jitter to avoid detection patterns
            const jitter = Math.random() * 1000; // 0-1000ms random delay
            await new Promise((resolve)=>setTimeout(resolve, delay + jitter));
        }
        this.lastRequestTime = Date.now();
        this.requestCount++;
    }
    getRandomUserAgent() {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ];
        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }
    async fetchPage(imdbId) {
        await this.rateLimit();
        const url = `https://www.imdb.com/title/${imdbId}/`;
        const headers = {
            'User-Agent': this.getRandomUserAgent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        };
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers,
                timeout: 30000
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["load"])(response.data);
        } catch (error) {
            console.error(`Error fetching IMDb page for ${imdbId}:`, error);
            throw new Error(`Failed to fetch IMDb page: ${error.message}`);
        }
    }
    extractBasicInfo($) {
        const titleElement = $('h1[data-testid="hero__pageTitle"] span[data-testid="hero__primary-text"]');
        const title = titleElement.text().trim();
        if (!title) {
            throw new Error('Could not extract title from IMDb page');
        }
        // Extract year from the release info
        const yearElement = $('ul.ipc-inline-list a[href*="/releaseinfo/"]');
        const yearText = yearElement.text().trim();
        const year = parseInt(yearText) || new Date().getFullYear();
        // Determine if it's a movie or series
        const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();
        const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');
        return {
            title,
            year,
            type: isMovie ? 'movie' : 'series'
        };
    }
    extractRating($) {
        const ratingElement = $('ul.ipc-inline-list a[href*="/parentalguide/"]');
        return ratingElement.text().trim() || undefined;
    }
    extractRuntime($) {
        const runtimeElements = $('ul.ipc-inline-list li');
        for(let i = 0; i < runtimeElements.length; i++){
            const text = $(runtimeElements[i]).text().trim();
            if (text.includes('h') || text.includes('min')) {
                return text;
            }
        }
        return undefined;
    }
    extractIMDbRating($) {
        const ratingElement = $('div[data-testid="hero-rating-bar__aggregate-rating__score"] span');
        const rating = parseFloat(ratingElement.text().trim()) || undefined;
        const votesElement = $('div.sc-d541859f-3');
        const votes = votesElement.text().trim() || undefined;
        return {
            rating,
            votes
        };
    }
    extractPopularity($) {
        const popularityElement = $('div[data-testid="hero-rating-bar__popularity__score"]');
        const popularity = parseInt(popularityElement.text().trim()) || undefined;
        const deltaElement = $('div[data-testid="hero-rating-bar__popularity__delta"]');
        const deltaText = deltaElement.text().trim();
        const delta = deltaText ? parseInt(deltaText.replace(/[^\d-]/g, '')) : undefined;
        return {
            popularity,
            delta
        };
    }
    extractPosterUrl($) {
        // Try multiple selectors for poster image
        const selectors = [
            'div[data-testid="hero-media__poster"] img',
            '.ipc-image[data-testid="hero-media__poster"]',
            '.poster img',
            '.ipc-media img',
            'img[class*="poster"]',
            'a[class*="ipc-lockup-overlay"] img'
        ];
        for (const selector of selectors){
            const element = $(selector);
            const src = element.attr('src');
            if (src && src.includes('media-amazon.com')) {
                // Clean up the URL to get high quality image
                return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1000_.').replace(/\._.*?\./, '._V1_FMjpg_UX1000_.');
            }
        }
        return undefined;
    }
    extractBackdropUrl($) {
        // Try to extract backdrop/hero image
        const selectors = [
            '.hero-media__slate-overlay img',
            '.slate img',
            '.hero__background img',
            'div[data-testid="hero-media"] img'
        ];
        for (const selector of selectors){
            const element = $(selector);
            const src = element.attr('src');
            if (src && src.includes('media-amazon.com')) {
                return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1920_.');
            }
        }
        return undefined;
    }
    extractTrailerInfo($) {
        const trailerElement = $('a[data-testid="video-player-slate-overlay"]');
        const url = trailerElement.attr('href') || undefined;
        const runtimeElement = $('span[data-testid="video-player-slate-runtime"]');
        const runtime = runtimeElement.text().trim() || undefined;
        const likesElement = $('span.ipc-reaction-summary__label');
        const likes = likesElement.text().trim() || undefined;
        return {
            url,
            runtime,
            likes
        };
    }
    extractDescription($) {
        // Try multiple selectors for description
        const selectors = [
            'div[data-testid="hero-media__slate"] img',
            'span[data-testid="plot-xl"]',
            'span[data-testid="plot-l"]',
            'span[data-testid="plot"]'
        ];
        for (const selector of selectors){
            const element = $(selector);
            if (selector.includes('img')) {
                const alt = element.attr('alt');
                if (alt && alt.length > 20) return alt;
            } else {
                const text = element.text().trim();
                if (text && text.length > 10) return text;
            }
        }
        return undefined;
    }
    extractGenres($) {
        const genres = [];
        // Updated genre selectors based on current IMDb structure
        const genreSelectors = [
            // Current IMDb hero section genres (most common)
            '[data-testid="genres"] .ipc-chip .ipc-chip__text',
            '[data-testid="genres"] .ipc-chip__text',
            '[data-testid="genres"] a .ipc-chip__text',
            // Hero section alternative formats
            '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',
            '.GenresAndPlot__GenreChip .ipc-chip__text',
            // Storyline section (your provided structure - keep as fallback)
            'li[data-testid="storyline-genres"] .ipc-metadata-list-item__list-content-item',
            'li[data-testid="storyline-genres"] a[href*="genres="]',
            // General genre link selectors (broader search)
            'a[href*="/search/title/?genres="] span',
            'a[href*="genres="] span',
            'a[href*="genres="]',
            // Schema.org microdata
            '[itemprop="genre"]',
            'span[itemprop="genre"]',
            // Fallback selectors
            '.see-more.inline.canwrap a[href*="genres="]',
            '.titlePageSprite.star-box-giga-star + div a[href*="genres="]',
            // Very broad fallback - any link with genre in URL
            'a[href*="explore=genres"]'
        ];
        console.log(`🔍 Starting genre extraction...`);
        for(let i = 0; i < genreSelectors.length; i++){
            const selector = genreSelectors[i];
            const elements = $(selector);
            console.log(`🔍 Selector ${i + 1}/${genreSelectors.length}: "${selector}" found ${elements.length} elements`);
            if (elements.length > 0) {
                elements.each((_, element)=>{
                    const genre = $(element).text().trim();
                    console.log(`📝 Found genre text: "${genre}"`);
                    // Clean up genre text and validate
                    if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {
                        // Skip common non-genre text
                        const skipTexts = [
                            'Genres',
                            'Genre',
                            'See all',
                            'More',
                            'All',
                            '...'
                        ];
                        if (!skipTexts.includes(genre)) {
                            genres.push(genre);
                            console.log(`✅ Added genre: "${genre}"`);
                        }
                    }
                });
                if (genres.length > 0) {
                    console.log(`✅ Successfully extracted ${genres.length} genres: [${genres.join(', ')}]`);
                    break; // Use the first selector that finds results
                }
            }
        }
        if (genres.length === 0) {
            console.log('⚠️ No genres found with any selector');
            // Debug: Let's see what's actually in the storyline section
            const storylineSection = $('li[data-testid="storyline-genres"]');
            if (storylineSection.length > 0) {
                console.log('📋 Storyline section HTML:', storylineSection.html());
            } else {
                console.log('❌ No storyline-genres section found');
            }
        }
        return genres;
    }
    extractCast($) {
        const cast = [];
        // Try multiple selectors for cast
        const castSelectors = [
            'section[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
            '.cast_list .primary_photo + td a',
            '.titleCast .primary_photo + td a',
            'div[data-testid="title-cast-item"] a[href*="/name/"]'
        ];
        for (const selector of castSelectors){
            const elements = $(selector);
            if (elements.length > 0) {
                elements.each((_, element)=>{
                    const actorName = $(element).text().trim();
                    if (actorName && !cast.includes(actorName) && cast.length < 10) {
                        cast.push(actorName);
                    }
                });
                break; // Use the first selector that finds results
            }
        }
        return cast;
    }
    extractDirector($) {
        // Try multiple selectors for director
        const directorSelectors = [
            'li[data-testid="title-pc-principal-credit"]:contains("Director") .ipc-metadata-list-item__list-content-item',
            'li[data-testid="title-pc-principal-credit"]:contains("Directors") .ipc-metadata-list-item__list-content-item',
            'a[href*="/name/"][href*="ref_=tt_ov_dr"]',
            '.credit_summary_item:contains("Director") a'
        ];
        for (const selector of directorSelectors){
            const element = $(selector).first();
            const director = element.text().trim();
            if (director) {
                return director;
            }
        }
        return undefined;
    }
    extractCreator($) {
        // Try multiple selectors for creator (for TV series)
        const creatorSelectors = [
            'li[data-testid="title-pc-principal-credit"]:contains("Creator") .ipc-metadata-list-item__list-content-item',
            'li[data-testid="title-pc-principal-credit"]:contains("Creators") .ipc-metadata-list-item__list-content-item',
            '.credit_summary_item:contains("Creator") a',
            '.credit_summary_item:contains("Created by") a'
        ];
        for (const selector of creatorSelectors){
            const element = $(selector).first();
            const creator = element.text().trim();
            if (creator) {
                return creator;
            }
        }
        return undefined;
    }
    extractLanguage($) {
        // Try multiple selectors for language
        const languageSelectors = [
            'li[data-testid="title-details-languages"] .ipc-metadata-list-item__list-content-item',
            'div[data-testid="title-details-section"] li:contains("Language") .ipc-metadata-list-item__list-content-item',
            'a[href*="primary_language="]',
            '.txt-block:contains("Language") a'
        ];
        for (const selector of languageSelectors){
            const element = $(selector).first();
            const language = element.text().trim();
            if (language) {
                return language;
            }
        }
        return undefined;
    }
    extractCountry($) {
        // Try multiple selectors for country
        const countrySelectors = [
            'li[data-testid="title-details-origin"] .ipc-metadata-list-item__list-content-item',
            'div[data-testid="title-details-section"] li:contains("Country") .ipc-metadata-list-item__list-content-item',
            'a[href*="country_of_origin="]',
            '.txt-block:contains("Country") a'
        ];
        for (const selector of countrySelectors){
            const element = $(selector).first();
            const country = element.text().trim();
            if (country) {
                return country;
            }
        }
        return undefined;
    }
    async scrapeMovie(imdbId) {
        const $ = await this.fetchPage(imdbId);
        const basicInfo = this.extractBasicInfo($);
        if (basicInfo.type !== 'movie') {
            throw new Error('IMDb ID does not correspond to a movie');
        }
        const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
        const { popularity, delta: popularityDelta } = this.extractPopularity($);
        const { url: trailerUrl, runtime: trailerRuntime, likes: trailerLikes } = this.extractTrailerInfo($);
        return {
            title: basicInfo.title,
            year: basicInfo.year,
            rating: this.extractRating($),
            runtime: this.extractRuntime($),
            imdbRating,
            imdbVotes,
            popularity,
            popularityDelta,
            posterUrl: this.extractPosterUrl($),
            backdropUrl: this.extractBackdropUrl($),
            trailerUrl,
            trailerRuntime,
            trailerLikes,
            description: this.extractDescription($),
            genres: this.extractGenres($),
            director: this.extractDirector($),
            cast: this.extractCast($),
            language: this.extractLanguage($),
            country: this.extractCountry($)
        };
    }
    async scrapeSeries(imdbId) {
        const $ = await this.fetchPage(imdbId);
        const basicInfo = this.extractBasicInfo($);
        if (basicInfo.type !== 'series') {
            throw new Error('IMDb ID does not correspond to a TV series');
        }
        const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
        const { popularity, delta: popularityDelta } = this.extractPopularity($);
        const { url: trailerUrl } = this.extractTrailerInfo($);
        return {
            title: basicInfo.title,
            startYear: basicInfo.year,
            endYear: undefined,
            rating: this.extractRating($),
            imdbRating,
            imdbVotes,
            popularity,
            popularityDelta,
            posterUrl: this.extractPosterUrl($),
            backdropUrl: this.extractBackdropUrl($),
            trailerUrl,
            description: this.extractDescription($),
            genres: this.extractGenres($),
            creator: this.extractCreator($),
            cast: this.extractCast($),
            language: this.extractLanguage($),
            country: this.extractCountry($),
            totalSeasons: undefined,
            status: 'ongoing'
        };
    }
}
const __TURBOPACK__default__export__ = IMDbScraper;
}}),
"[project]/src/lib/vidsrcSync.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Movie.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Series.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Episode.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scraper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/scraper.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
class VidSrcSyncService {
    static instance;
    syncInterval = null;
    isInitialized = false;
    scraper;
    PARALLEL_LIMIT = 10;
    BATCH_DELAY = 2000;
    ITEM_DELAY = 200;
    constructor(){
        this.scraper = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scraper$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getInstance();
    }
    static getInstance() {
        if (!VidSrcSyncService.instance) {
            VidSrcSyncService.instance = new VidSrcSyncService();
        }
        return VidSrcSyncService.instance;
    }
    async initialize() {
        if (this.isInitialized) return;
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
        await this.checkAndRunSync();
        this.startSyncScheduler();
        this.isInitialized = true;
        console.log('🔄 VidSrc Sync Service initialized');
    }
    async processInParallel(items, processor, batchSize = this.PARALLEL_LIMIT) {
        const results = [];
        for(let i = 0; i < items.length; i += batchSize){
            const batch = items.slice(i, i + batchSize);
            console.log(`📦 Processing batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(items.length / batchSize)} (${batch.length} items)`);
            // Process batch in parallel with individual delays
            const batchPromises = batch.map(async (item, batchIndex)=>{
                // Add staggered delay to avoid hitting rate limits
                await new Promise((resolve)=>setTimeout(resolve, batchIndex * this.ITEM_DELAY));
                return processor(item, i + batchIndex);
            });
            const batchResults = await Promise.allSettled(batchPromises);
            // Collect successful results
            batchResults.forEach((result, index)=>{
                if (result.status === 'fulfilled') {
                    results.push(result.value);
                } else {
                    console.error(`❌ Error in batch item ${i + index}:`, result.reason);
                }
            });
            // Delay between batches to avoid detection
            if (i + batchSize < items.length) {
                console.log(`⏳ Waiting ${this.BATCH_DELAY}ms before next batch...`);
                await new Promise((resolve)=>setTimeout(resolve, this.BATCH_DELAY));
            }
        }
        return results;
    }
    async checkAndRunSync() {
        const lastSync = await this.getLastSyncStatus();
        const now = new Date();
        if (!lastSync || now >= lastSync.nextSyncTime) {
            console.log('⏰ Sync time reached, starting automatic sync...');
            await this.runFullSync();
        } else {
            const timeUntilNext = lastSync.nextSyncTime.getTime() - now.getTime();
            console.log(`⏳ Next sync in ${Math.round(timeUntilNext / (1000 * 60 * 60))} hours`);
        }
    }
    startSyncScheduler() {
        // Check every hour if it's time to sync
        this.syncInterval = setInterval(async ()=>{
            await this.checkAndRunSync();
        }, 60 * 60 * 1000); // 1 hour
    }
    async runFullSync() {
        console.log('🚀 Starting VidSrc full sync...');
        const syncStatus = await this.updateSyncStatus(true);
        const results = {
            movies: 0,
            series: 0,
            episodes: 0
        };
        try {
            // Sync movies (pages 1-15)
            console.log('📽️ Syncing movies...');
            results.movies = await this.syncMovies();
            // Sync series (pages 1-15)
            console.log('📺 Syncing series...');
            results.series = await this.syncSeries();
            // Sync episodes (pages 1-15)
            console.log('🎬 Syncing episodes...');
            results.episodes = await this.syncEpisodes();
            // Update sync status
            await this.updateSyncStatus(false, results);
            console.log('✅ VidSrc sync completed:', results);
            return results;
        } catch (error) {
            console.error('❌ VidSrc sync failed:', error);
            await this.updateSyncStatus(false, results);
            throw error;
        }
    }
    async syncMovies() {
        let totalMovies = 0;
        for(let page = 1; page <= 15; page++){
            try {
                const url = `https://vidsrc.xyz/movies/latest/page-${page}.json`;
                console.log(`📥 Fetching movies page ${page}...`);
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(url, {
                    timeout: 30000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
                const movies = response.data.result;
                // Filter out duplicates before processing
                const existingMovieIds = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].find({
                    imdbId: {
                        $in: movies.map((m)=>m.imdb_id)
                    }
                }, {
                    imdbId: 1
                }).lean();
                const existingIds = new Set(existingMovieIds.map((m)=>m.imdbId));
                const newMovies = movies.filter((movie)=>!existingIds.has(movie.imdb_id));
                console.log(`🎬 Found ${movies.length} movies, ${newMovies.length} new movies to process`);
                if (newMovies.length === 0) {
                    console.log(`⏭️ No new movies on page ${page}, skipping...`);
                    continue;
                }
                // Process movies in parallel batches
                const processedMovies = await this.processInParallel(newMovies, async (movieData)=>this.processMovie(movieData));
                const successfulMovies = processedMovies.filter((result)=>result !== null);
                totalMovies += successfulMovies.length;
                console.log(`✅ Page ${page}: Added ${successfulMovies.length}/${newMovies.length} movies`);
                // Add delay between pages to avoid rate limiting
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`❌ Error fetching movies page ${page}:`, error);
            }
        }
        return totalMovies;
    }
    async processMovie(movieData) {
        try {
            console.log(`🔍 Scraping metadata for movie: ${movieData.title} (${movieData.imdb_id})`);
            // Scrape complete metadata from IMDb
            const scrapedData = await this.scraper.scrapeMovie(movieData.imdb_id);
            // Create movie with scraped metadata
            const movie = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
                imdbId: movieData.imdb_id,
                tmdbId: movieData.tmdb_id,
                title: scrapedData.title || movieData.title,
                year: scrapedData.year || new Date().getFullYear(),
                rating: scrapedData.rating,
                runtime: scrapedData.runtime,
                imdbRating: scrapedData.imdbRating,
                imdbVotes: scrapedData.imdbVotes,
                popularity: scrapedData.popularity,
                popularityDelta: scrapedData.popularityDelta,
                posterUrl: scrapedData.posterUrl,
                trailerUrl: scrapedData.trailerUrl,
                trailerRuntime: scrapedData.trailerRuntime,
                trailerLikes: scrapedData.trailerLikes,
                description: scrapedData.description,
                genres: scrapedData.genres || [],
                director: scrapedData.director,
                cast: scrapedData.cast || [],
                language: scrapedData.language,
                country: scrapedData.country,
                embedUrl: movieData.embed_url,
                embedUrlTmdb: movieData.embed_url_tmdb,
                vidsrcUrl: movieData.embed_url,
                vidsrcTmdbUrl: movieData.embed_url_tmdb,
                quality: movieData.quality
            });
            await movie.save();
            console.log(`✅ Added movie with metadata: ${scrapedData.title || movieData.title} (${movieData.imdb_id})`);
            return movie;
        } catch (error) {
            console.error(`❌ Error processing movie ${movieData.imdb_id}:`, error);
            // Fallback: Create movie with basic data if scraping fails
            try {
                const movie = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
                    imdbId: movieData.imdb_id,
                    tmdbId: movieData.tmdb_id,
                    title: movieData.title,
                    year: new Date().getFullYear(),
                    description: `Movie: ${movieData.title}`,
                    genres: [
                        'Drama'
                    ],
                    imdbRating: 0,
                    runtime: '120 min',
                    language: 'English',
                    country: 'US',
                    director: 'Unknown',
                    cast: [],
                    embedUrl: movieData.embed_url,
                    embedUrlTmdb: movieData.embed_url_tmdb || '',
                    vidsrcUrl: movieData.embed_url,
                    vidsrcTmdbUrl: movieData.embed_url_tmdb,
                    quality: movieData.quality || 'HD'
                });
                await movie.save();
                console.log(`⚠️ Added movie with fallback data: ${movieData.title} (${movieData.imdb_id})`);
                return movie;
            } catch (fallbackError) {
                console.error(`❌ Failed to save movie even with fallback: ${movieData.imdb_id}`, fallbackError);
                return null;
            }
        }
    }
    async syncSeries() {
        let totalSeries = 0;
        for(let page = 1; page <= 15; page++){
            try {
                const url = `https://vidsrc.xyz/tvshows/latest/page-${page}.json`;
                console.log(`📥 Fetching series page ${page}...`);
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(url, {
                    timeout: 30000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
                const seriesList = response.data.result;
                // Filter out duplicates before processing
                const existingSeriesIds = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].find({
                    imdbId: {
                        $in: seriesList.map((s)=>s.imdb_id)
                    }
                }, {
                    imdbId: 1
                }).lean();
                const existingIds = new Set(existingSeriesIds.map((s)=>s.imdbId));
                const newSeries = seriesList.filter((series)=>!existingIds.has(series.imdb_id));
                console.log(`📺 Found ${seriesList.length} series, ${newSeries.length} new series to process`);
                if (newSeries.length === 0) {
                    console.log(`⏭️ No new series on page ${page}, skipping...`);
                    continue;
                }
                // Process series in parallel batches
                const processedSeries = await this.processInParallel(newSeries, async (seriesData)=>this.processSeries(seriesData));
                const successfulSeries = processedSeries.filter((result)=>result !== null);
                totalSeries += successfulSeries.length;
                console.log(`✅ Page ${page}: Added ${successfulSeries.length}/${newSeries.length} series`);
            } catch (error) {
                console.error(`❌ Error fetching series page ${page}:`, error);
            }
        }
        return totalSeries;
    }
    async processSeries(seriesData) {
        try {
            console.log(`🔍 Scraping metadata for series: ${seriesData.title} (${seriesData.imdb_id})`);
            // Scrape complete metadata from IMDb
            const scrapedData = await this.scraper.scrapeSeries(seriesData.imdb_id);
            // Create series with scraped metadata
            const series = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
                imdbId: seriesData.imdb_id,
                tmdbId: seriesData.tmdb_id,
                title: scrapedData.title || seriesData.title,
                startYear: scrapedData.startYear || new Date().getFullYear(),
                endYear: scrapedData.endYear,
                rating: scrapedData.rating,
                imdbRating: scrapedData.imdbRating,
                imdbVotes: scrapedData.imdbVotes,
                popularity: scrapedData.popularity,
                popularityDelta: scrapedData.popularityDelta,
                posterUrl: scrapedData.posterUrl,
                trailerUrl: scrapedData.trailerUrl,
                description: scrapedData.description,
                genres: scrapedData.genres || [],
                creator: scrapedData.creator,
                cast: scrapedData.cast || [],
                language: scrapedData.language,
                country: scrapedData.country,
                totalSeasons: scrapedData.totalSeasons,
                status: scrapedData.status,
                embedUrl: seriesData.embed_url,
                embedUrlTmdb: seriesData.embed_url_tmdb,
                vidsrcUrl: seriesData.embed_url,
                vidsrcTmdbUrl: seriesData.embed_url_tmdb
            });
            await series.save();
            console.log(`✅ Added series with metadata: ${scrapedData.title || seriesData.title} (${seriesData.imdb_id})`);
            return series;
        } catch (error) {
            console.error(`❌ Error processing series ${seriesData.imdb_id}:`, error);
            // Fallback: Create series with basic data if scraping fails
            try {
                const series = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
                    imdbId: seriesData.imdb_id,
                    tmdbId: seriesData.tmdb_id,
                    title: seriesData.title,
                    startYear: new Date().getFullYear(),
                    description: `TV Series: ${seriesData.title}`,
                    genres: [
                        'Drama'
                    ],
                    imdbRating: 0,
                    status: 'ongoing',
                    totalSeasons: 1,
                    language: 'English',
                    country: 'US',
                    cast: [],
                    embedUrl: seriesData.embed_url,
                    embedUrlTmdb: seriesData.embed_url_tmdb || '',
                    vidsrcUrl: seriesData.embed_url,
                    vidsrcTmdbUrl: seriesData.embed_url_tmdb
                });
                await series.save();
                console.log(`⚠️ Added series with fallback data: ${seriesData.title} (${seriesData.imdb_id})`);
                return series;
            } catch (fallbackError) {
                console.error(`❌ Failed to save series even with fallback: ${seriesData.imdb_id}`, fallbackError);
                return null;
            }
        }
    }
    async syncEpisodes() {
        let totalEpisodes = 0;
        for(let page = 1; page <= 15; page++){
            try {
                const url = `https://vidsrc.xyz/episodes/latest/page-${page}.json`;
                console.log(`📥 Fetching episodes page ${page}...`);
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(url, {
                    timeout: 30000,
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                    }
                });
                const episodes = response.data.result;
                for (const episodeData of episodes){
                    try {
                        // Check if episode already exists
                        const existingEpisode = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].findOne({
                            imdbId: episodeData.imdb_id,
                            season: parseInt(episodeData.season),
                            episode: parseInt(episodeData.episode)
                        });
                        if (existingEpisode) continue;
                        // Ensure series exists (auto-create if needed)
                        await this.ensureSeriesExistsForSync(episodeData.imdb_id, episodeData.show_title);
                        // Get series for poster URL and genres
                        const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].findOne({
                            imdbId: episodeData.imdb_id
                        });
                        // Generate VidSrc embed URL using the pattern
                        const embedUrl = `https://vidsrc.me/embed/tv?imdb=${episodeData.imdb_id}&season=${episodeData.season}&episode=${episodeData.episode}`;
                        const episode = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
                            imdbId: episodeData.imdb_id,
                            tmdbId: episodeData.tmdb_id,
                            seriesTitle: episodeData.show_title,
                            season: parseInt(episodeData.season),
                            episode: parseInt(episodeData.episode),
                            episodeTitle: `Episode ${episodeData.episode}`,
                            description: `Episode ${episodeData.episode} of ${episodeData.show_title}`,
                            posterUrl: series?.posterUrl || '',
                            seriesPosterUrl: series?.posterUrl || '',
                            genres: series?.genres || [],
                            quality: episodeData.quality || 'HD',
                            embedUrl: embedUrl,
                            embedUrlTmdb: episodeData.embed_url_tmdb || '',
                            vidsrcUrl: episodeData.embed_url,
                            vidsrcTmdbUrl: episodeData.embed_url_tmdb,
                            runtime: '45 min',
                            airDate: new Date().toISOString(),
                            createdAt: new Date(),
                            updatedAt: new Date()
                        });
                        await episode.save();
                        totalEpisodes++;
                        console.log(`✅ Added episode: ${episodeData.show_title} S${episodeData.season}E${episodeData.episode}`);
                    } catch (error) {
                        console.error(`❌ Error processing episode ${episodeData.imdb_id} S${episodeData.season}E${episodeData.episode}:`, error);
                    }
                }
                // Add delay between pages
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`❌ Error fetching episodes page ${page}:`, error);
            }
        }
        return totalEpisodes;
    }
    async ensureSeriesExistsForSync(imdbId, title) {
        const existingSeries = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].findOne({
            imdbId
        });
        if (!existingSeries) {
            try {
                console.log(`🔍 Auto-creating series with metadata: ${title} (${imdbId})`);
                // Scrape complete metadata from IMDb
                const scrapedData = await this.scraper.scrapeSeries(imdbId);
                const series = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
                    imdbId,
                    title: scrapedData.title || title,
                    startYear: scrapedData.startYear || new Date().getFullYear(),
                    endYear: scrapedData.endYear,
                    rating: scrapedData.rating,
                    imdbRating: scrapedData.imdbRating,
                    imdbVotes: scrapedData.imdbVotes,
                    popularity: scrapedData.popularity,
                    popularityDelta: scrapedData.popularityDelta,
                    posterUrl: scrapedData.posterUrl,
                    trailerUrl: scrapedData.trailerUrl,
                    description: scrapedData.description || `TV Series: ${title}`,
                    genres: scrapedData.genres || [],
                    creator: scrapedData.creator,
                    cast: scrapedData.cast || [],
                    language: scrapedData.language,
                    country: scrapedData.country,
                    totalSeasons: scrapedData.totalSeasons,
                    status: scrapedData.status,
                    embedUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
                    embedUrlTmdb: '',
                    vidsrcUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
                    vidsrcTmdbUrl: ''
                });
                await series.save();
                console.log(`✅ Auto-created series with metadata: ${scrapedData.title || title} (${imdbId})`);
            } catch (error) {
                console.error(`❌ Error scraping series metadata for ${imdbId}:`, error);
                // Fallback: Create series with basic data
                const series = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
                    imdbId,
                    title,
                    startYear: new Date().getFullYear(),
                    description: `TV Series: ${title}`,
                    genres: [
                        'Drama'
                    ],
                    imdbRating: 0,
                    status: 'ongoing',
                    totalSeasons: 1,
                    language: 'English',
                    country: 'US',
                    cast: [],
                    embedUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
                    embedUrlTmdb: '',
                    vidsrcUrl: `https://vidsrc.me/embed/tv?imdb=${imdbId}`,
                    vidsrcTmdbUrl: ''
                });
                await series.save();
                console.log(`⚠️ Auto-created series with fallback data: ${title} (${imdbId})`);
            }
        }
    }
    async getLastSyncStatus() {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
        const { default: SyncStatusModel } = await __turbopack_context__.r("[project]/src/models/SyncStatus.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        return SyncStatusModel.findOne().sort({
            createdAt: -1
        }).lean();
    }
    async updateSyncStatus(isRunning, results) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
        const { default: SyncStatusModel } = await __turbopack_context__.r("[project]/src/models/SyncStatus.ts [app-ssr] (ecmascript, async loader)")(__turbopack_context__.i);
        const now = new Date();
        const nextSync = new Date(now.getTime() + 12 * 60 * 60 * 1000); // 12 hours from now
        const syncStatus = {
            lastSyncTime: isRunning ? undefined : now,
            nextSyncTime: nextSync,
            isRunning,
            lastSyncResults: results || {
                movies: 0,
                series: 0,
                episodes: 0
            },
            updatedAt: now
        };
        const existing = await SyncStatusModel.findOne().sort({
            createdAt: -1
        });
        if (existing) {
            Object.assign(existing, syncStatus);
            return existing.save();
        } else {
            return SyncStatusModel.create({
                ...syncStatus,
                createdAt: now
            });
        }
    }
    async getSyncStatus() {
        return this.getLastSyncStatus();
    }
    async forcSync() {
        console.log('🔄 Force sync requested...');
        return this.runFullSync();
    }
    destroy() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        this.isInitialized = false;
        console.log('🛑 VidSrc Sync Service destroyed');
    }
}
const __TURBOPACK__default__export__ = VidSrcSyncService;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__8935775f._.js.map