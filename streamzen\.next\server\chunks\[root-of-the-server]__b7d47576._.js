module.exports = {

"[project]/.next-internal/server/app/api/search/suggestions/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Movie.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MovieSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    title: {
        type: String,
        required: true,
        index: true
    },
    year: {
        type: Number,
        required: true,
        index: true
    },
    rating: String,
    runtime: String,
    imdbRating: {
        type: Number,
        index: true
    },
    imdbVotes: String,
    popularity: {
        type: Number,
        index: true
    },
    popularityDelta: Number,
    posterUrl: String,
    trailerUrl: String,
    trailerRuntime: String,
    trailerLikes: String,
    description: String,
    genres: [
        {
            type: String,
            index: true
        }
    ],
    director: String,
    cast: [
        String
    ],
    language: {
        type: String,
        index: true
    },
    country: {
        type: String,
        index: true
    },
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    quality: {
        type: String,
        index: true
    }
}, {
    timestamps: true
});
// Compound indexes for better query performance
MovieSchema.index({
    year: -1,
    imdbRating: -1
});
MovieSchema.index({
    genres: 1,
    year: -1
});
// Removed text index to avoid language override issues
MovieSchema.index({
    title: 1
});
MovieSchema.index({
    language: 1,
    country: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Movie || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Movie', MovieSchema);
}}),
"[project]/src/models/Series.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const SeriesSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    title: {
        type: String,
        required: true,
        index: true
    },
    startYear: {
        type: Number,
        required: true,
        index: true
    },
    endYear: {
        type: Number,
        index: true
    },
    rating: String,
    imdbRating: {
        type: Number,
        index: true
    },
    imdbVotes: String,
    popularity: {
        type: Number,
        index: true
    },
    popularityDelta: Number,
    posterUrl: String,
    trailerUrl: String,
    description: String,
    genres: [
        {
            type: String,
            index: true
        }
    ],
    creator: String,
    cast: [
        String
    ],
    language: {
        type: String,
        index: true
    },
    country: {
        type: String,
        index: true
    },
    totalSeasons: Number,
    status: {
        type: String,
        enum: [
            'ongoing',
            'ended',
            'cancelled'
        ],
        index: true
    },
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String // VidSrc TMDB embed URL
}, {
    timestamps: true
});
// Compound indexes for better query performance
SeriesSchema.index({
    startYear: -1,
    imdbRating: -1
});
SeriesSchema.index({
    genres: 1,
    startYear: -1
});
SeriesSchema.index({
    status: 1,
    startYear: -1
});
// Removed text index to avoid language override issues
SeriesSchema.index({
    title: 1
});
SeriesSchema.index({
    language: 1,
    country: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Series || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Series', SeriesSchema);
}}),
"[project]/src/models/Episode.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const EpisodeSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    seriesTitle: {
        type: String,
        required: true,
        index: true
    },
    season: {
        type: Number,
        required: true,
        index: true
    },
    episode: {
        type: Number,
        required: true,
        index: true
    },
    episodeTitle: String,
    airDate: {
        type: Date,
        index: true
    },
    runtime: String,
    imdbRating: Number,
    description: String,
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    quality: {
        type: String,
        index: true
    },
    genres: [
        {
            type: String,
            index: true
        }
    ]
}, {
    timestamps: true
});
// Compound indexes for better query performance
EpisodeSchema.index({
    imdbId: 1,
    season: 1,
    episode: 1
}, {
    unique: true
});
EpisodeSchema.index({
    airDate: -1
});
EpisodeSchema.index({
    seriesTitle: 1,
    season: 1,
    episode: 1
});
EpisodeSchema.index({
    createdAt: -1
}); // For latest episodes
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Episode || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Episode', EpisodeSchema);
}}),
"[project]/src/app/api/search/suggestions/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Movie.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Series.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Episode.ts [app-route] (ecmascript)");
;
;
;
;
;
async function GET(request) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { searchParams } = new URL(request.url);
        const query = searchParams.get('q');
        const limit = parseInt(searchParams.get('limit') || '8');
        if (!query || query.trim().length < 2) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                suggestions: []
            });
        }
        const searchQuery = query.trim();
        const searchRegex = new RegExp(searchQuery, 'i');
        const exactRegex = new RegExp(`^${searchQuery}`, 'i');
        const suggestions = [];
        // Get movie suggestions
        const movies = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    $or: [
                        {
                            title: exactRegex
                        },
                        {
                            title: searchRegex
                        }
                    ]
                }
            },
            {
                $addFields: {
                    score: {
                        $add: [
                            // Exact start match gets highest score
                            {
                                $cond: [
                                    {
                                        $regexMatch: {
                                            input: "$title",
                                            regex: exactRegex
                                        }
                                    },
                                    100,
                                    0
                                ]
                            },
                            // Contains match gets lower score
                            {
                                $cond: [
                                    {
                                        $regexMatch: {
                                            input: "$title",
                                            regex: searchRegex
                                        }
                                    },
                                    50,
                                    0
                                ]
                            },
                            // Rating bonus (only for numeric ratings)
                            {
                                $multiply: [
                                    {
                                        $cond: [
                                            {
                                                $and: [
                                                    {
                                                        $ne: [
                                                            "$imdbRating",
                                                            null
                                                        ]
                                                    },
                                                    {
                                                        $ne: [
                                                            "$imdbRating",
                                                            ""
                                                        ]
                                                    },
                                                    {
                                                        $eq: [
                                                            {
                                                                $type: "$imdbRating"
                                                            },
                                                            "number"
                                                        ]
                                                    }
                                                ]
                                            },
                                            "$imdbRating",
                                            0
                                        ]
                                    },
                                    2
                                ]
                            }
                        ]
                    },
                    type: {
                        $literal: "movie"
                    }
                }
            },
            {
                $sort: {
                    score: -1,
                    rating: -1
                }
            },
            {
                $limit: Math.ceil(limit / 3)
            },
            {
                $project: {
                    title: 1,
                    posterUrl: 1,
                    rating: "$imdbRating",
                    releaseDate: 1,
                    type: 1,
                    imdbId: 1,
                    score: 1
                }
            }
        ]);
        // Get series suggestions
        const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    $or: [
                        {
                            title: exactRegex
                        },
                        {
                            title: searchRegex
                        }
                    ]
                }
            },
            {
                $addFields: {
                    score: {
                        $add: [
                            // Exact start match gets highest score
                            {
                                $cond: [
                                    {
                                        $regexMatch: {
                                            input: "$title",
                                            regex: exactRegex
                                        }
                                    },
                                    100,
                                    0
                                ]
                            },
                            // Contains match gets lower score
                            {
                                $cond: [
                                    {
                                        $regexMatch: {
                                            input: "$title",
                                            regex: searchRegex
                                        }
                                    },
                                    50,
                                    0
                                ]
                            },
                            // Rating bonus (only for numeric ratings)
                            {
                                $multiply: [
                                    {
                                        $cond: [
                                            {
                                                $and: [
                                                    {
                                                        $ne: [
                                                            "$imdbRating",
                                                            null
                                                        ]
                                                    },
                                                    {
                                                        $ne: [
                                                            "$imdbRating",
                                                            ""
                                                        ]
                                                    },
                                                    {
                                                        $eq: [
                                                            {
                                                                $type: "$imdbRating"
                                                            },
                                                            "number"
                                                        ]
                                                    }
                                                ]
                                            },
                                            "$imdbRating",
                                            0
                                        ]
                                    },
                                    2
                                ]
                            }
                        ]
                    },
                    type: {
                        $literal: "series"
                    }
                }
            },
            {
                $sort: {
                    score: -1,
                    rating: -1
                }
            },
            {
                $limit: Math.ceil(limit / 3)
            },
            {
                $project: {
                    title: 1,
                    posterUrl: 1,
                    rating: "$imdbRating",
                    releaseDate: 1,
                    totalSeasons: 1,
                    type: 1,
                    imdbId: 1,
                    score: 1
                }
            }
        ]);
        // Get episode suggestions (with series info)
        const episodes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    $or: [
                        {
                            title: exactRegex
                        },
                        {
                            title: searchRegex
                        }
                    ]
                }
            },
            {
                $lookup: {
                    from: 'series',
                    localField: 'seriesImdbId',
                    foreignField: 'imdbId',
                    as: 'series'
                }
            },
            {
                $addFields: {
                    score: {
                        $add: [
                            // Exact start match gets highest score
                            {
                                $cond: [
                                    {
                                        $regexMatch: {
                                            input: "$title",
                                            regex: exactRegex
                                        }
                                    },
                                    100,
                                    0
                                ]
                            },
                            // Contains match gets lower score
                            {
                                $cond: [
                                    {
                                        $regexMatch: {
                                            input: "$title",
                                            regex: searchRegex
                                        }
                                    },
                                    50,
                                    0
                                ]
                            },
                            // Rating bonus (only for numeric ratings)
                            {
                                $multiply: [
                                    {
                                        $cond: [
                                            {
                                                $and: [
                                                    {
                                                        $ne: [
                                                            "$imdbRating",
                                                            null
                                                        ]
                                                    },
                                                    {
                                                        $ne: [
                                                            "$imdbRating",
                                                            ""
                                                        ]
                                                    },
                                                    {
                                                        $eq: [
                                                            {
                                                                $type: "$imdbRating"
                                                            },
                                                            "number"
                                                        ]
                                                    }
                                                ]
                                            },
                                            "$imdbRating",
                                            0
                                        ]
                                    },
                                    2
                                ]
                            },
                            // Recent episodes bonus
                            {
                                $cond: [
                                    {
                                        $gte: [
                                            "$createdAt",
                                            new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
                                        ]
                                    },
                                    10,
                                    0
                                ]
                            }
                        ]
                    },
                    type: {
                        $literal: "episode"
                    },
                    seriesTitle: {
                        $arrayElemAt: [
                            "$series.title",
                            0
                        ]
                    },
                    seriesPoster: {
                        $arrayElemAt: [
                            "$series.posterUrl",
                            0
                        ]
                    }
                }
            },
            {
                $sort: {
                    score: -1,
                    rating: -1,
                    createdAt: -1
                }
            },
            {
                $limit: Math.ceil(limit / 3)
            },
            {
                $project: {
                    title: 1,
                    season: 1,
                    episode: 1,
                    rating: "$imdbRating",
                    airDate: 1,
                    seriesTitle: 1,
                    seriesPoster: 1,
                    seriesImdbId: 1,
                    type: 1,
                    score: 1
                }
            }
        ]);
        // Combine and sort all suggestions
        suggestions.push(...movies, ...series, ...episodes);
        suggestions.sort((a, b)=>{
            if (b.score !== a.score) return b.score - a.score;
            if (b.rating !== a.rating) return (b.rating || 0) - (a.rating || 0);
            return new Date(b.createdAt || b.releaseDate).getTime() - new Date(a.createdAt || a.releaseDate).getTime();
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            suggestions: suggestions.slice(0, limit),
            query: searchQuery
        });
    } catch (error) {
        console.error('Search suggestions error:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to get suggestions'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__b7d47576._.js.map