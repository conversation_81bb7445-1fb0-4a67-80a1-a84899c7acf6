{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ui/Button.tsx"], "sourcesContent": ["import React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'ghost';\n  size?: 'sm' | 'md' | 'lg';\n  children: React.ReactNode;\n}\n\nconst Button: React.FC<ButtonProps> = ({\n  variant = 'primary',\n  size = 'md',\n  className,\n  children,\n  ...props\n}) => {\n  const baseClasses = 'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/20';\n  \n  const variantClasses = {\n    primary: 'bg-white text-black hover:bg-gray-200 active:bg-gray-300',\n    secondary: 'bg-white/10 text-white hover:bg-white/20 active:bg-white/30 backdrop-blur-sm',\n    ghost: 'text-white hover:bg-white/10 active:bg-white/20'\n  };\n  \n  const sizeClasses = {\n    sm: 'px-3 py-1.5 text-sm',\n    md: 'px-4 py-2 text-base',\n    lg: 'px-6 py-3 text-lg'\n  };\n\n  return (\n    <button\n      className={cn(\n        baseClasses,\n        variantClasses[variant],\n        sizeClasses[size],\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </button>\n  );\n};\n\nexport default Button;\n"], "names": [], "mappings": ";;;;AACA;;;AAQA,MAAM,SAAgC,CAAC,EACrC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,SAAS,EACT,QAAQ,EACR,GAAG,OACJ;IACC,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/api.ts"], "sourcesContent": ["const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n\nexport interface Movie {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Series {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Episode {\n  _id: string;\n  imdbId: string;\n  tmdbId?: string;\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: string;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  quality?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n}\n\nexport interface ContentFilters {\n  genre?: string;\n  year?: number;\n  language?: string;\n  country?: string;\n  rating?: string;\n  quality?: string;\n  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n  search?: string;\n}\n\nexport interface GenreCount {\n  genre: string;\n  count: number;\n}\n\nexport interface LanguageCount {\n  language: string;\n  count: number;\n}\n\nexport interface CountryCount {\n  country: string;\n  count: number;\n}\n\nexport interface FilterOptions {\n  genres: GenreCount[];\n  languages: LanguageCount[];\n  countries: CountryCount[];\n  years: number[];\n  ratings: string[];\n  qualities: string[];\n}\n\nclass ApiClient {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = API_BASE_URL) {\n    this.baseUrl = baseUrl;\n  }\n\n  private async request<T>(endpoint: string, options?: RequestInit): Promise<T> {\n    // For server-side rendering, use absolute URL\n    const baseUrl = this.baseUrl || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');\n    const url = endpoint.startsWith('http') ? endpoint : `${baseUrl}${endpoint}`;\n\n    try {\n      const response = await fetch(url, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options?.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      return await response.json();\n    } catch (error) {\n      console.error(`API request failed: ${url}`, error);\n      throw error;\n    }\n  }\n\n  // Movies\n  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<Movie>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Movie>>(`/api/movies?${params.toString()}`);\n  }\n\n  async getMovie(id: string): Promise<Movie> {\n    return this.request<Movie>(`/api/movies/${id}`);\n  }\n\n  // Series\n  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<Series>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Series>>(`/api/series?${params.toString()}`);\n  }\n\n  async getSeriesById(id: string): Promise<Series> {\n    return this.request<Series>(`/api/series/${id}`);\n  }\n\n  async getSeriesEpisodes(id: string, season?: number): Promise<Episode[]> {\n    const params = season ? `?season=${season}` : '';\n    return this.request<Episode[]>(`/api/series/${id}/episodes${params}`);\n  }\n\n  // Episodes\n  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<Episode>> {\n    const params = new URLSearchParams();\n    Object.entries(filters).forEach(([key, value]) => {\n      if (value !== undefined && value !== null) {\n        params.append(key, value.toString());\n      }\n    });\n    \n    return this.request<PaginatedResponse<Episode>>(`/api/episodes?${params.toString()}`);\n  }\n\n  // Requests\n  async createBulkRequest(imdbIds: string[], contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<{ requestId: string; status: string; totalCount: number; message: string }> {\n    return this.request('/api/requests', {\n      method: 'POST',\n      body: JSON.stringify({ imdbIds, contentType }),\n    });\n  }\n\n  async getRequestStatus(requestId: string): Promise<any> {\n    return this.request(`/api/requests/${requestId}`);\n  }\n\n  // Filter Options\n  async getMovieFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/movies/filters');\n  }\n\n  async getSeriesFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/series/filters');\n  }\n\n  async getEpisodeFilterOptions(): Promise<FilterOptions> {\n    return this.request<FilterOptions>('/api/episodes/filters');\n  }\n\n  // Sync\n  async syncContent(): Promise<{ success: boolean; message: string; counts: { movies: number; series: number; episodes: number } }> {\n    return this.request('/api/sync', {\n      method: 'POST',\n    });\n  }\n}\n\nexport const apiClient = new ApiClient();\nexport default ApiClient;\n"], "names": [], "mappings": ";;;;AAAA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI,CAAC,6EAAyD,uBAAuB;AA8HzI,MAAM;IACI,QAAgB;IAExB,YAAY,UAAkB,YAAY,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAc,QAAW,QAAgB,EAAE,OAAqB,EAAc;QAC5E,8CAA8C;QAC9C,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC,6EAAyD,uBAAuB;QACjH,MAAM,MAAM,SAAS,UAAU,CAAC,UAAU,WAAW,GAAG,UAAU,UAAU;QAE5E,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,SAAS,OAAO;gBACrB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,OAAO,MAAM,SAAS,IAAI;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,oBAAoB,EAAE,KAAK,EAAE;YAC5C,MAAM;QACR;IACF;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAqC;QAC/E,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA2B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IAClF;IAEA,MAAM,SAAS,EAAU,EAAkB;QACzC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,YAAY,EAAE,IAAI;IAChD;IAEA,SAAS;IACT,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAsC;QAChF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA4B,CAAC,YAAY,EAAE,OAAO,QAAQ,IAAI;IACnF;IAEA,MAAM,cAAc,EAAU,EAAmB;QAC/C,OAAO,IAAI,CAAC,OAAO,CAAS,CAAC,YAAY,EAAE,IAAI;IACjD;IAEA,MAAM,kBAAkB,EAAU,EAAE,MAAe,EAAsB;QACvE,MAAM,SAAS,SAAS,CAAC,QAAQ,EAAE,QAAQ,GAAG;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAY,CAAC,YAAY,EAAE,GAAG,SAAS,EAAE,QAAQ;IACtE;IAEA,WAAW;IACX,MAAM,YAAY,UAA0B,CAAC,CAAC,EAAuC;QACnF,MAAM,SAAS,IAAI;QACnB,OAAO,OAAO,CAAC,SAAS,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC3C,IAAI,UAAU,aAAa,UAAU,MAAM;gBACzC,OAAO,MAAM,CAAC,KAAK,MAAM,QAAQ;YACnC;QACF;QAEA,OAAO,IAAI,CAAC,OAAO,CAA6B,CAAC,cAAc,EAAE,OAAO,QAAQ,IAAI;IACtF;IAEA,WAAW;IACX,MAAM,kBAAkB,OAAiB,EAAE,cAA2C,MAAM,EAAuF;QACjL,OAAO,IAAI,CAAC,OAAO,CAAC,iBAAiB;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAS;YAAY;QAC9C;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAAgB;QACtD,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,WAAW;IAClD;IAEA,iBAAiB;IACjB,MAAM,wBAAgD;QACpD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,yBAAiD;QACrD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,MAAM,0BAAkD;QACtD,OAAO,IAAI,CAAC,OAAO,CAAgB;IACrC;IAEA,OAAO;IACP,MAAM,cAA4H;QAChI,OAAO,IAAI,CAAC,OAAO,CAAC,aAAa;YAC/B,QAAQ;QACV;IACF;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/RequestForm.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { Send, Loader2, CheckCircle, AlertCircle, Info, Sparkles } from 'lucide-react';\nimport Button from './ui/Button';\nimport { apiClient } from '@/lib/api';\n\nconst RequestForm: React.FC = () => {\n  const [imdbIds, setImdbIds] = useState('');\n  const [contentType, setContentType] = useState<'auto' | 'movie' | 'series'>('auto');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');\n  const [message, setMessage] = useState('');\n  const [requestId, setRequestId] = useState<string | null>(null);\n\n  const validateImdbIds = (ids: string[]): { valid: string[]; invalid: string[] } => {\n    const valid: string[] = [];\n    const invalid: string[] = [];\n    \n    ids.forEach(id => {\n      const trimmedId = id.trim();\n      if (trimmedId.match(/^tt\\d{7,}$/)) {\n        valid.push(trimmedId);\n      } else if (trimmedId) {\n        invalid.push(trimmedId);\n      }\n    });\n    \n    return { valid, invalid };\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    if (!imdbIds.trim()) {\n      setStatus('error');\n      setMessage('Please enter at least one IMDb ID');\n      return;\n    }\n\n    const ids = imdbIds.split('\\n').map(id => id.trim()).filter(Boolean);\n    \n    if (ids.length === 0) {\n      setStatus('error');\n      setMessage('Please enter at least one IMDb ID');\n      return;\n    }\n\n    if (ids.length > 50) {\n      setStatus('error');\n      setMessage('Maximum 50 IMDb IDs allowed per request');\n      return;\n    }\n\n    const { valid, invalid } = validateImdbIds(ids);\n    \n    if (invalid.length > 0) {\n      setStatus('error');\n      setMessage(`Invalid IMDb IDs: ${invalid.join(', ')}. IMDb IDs should start with 'tt' followed by at least 7 digits.`);\n      return;\n    }\n\n    if (valid.length === 0) {\n      setStatus('error');\n      setMessage('No valid IMDb IDs found');\n      return;\n    }\n\n    setIsSubmitting(true);\n    setStatus('loading');\n    setMessage('Submitting request...');\n\n    try {\n      const response = await apiClient.createBulkRequest(valid, contentType);\n\n      setStatus('success');\n      setMessage(`Request submitted successfully! Processing ${response.totalCount} items as ${contentType === 'auto' ? 'auto-detected content' : contentType === 'movie' ? 'movies' : 'series'}.`);\n      setRequestId(response.requestId);\n      setImdbIds('');\n\n      // Start polling for status updates\n      pollRequestStatus(response.requestId);\n\n    } catch (error) {\n      setStatus('error');\n      setMessage('Failed to submit request. Please try again.');\n      console.error('Request submission error:', error);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const pollRequestStatus = async (id: string) => {\n    try {\n      const status = await apiClient.getRequestStatus(id);\n      \n      if (status.status === 'completed') {\n        setMessage(`Request completed! Successfully processed ${status.processedCount} out of ${status.totalCount} items.`);\n      } else if (status.status === 'failed') {\n        setMessage(`Request completed with errors. Processed ${status.processedCount} out of ${status.totalCount} items.`);\n      } else if (status.status === 'processing') {\n        setMessage(`Processing... ${status.processedCount} of ${status.totalCount} completed.`);\n        // Continue polling\n        setTimeout(() => pollRequestStatus(id), 5000);\n      }\n    } catch (error) {\n      console.error('Error polling request status:', error);\n    }\n  };\n\n  const getStatusIcon = () => {\n    switch (status) {\n      case 'loading':\n        return <Loader2 className=\"animate-spin\" size={20} />;\n      case 'success':\n        return <CheckCircle className=\"text-green-400\" size={20} />;\n      case 'error':\n        return <AlertCircle className=\"text-red-400\" size={20} />;\n      default:\n        return <Send size={20} />;\n    }\n  };\n\n  const getStatusColor = () => {\n    switch (status) {\n      case 'success':\n        return 'bg-gradient-to-br from-green-900/30 to-green-800/30 border-green-500/30 text-green-300';\n      case 'error':\n        return 'bg-gradient-to-br from-red-900/30 to-red-800/30 border-red-500/30 text-red-300';\n      case 'loading':\n        return 'bg-gradient-to-br from-blue-900/30 to-blue-800/30 border-blue-500/30 text-blue-300';\n      default:\n        return '';\n    }\n  };\n\n  return (\n    <div className=\"bg-gray-900/40 backdrop-blur-xl rounded-3xl p-8 border border-gray-800/50\">\n      <div className=\"flex items-center mb-8\">\n        <div className=\"w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-2xl flex items-center justify-center mr-4\">\n          <Send className=\"w-6 h-6 text-white\" />\n        </div>\n        <div>\n          <h2 className=\"text-white font-bold text-2xl\">Submit Request</h2>\n          <p className=\"text-gray-400 text-sm\">Add new content to StreamZen</p>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-8\">\n        {/* Content Type Selector */}\n        <div>\n          <label className=\"block text-white font-semibold text-lg mb-4\">\n            Content Type\n          </label>\n          <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n            <button\n              type=\"button\"\n              onClick={() => setContentType('auto')}\n              className={`group relative p-6 rounded-2xl border-2 transition-all duration-300 ${\n                contentType === 'auto'\n                  ? 'bg-gradient-to-br from-red-600/20 to-red-700/20 border-red-500 text-white shadow-lg shadow-red-500/20'\n                  : 'bg-gray-800/50 border-gray-700 text-gray-300 hover:bg-gray-800/70 hover:border-gray-600'\n              }`}\n            >\n              <div className=\"text-center\">\n                <div className=\"w-8 h-8 mx-auto mb-3 rounded-lg bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center\">\n                  <Sparkles className=\"w-4 h-4 text-white\" />\n                </div>\n                <div className=\"font-semibold text-base\">Auto Detect</div>\n                <div className=\"text-xs opacity-75 mt-2\">Recommended</div>\n              </div>\n              {contentType === 'auto' && (\n                <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center\">\n                  <CheckCircle className=\"w-4 h-4 text-white\" />\n                </div>\n              )}\n            </button>\n\n            <button\n              type=\"button\"\n              onClick={() => setContentType('movie')}\n              className={`group relative p-6 rounded-2xl border-2 transition-all duration-300 ${\n                contentType === 'movie'\n                  ? 'bg-gradient-to-br from-red-600/20 to-red-700/20 border-red-500 text-white shadow-lg shadow-red-500/20'\n                  : 'bg-gray-800/50 border-gray-700 text-gray-300 hover:bg-gray-800/70 hover:border-gray-600'\n              }`}\n            >\n              <div className=\"text-center\">\n                <div className=\"w-8 h-8 mx-auto mb-3 rounded-lg bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">🎬</span>\n                </div>\n                <div className=\"font-semibold text-base\">Movies Only</div>\n                <div className=\"text-xs opacity-75 mt-2\">Force movie type</div>\n              </div>\n              {contentType === 'movie' && (\n                <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center\">\n                  <CheckCircle className=\"w-4 h-4 text-white\" />\n                </div>\n              )}\n            </button>\n\n            <button\n              type=\"button\"\n              onClick={() => setContentType('series')}\n              className={`group relative p-6 rounded-2xl border-2 transition-all duration-300 ${\n                contentType === 'series'\n                  ? 'bg-gradient-to-br from-red-600/20 to-red-700/20 border-red-500 text-white shadow-lg shadow-red-500/20'\n                  : 'bg-gray-800/50 border-gray-700 text-gray-300 hover:bg-gray-800/70 hover:border-gray-600'\n              }`}\n            >\n              <div className=\"text-center\">\n                <div className=\"w-8 h-8 mx-auto mb-3 rounded-lg bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center\">\n                  <span className=\"text-white font-bold text-sm\">📺</span>\n                </div>\n                <div className=\"font-semibold text-base\">Series Only</div>\n                <div className=\"text-xs opacity-75 mt-2\">Force series type</div>\n              </div>\n              {contentType === 'series' && (\n                <div className=\"absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center\">\n                  <CheckCircle className=\"w-4 h-4 text-white\" />\n                </div>\n              )}\n            </button>\n          </div>\n\n          <div className=\"mt-4 p-4 bg-gray-800/30 rounded-xl\">\n            <p className=\"text-gray-300 text-sm leading-relaxed\">\n              {contentType === 'auto' && (\n                <>\n                  <span className=\"text-blue-400 font-medium\">Auto Detection:</span> Our system will automatically analyze each IMDb ID and determine whether it's a movie or TV series, then process accordingly with full metadata scraping.\n                </>\n              )}\n              {contentType === 'movie' && (\n                <>\n                  <span className=\"text-purple-400 font-medium\">Movies Only:</span> All provided IMDb IDs will be processed as movies with complete metadata including cast, genres, and streaming links.\n                </>\n              )}\n              {contentType === 'series' && (\n                <>\n                  <span className=\"text-green-400 font-medium\">Series Only:</span> All provided IMDb IDs will be processed as TV series with automatic season and episode detection, complete metadata scraping.\n                </>\n              )}\n            </p>\n          </div>\n        </div>\n\n        <div>\n          <label htmlFor=\"imdbIds\" className=\"block text-white font-semibold text-lg mb-4\">\n            IMDb IDs\n          </label>\n          <div className=\"relative\">\n            <textarea\n              id=\"imdbIds\"\n              value={imdbIds}\n              onChange={(e) => setImdbIds(e.target.value)}\n              placeholder=\"Enter IMDb IDs, one per line&#10;&#10;Examples:&#10;tt0111161 (The Shawshank Redemption)&#10;tt0068646 (The Godfather)&#10;tt0944947 (Game of Thrones)&#10;tt1316554 (Black Butler)\"\n              className=\"w-full h-40 px-4 py-4 bg-gray-800/50 border-2 border-gray-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none transition-all duration-200 text-sm leading-relaxed\"\n              disabled={isSubmitting}\n            />\n            <div className=\"absolute bottom-4 right-4 flex items-center space-x-2\">\n              <div className={`px-3 py-1 rounded-full text-xs font-medium ${\n                imdbIds.split('\\n').filter(Boolean).length > 50\n                  ? 'bg-red-500/20 text-red-400 border border-red-500/30'\n                  : imdbIds.split('\\n').filter(Boolean).length > 40\n                  ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30'\n                  : 'bg-gray-700/50 text-gray-400 border border-gray-600/30'\n              }`}>\n                {imdbIds.split('\\n').filter(Boolean).length} / 50\n              </div>\n            </div>\n          </div>\n          <div className=\"mt-3 flex items-center justify-between text-sm\">\n            <div className=\"flex items-center space-x-2 text-gray-400\">\n              <Info size={16} />\n              <span>One IMDb ID per line • Maximum 50 IDs per request</span>\n            </div>\n          </div>\n        </div>\n\n        <Button\n          type=\"submit\"\n          disabled={isSubmitting || !imdbIds.trim() || imdbIds.split('\\n').filter(Boolean).length > 50}\n          className=\"w-full h-14 flex items-center justify-center space-x-3 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 disabled:from-gray-700 disabled:to-gray-800 text-white font-semibold text-lg rounded-2xl transition-all duration-200 shadow-lg hover:shadow-xl disabled:shadow-none\"\n        >\n          {getStatusIcon()}\n          <span>{isSubmitting ? 'Processing Request...' : 'Submit Request'}</span>\n        </Button>\n      </form>\n\n      {message && (\n        <div className={`mt-6 p-6 rounded-2xl border-2 ${getStatusColor()}`}>\n          <div className=\"flex items-start space-x-3\">\n            {getStatusIcon()}\n            <div className=\"flex-1\">\n              <p className=\"text-sm leading-relaxed\">{message}</p>\n              {requestId && (\n                <div className=\"mt-3 p-3 bg-black/20 rounded-lg\">\n                  <p className=\"text-xs opacity-75 font-mono\">\n                    Request ID: {requestId}\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"mt-8 p-6 bg-gray-800/30 rounded-2xl border border-gray-700/50\">\n        <div className=\"flex items-center mb-4\">\n          <div className=\"w-8 h-8 bg-blue-600/20 rounded-lg flex items-center justify-center mr-3\">\n            <Info className=\"w-4 h-4 text-blue-400\" />\n          </div>\n          <h3 className=\"text-white font-semibold text-lg\">Quick Tips</h3>\n        </div>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm text-gray-300\">\n          <div className=\"space-y-3\">\n            <div className=\"flex items-start\">\n              <div className=\"w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n              <span>IMDb IDs start with \"tt\" + 7+ digits (e.g., tt0111161)</span>\n            </div>\n            <div className=\"flex items-start\">\n              <div className=\"w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n              <span>Find IMDb IDs in any IMDb page URL</span>\n            </div>\n          </div>\n          <div className=\"space-y-3\">\n            <div className=\"flex items-start\">\n              <div className=\"w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n              <span>Maximum 50 IDs per request</span>\n            </div>\n            <div className=\"flex items-start\">\n              <div className=\"w-1.5 h-1.5 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0\"></div>\n              <span>Processing: 2-5 minutes per item</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RequestForm;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAOA,MAAM,cAAwB;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAC5E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4C;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE1D,MAAM,kBAAkB,CAAC;QACvB,MAAM,QAAkB,EAAE;QAC1B,MAAM,UAAoB,EAAE;QAE5B,IAAI,OAAO,CAAC,CAAA;YACV,MAAM,YAAY,GAAG,IAAI;YACzB,IAAI,UAAU,KAAK,CAAC,eAAe;gBACjC,MAAM,IAAI,CAAC;YACb,OAAO,IAAI,WAAW;gBACpB,QAAQ,IAAI,CAAC;YACf;QACF;QAEA,OAAO;YAAE;YAAO;QAAQ;IAC1B;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,UAAU;YACV,WAAW;YACX;QACF;QAEA,MAAM,MAAM,QAAQ,KAAK,CAAC,MAAM,GAAG,CAAC,CAAA,KAAM,GAAG,IAAI,IAAI,MAAM,CAAC;QAE5D,IAAI,IAAI,MAAM,KAAK,GAAG;YACpB,UAAU;YACV,WAAW;YACX;QACF;QAEA,IAAI,IAAI,MAAM,GAAG,IAAI;YACnB,UAAU;YACV,WAAW;YACX;QACF;QAEA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,gBAAgB;QAE3C,IAAI,QAAQ,MAAM,GAAG,GAAG;YACtB,UAAU;YACV,WAAW,CAAC,kBAAkB,EAAE,QAAQ,IAAI,CAAC,MAAM,gEAAgE,CAAC;YACpH;QACF;QAEA,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB,UAAU;YACV,WAAW;YACX;QACF;QAEA,gBAAgB;QAChB,UAAU;QACV,WAAW;QAEX,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,iBAAiB,CAAC,OAAO;YAE1D,UAAU;YACV,WAAW,CAAC,2CAA2C,EAAE,SAAS,UAAU,CAAC,UAAU,EAAE,gBAAgB,SAAS,0BAA0B,gBAAgB,UAAU,WAAW,SAAS,CAAC,CAAC;YAC5L,aAAa,SAAS,SAAS;YAC/B,WAAW;YAEX,mCAAmC;YACnC,kBAAkB,SAAS,SAAS;QAEtC,EAAE,OAAO,OAAO;YACd,UAAU;YACV,WAAW;YACX,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,SAAS,MAAM,iHAAA,CAAA,YAAS,CAAC,gBAAgB,CAAC;YAEhD,IAAI,OAAO,MAAM,KAAK,aAAa;gBACjC,WAAW,CAAC,0CAA0C,EAAE,OAAO,cAAc,CAAC,QAAQ,EAAE,OAAO,UAAU,CAAC,OAAO,CAAC;YACpH,OAAO,IAAI,OAAO,MAAM,KAAK,UAAU;gBACrC,WAAW,CAAC,yCAAyC,EAAE,OAAO,cAAc,CAAC,QAAQ,EAAE,OAAO,UAAU,CAAC,OAAO,CAAC;YACnH,OAAO,IAAI,OAAO,MAAM,KAAK,cAAc;gBACzC,WAAW,CAAC,cAAc,EAAE,OAAO,cAAc,CAAC,IAAI,EAAE,OAAO,UAAU,CAAC,WAAW,CAAC;gBACtF,mBAAmB;gBACnB,WAAW,IAAM,kBAAkB,KAAK;YAC1C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACrD;gBACE,qBAAO,8OAAC,kMAAA,CAAA,OAAI;oBAAC,MAAM;;;;;;QACvB;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;kCAElB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;0BAIzC,8OAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAA8C;;;;;;0CAG/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,oEAAoE,EAC9E,gBAAgB,SACZ,0GACA,2FACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;4CAE1C,gBAAgB,wBACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK7B,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,oEAAoE,EAC9E,gBAAgB,UACZ,0GACA,2FACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;kEAEjD,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;4CAE1C,gBAAgB,yBACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK7B,8OAAC;wCACC,MAAK;wCACL,SAAS,IAAM,eAAe;wCAC9B,WAAW,CAAC,oEAAoE,EAC9E,gBAAgB,WACZ,0GACA,2FACJ;;0DAEF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA+B;;;;;;;;;;;kEAEjD,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;kEACzC,8OAAC;wDAAI,WAAU;kEAA0B;;;;;;;;;;;;4CAE1C,gBAAgB,0BACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0CAM/B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCACV,gBAAgB,wBACf;;8DACE,8OAAC;oDAAK,WAAU;8DAA4B;;;;;;gDAAsB;;;wCAGrE,gBAAgB,yBACf;;8DACE,8OAAC;oDAAK,WAAU;8DAA8B;;;;;;gDAAmB;;;wCAGpE,gBAAgB,0BACf;;8DACE,8OAAC;oDAAK,WAAU;8DAA6B;;;;;;gDAAmB;;;;;;;;;;;;;;;;;;;;kCAO1E,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAU,WAAU;0CAA8C;;;;;;0CAGjF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wCAC1C,aAAY;wCACZ,WAAU;wCACV,UAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,CAAC,2CAA2C,EAC1D,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,SAAS,MAAM,GAAG,KACzC,wDACA,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,SAAS,MAAM,GAAG,KAC7C,iEACA,0DACJ;;gDACC,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,SAAS,MAAM;gDAAC;;;;;;;;;;;;;;;;;;0CAIlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,MAAM;;;;;;sDACZ,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,8OAAC,kIAAA,CAAA,UAAM;wBACL,MAAK;wBACL,UAAU,gBAAgB,CAAC,QAAQ,IAAI,MAAM,QAAQ,KAAK,CAAC,MAAM,MAAM,CAAC,SAAS,MAAM,GAAG;wBAC1F,WAAU;;4BAET;0CACD,8OAAC;0CAAM,eAAe,0BAA0B;;;;;;;;;;;;;;;;;;YAInD,yBACC,8OAAC;gBAAI,WAAW,CAAC,8BAA8B,EAAE,kBAAkB;0BACjE,cAAA,8OAAC;oBAAI,WAAU;;wBACZ;sCACD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAA2B;;;;;;gCACvC,2BACC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;;4CAA+B;4CAC7B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;;;;;;;kCAEnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAGV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;uCAEe", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/RequestHistory.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Clock, CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';\nimport Button from './ui/Button';\nimport { cn } from '@/lib/utils';\n\ninterface RequestStatus {\n  _id: string;\n  imdbIds: string[];\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  processedCount: number;\n  totalCount: number;\n  failedIds: string[];\n  errorMessages: string[];\n  createdAt: string;\n  updatedAt: string;\n  completedAt?: string;\n}\n\nconst RequestHistory: React.FC = () => {\n  const [requests, setRequests] = useState<RequestStatus[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Load requests from localStorage on component mount\n  useEffect(() => {\n    const savedRequests = localStorage.getItem('streamzen_requests');\n    if (savedRequests) {\n      try {\n        const parsed = JSON.parse(savedRequests);\n        setRequests(parsed);\n      } catch (error) {\n        console.error('Error parsing saved requests:', error);\n      }\n    }\n  }, []);\n\n  // Save requests to localStorage whenever requests change\n  useEffect(() => {\n    if (requests.length > 0) {\n      localStorage.setItem('streamzen_requests', JSON.stringify(requests));\n    }\n  }, [requests]);\n\n  const refreshRequest = async (requestId: string) => {\n    setIsLoading(true);\n    try {\n      const response = await fetch(`/api/requests/${requestId}`);\n      if (response.ok) {\n        const updatedRequest = await response.json();\n        setRequests(prev => \n          prev.map(req => req._id === requestId ? updatedRequest : req)\n        );\n      }\n    } catch (error) {\n      console.error('Error refreshing request:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return <Clock className=\"text-yellow-400\" size={16} />;\n      case 'processing':\n        return <Loader2 className=\"text-blue-400 animate-spin\" size={16} />;\n      case 'completed':\n        return <CheckCircle className=\"text-green-400\" size={16} />;\n      case 'failed':\n        return <XCircle className=\"text-red-400\" size={16} />;\n      default:\n        return <Clock className=\"text-gray-400\" size={16} />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'pending':\n        return 'text-yellow-400';\n      case 'processing':\n        return 'text-blue-400';\n      case 'completed':\n        return 'text-green-400';\n      case 'failed':\n        return 'text-red-400';\n      default:\n        return 'text-gray-400';\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleString('en-US', {\n      month: 'short',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  const getProgressPercentage = (request: RequestStatus) => {\n    return Math.round((request.processedCount / request.totalCount) * 100);\n  };\n\n  // Add new request to history (called from parent component)\n  const addRequest = (request: RequestStatus) => {\n    setRequests(prev => [request, ...prev.slice(0, 9)]); // Keep only last 10 requests\n  };\n\n  // Expose addRequest function to parent\n  React.useEffect(() => {\n    (window as any).addRequestToHistory = addRequest;\n    return () => {\n      delete (window as any).addRequestToHistory;\n    };\n  }, []);\n\n  if (requests.length === 0) {\n    return (\n      <div className=\"bg-gray-900/40 backdrop-blur-xl rounded-3xl p-8 border border-gray-800/50\">\n        <div className=\"flex items-center mb-8\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mr-4\">\n            <Clock className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-white font-bold text-2xl\">Request History</h2>\n            <p className=\"text-gray-400 text-sm\">Track your submission history</p>\n          </div>\n        </div>\n        <div className=\"text-center py-12\">\n          <div className=\"w-20 h-20 bg-gray-800/50 rounded-3xl flex items-center justify-center mx-auto mb-6\">\n            <Clock className=\"text-gray-400\" size={32} />\n          </div>\n          <h3 className=\"text-white font-semibold text-lg mb-2\">No requests yet</h3>\n          <p className=\"text-gray-400 text-sm leading-relaxed max-w-sm mx-auto\">\n            Submit your first content request to see processing status and history here\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-gray-900/40 backdrop-blur-xl rounded-3xl p-8 border border-gray-800/50\">\n      <div className=\"flex items-center justify-between mb-8\">\n        <div className=\"flex items-center\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mr-4\">\n            <Clock className=\"w-6 h-6 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-white font-bold text-2xl\">Request History</h2>\n            <p className=\"text-gray-400 text-sm\">Track your submission history</p>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"px-3 py-1 bg-gray-800/50 rounded-full\">\n            <span className=\"text-gray-300 text-sm font-medium\">{requests.length} requests</span>\n          </div>\n          <Button\n            onClick={() => window.location.reload()}\n            variant=\"ghost\"\n            size=\"sm\"\n            className=\"flex items-center space-x-2 bg-gray-800/50 hover:bg-gray-800/70 rounded-xl px-3 py-2\"\n          >\n            <RefreshCw size={16} />\n            <span>Refresh</span>\n          </Button>\n        </div>\n      </div>\n\n      <div className=\"space-y-6 max-h-[500px] overflow-y-auto custom-scrollbar\">\n        {requests.map((request) => (\n          <div\n            key={request._id}\n            className=\"bg-gray-800/30 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 hover:bg-gray-800/40 transition-all duration-200\"\n          >\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-8 h-8 bg-gray-700/50 rounded-xl flex items-center justify-center\">\n                  {getStatusIcon(request.status)}\n                </div>\n                <div>\n                  <span className={cn('font-semibold text-sm capitalize', getStatusColor(request.status))}>\n                    {request.status}\n                  </span>\n                  <div className=\"text-gray-400 text-xs mt-0.5\">\n                    {formatDate(request.createdAt)}\n                  </div>\n                </div>\n              </div>\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"text-right\">\n                  <div className=\"text-white font-medium text-sm\">\n                    {request.processedCount}/{request.totalCount}\n                  </div>\n                  <div className=\"text-gray-400 text-xs\">\n                    {getProgressPercentage(request)}% complete\n                  </div>\n                </div>\n                {(request.status === 'processing' || request.status === 'pending') && (\n                  <Button\n                    onClick={() => refreshRequest(request._id)}\n                    disabled={isLoading}\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className=\"w-8 h-8 p-0 bg-gray-700/50 hover:bg-gray-700/70 rounded-lg\"\n                  >\n                    <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />\n                  </Button>\n                )}\n              </div>\n            </div>\n\n            <div className=\"mb-4\">\n              <div className=\"w-full bg-gray-700/50 rounded-full h-3 overflow-hidden\">\n                <div\n                  className={cn(\n                    'h-3 rounded-full transition-all duration-500 relative',\n                    request.status === 'completed' ? 'bg-gradient-to-r from-green-500 to-green-600' :\n                    request.status === 'failed' ? 'bg-gradient-to-r from-red-500 to-red-600' :\n                    request.status === 'processing' ? 'bg-gradient-to-r from-blue-500 to-blue-600' : 'bg-gradient-to-r from-yellow-500 to-yellow-600'\n                  )}\n                  style={{ width: `${getProgressPercentage(request)}%` }}\n                >\n                  {request.status === 'processing' && (\n                    <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\" />\n                  )}\n                </div>\n              </div>\n            </div>\n\n            <div className=\"flex items-center justify-between text-sm\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                  <span className=\"text-gray-300\">{request.totalCount} items</span>\n                </div>\n                {request.failedIds.length > 0 && (\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\n                    <span className=\"text-red-400\">{request.failedIds.length} failed</span>\n                  </div>\n                )}\n              </div>\n              <div className=\"text-gray-400 text-xs\">\n                ID: {request._id.slice(-8)}\n              </div>\n              {request.completedAt && (\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  <span className=\"text-green-400 text-xs\">\n                    Completed: {formatDate(request.completedAt)}\n                  </span>\n                </div>\n              )}\n            </div>\n\n            {request.errorMessages.length > 0 && (\n              <div className=\"mt-4 p-4 bg-red-900/20 border border-red-500/30 rounded-xl\">\n                <div className=\"flex items-center space-x-2 mb-3\">\n                  <div className=\"w-4 h-4 bg-red-500/20 rounded-full flex items-center justify-center\">\n                    <div className=\"w-2 h-2 bg-red-500 rounded-full\"></div>\n                  </div>\n                  <span className=\"text-red-400 font-medium text-sm\">Processing Errors</span>\n                </div>\n                <ul className=\"space-y-2 text-red-300 text-xs\">\n                  {request.errorMessages.slice(0, 3).map((error, index) => (\n                    <li key={index} className=\"flex items-start space-x-2\">\n                      <div className=\"w-1 h-1 bg-red-400 rounded-full mt-1.5 flex-shrink-0\"></div>\n                      <span>{error}</span>\n                    </li>\n                  ))}\n                  {request.errorMessages.length > 3 && (\n                    <li className=\"flex items-start space-x-2\">\n                      <div className=\"w-1 h-1 bg-red-400 rounded-full mt-1.5 flex-shrink-0\"></div>\n                      <span className=\"italic\">... and {request.errorMessages.length - 3} more errors</span>\n                    </li>\n                  )}\n                </ul>\n              </div>\n            )}\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nexport default RequestHistory;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AALA;;;;;;AAoBA,MAAM,iBAA2B;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qDAAqD;IACrD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,eAAe;YACjB,IAAI;gBACF,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD;QACF;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;QAC5D;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,iBAAiB,OAAO;QAC5B,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW;YACzD,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,iBAAiB,MAAM,SAAS,IAAI;gBAC1C,YAAY,CAAA,OACV,KAAK,GAAG,CAAC,CAAA,MAAO,IAAI,GAAG,KAAK,YAAY,iBAAiB;YAE7D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAkB,MAAM;;;;;;YAClD,KAAK;gBACH,qBAAO,8OAAC,iNAAA,CAAA,UAAO;oBAAC,WAAU;oBAA6B,MAAM;;;;;;YAC/D,KAAK;gBACH,qBAAO,8OAAC,2NAAA,CAAA,cAAW;oBAAC,WAAU;oBAAiB,MAAM;;;;;;YACvD,KAAK;gBACH,qBAAO,8OAAC,4MAAA,CAAA,UAAO;oBAAC,WAAU;oBAAe,MAAM;;;;;;YACjD;gBACE,qBAAO,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;oBAAgB,MAAM;;;;;;QAClD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,cAAc,CAAC,SAAS;YAClD,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,KAAK,KAAK,CAAC,AAAC,QAAQ,cAAc,GAAG,QAAQ,UAAU,GAAI;IACpE;IAEA,4DAA4D;IAC5D,MAAM,aAAa,CAAC;QAClB,YAAY,CAAA,OAAQ;gBAAC;mBAAY,KAAK,KAAK,CAAC,GAAG;aAAG,GAAG,6BAA6B;IACpF;IAEA,uCAAuC;IACvC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACb,OAAe,mBAAmB,GAAG;QACtC,OAAO;YACL,OAAO,AAAC,OAAe,mBAAmB;QAC5C;IACF,GAAG,EAAE;IAEL,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;8CAC9C,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;8BAGzC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;gCAAgB,MAAM;;;;;;;;;;;sCAEzC,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAyD;;;;;;;;;;;;;;;;;;IAM9E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAGzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCAAqC,SAAS,MAAM;wCAAC;;;;;;;;;;;;0CAEvE,8OAAC,kIAAA,CAAA,UAAM;gCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;gCACrC,SAAQ;gCACR,MAAK;gCACL,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,MAAM;;;;;;kDACjB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,cAAc,QAAQ,MAAM;;;;;;0DAE/B,8OAAC;;kEACC,8OAAC;wDAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oCAAoC,eAAe,QAAQ,MAAM;kEAClF,QAAQ,MAAM;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;kEACZ,WAAW,QAAQ,SAAS;;;;;;;;;;;;;;;;;;kDAInC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;4DACZ,QAAQ,cAAc;4DAAC;4DAAE,QAAQ,UAAU;;;;;;;kEAE9C,8OAAC;wDAAI,WAAU;;4DACZ,sBAAsB;4DAAS;;;;;;;;;;;;;4CAGnC,CAAC,QAAQ,MAAM,KAAK,gBAAgB,QAAQ,MAAM,KAAK,SAAS,mBAC/D,8OAAC,kIAAA,CAAA,UAAM;gDACL,SAAS,IAAM,eAAe,QAAQ,GAAG;gDACzC,UAAU;gDACV,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAEV,cAAA,8OAAC,gNAAA,CAAA,YAAS;oDAAC,MAAM;oDAAI,WAAW,YAAY,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA,QAAQ,MAAM,KAAK,cAAc,iDACjC,QAAQ,MAAM,KAAK,WAAW,6CAC9B,QAAQ,MAAM,KAAK,eAAe,+CAA+C;wCAEnF,OAAO;4CAAE,OAAO,GAAG,sBAAsB,SAAS,CAAC,CAAC;wCAAC;kDAEpD,QAAQ,MAAM,KAAK,8BAClB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;0CAMvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;;4DAAiB,QAAQ,UAAU;4DAAC;;;;;;;;;;;;;4CAErD,QAAQ,SAAS,CAAC,MAAM,GAAG,mBAC1B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;;4DAAgB,QAAQ,SAAS,CAAC,MAAM;4DAAC;;;;;;;;;;;;;;;;;;;kDAI/D,8OAAC;wCAAI,WAAU;;4CAAwB;4CAChC,QAAQ,GAAG,CAAC,KAAK,CAAC,CAAC;;;;;;;oCAEzB,QAAQ,WAAW,kBAClB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;;oDAAyB;oDAC3B,WAAW,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;4BAMjD,QAAQ,aAAa,CAAC,MAAM,GAAG,mBAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAK,WAAU;0DAAmC;;;;;;;;;;;;kDAErD,8OAAC;wCAAG,WAAU;;4CACX,QAAQ,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC7C,8OAAC;oDAAe,WAAU;;sEACxB,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;sEAAM;;;;;;;mDAFA;;;;;4CAKV,QAAQ,aAAa,CAAC,MAAM,GAAG,mBAC9B,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;;4DAAS;4DAAS,QAAQ,aAAa,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;uBAtGxE,QAAQ,GAAG;;;;;;;;;;;;;;;;AAiH5B;uCAEe", "debugId": null}}]}