module.exports = {

"[project]/.next-internal/server/app/api/series/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Movie.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MovieSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    title: {
        type: String,
        required: true,
        index: true
    },
    year: {
        type: Number,
        required: true,
        index: true
    },
    rating: String,
    runtime: String,
    imdbRating: {
        type: Number,
        index: true
    },
    imdbVotes: String,
    popularity: {
        type: Number,
        index: true
    },
    popularityDelta: Number,
    posterUrl: String,
    trailerUrl: String,
    trailerRuntime: String,
    trailerLikes: String,
    description: String,
    genres: [
        {
            type: String,
            index: true
        }
    ],
    director: String,
    cast: [
        String
    ],
    language: {
        type: String,
        index: true
    },
    country: {
        type: String,
        index: true
    },
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    quality: {
        type: String,
        index: true
    }
}, {
    timestamps: true
});
// Compound indexes for better query performance
MovieSchema.index({
    year: -1,
    imdbRating: -1
});
MovieSchema.index({
    genres: 1,
    year: -1
});
// Removed text index to avoid language override issues
MovieSchema.index({
    title: 1
});
MovieSchema.index({
    language: 1,
    country: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Movie || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Movie', MovieSchema);
}}),
"[project]/src/models/Series.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const SeriesSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    title: {
        type: String,
        required: true,
        index: true
    },
    startYear: {
        type: Number,
        required: true,
        index: true
    },
    endYear: {
        type: Number,
        index: true
    },
    rating: String,
    imdbRating: {
        type: Number,
        index: true
    },
    imdbVotes: String,
    popularity: {
        type: Number,
        index: true
    },
    popularityDelta: Number,
    posterUrl: String,
    trailerUrl: String,
    description: String,
    genres: [
        {
            type: String,
            index: true
        }
    ],
    creator: String,
    cast: [
        String
    ],
    language: {
        type: String,
        index: true
    },
    country: {
        type: String,
        index: true
    },
    totalSeasons: Number,
    status: {
        type: String,
        enum: [
            'ongoing',
            'ended',
            'cancelled'
        ],
        index: true
    },
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String // VidSrc TMDB embed URL
}, {
    timestamps: true
});
// Compound indexes for better query performance
SeriesSchema.index({
    startYear: -1,
    imdbRating: -1
});
SeriesSchema.index({
    genres: 1,
    startYear: -1
});
SeriesSchema.index({
    status: 1,
    startYear: -1
});
// Removed text index to avoid language override issues
SeriesSchema.index({
    title: 1
});
SeriesSchema.index({
    language: 1,
    country: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Series || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Series', SeriesSchema);
}}),
"[project]/src/models/Episode.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const EpisodeSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    seriesTitle: {
        type: String,
        required: true,
        index: true
    },
    season: {
        type: Number,
        required: true,
        index: true
    },
    episode: {
        type: Number,
        required: true,
        index: true
    },
    episodeTitle: String,
    airDate: {
        type: Date,
        index: true
    },
    runtime: String,
    imdbRating: Number,
    description: String,
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    quality: {
        type: String,
        index: true
    },
    genres: [
        {
            type: String,
            index: true
        }
    ]
}, {
    timestamps: true
});
// Compound indexes for better query performance
EpisodeSchema.index({
    imdbId: 1,
    season: 1,
    episode: 1
}, {
    unique: true
});
EpisodeSchema.index({
    airDate: -1
});
EpisodeSchema.index({
    seriesTitle: 1,
    season: 1,
    episode: 1
});
EpisodeSchema.index({
    createdAt: -1
}); // For latest episodes
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Episode || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Episode', EpisodeSchema);
}}),
"[project]/src/models/Request.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const RequestSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbIds: [
        {
            type: String,
            required: true
        }
    ],
    status: {
        type: String,
        enum: [
            'pending',
            'processing',
            'completed',
            'failed'
        ],
        default: 'pending',
        index: true
    },
    processedCount: {
        type: Number,
        default: 0
    },
    totalCount: {
        type: Number,
        required: true
    },
    failedIds: [
        String
    ],
    errorMessages: [
        String
    ],
    submittedBy: {
        type: String,
        index: true
    },
    contentType: {
        type: String,
        enum: [
            'auto',
            'movie',
            'series'
        ],
        default: 'auto'
    }
}, {
    timestamps: true
});
// Index for querying pending requests
RequestSchema.index({
    status: 1,
    createdAt: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Request || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Request', RequestSchema);
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/node:assert [external] (node:assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:assert", () => require("node:assert"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:querystring [external] (node:querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:querystring", () => require("node:querystring"));

module.exports = mod;
}}),
"[externals]/node:events [external] (node:events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:events", () => require("node:events"));

module.exports = mod;
}}),
"[externals]/node:diagnostics_channel [external] (node:diagnostics_channel, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:diagnostics_channel", () => require("node:diagnostics_channel"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:tls [external] (node:tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:tls", () => require("node:tls"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:util/types [external] (node:util/types, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util/types", () => require("node:util/types"));

module.exports = mod;
}}),
"[externals]/node:worker_threads [external] (node:worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:worker_threads", () => require("node:worker_threads"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/node:http2 [external] (node:http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http2", () => require("node:http2"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:console [external] (node:console, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:console", () => require("node:console"));

module.exports = mod;
}}),
"[externals]/node:dns [external] (node:dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:dns", () => require("node:dns"));

module.exports = mod;
}}),
"[project]/src/lib/scraper.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/load-parse.js [app-route] (ecmascript)");
;
;
class IMDbScraper {
    static instance;
    requestCount = 0;
    lastRequestTime = 0;
    RATE_LIMIT = 30;
    MIN_DELAY = 2000;
    static getInstance() {
        if (!IMDbScraper.instance) {
            IMDbScraper.instance = new IMDbScraper();
        }
        return IMDbScraper.instance;
    }
    async rateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        if (timeSinceLastRequest < this.MIN_DELAY) {
            const delay = this.MIN_DELAY - timeSinceLastRequest;
            // Add random jitter to avoid detection patterns
            const jitter = Math.random() * 1000; // 0-1000ms random delay
            await new Promise((resolve)=>setTimeout(resolve, delay + jitter));
        }
        this.lastRequestTime = Date.now();
        this.requestCount++;
    }
    getRandomUserAgent() {
        const userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'
        ];
        return userAgents[Math.floor(Math.random() * userAgents.length)];
    }
    async fetchPage(imdbId) {
        await this.rateLimit();
        const url = `https://www.imdb.com/title/${imdbId}/`;
        const headers = {
            'User-Agent': this.getRandomUserAgent(),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Cache-Control': 'max-age=0'
        };
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers,
                timeout: 30000
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
        } catch (error) {
            console.error(`Error fetching IMDb page for ${imdbId}:`, error);
            throw new Error(`Failed to fetch IMDb page: ${error.message}`);
        }
    }
    extractBasicInfo($) {
        const titleElement = $('h1[data-testid="hero__pageTitle"] span[data-testid="hero__primary-text"]');
        const title = titleElement.text().trim();
        if (!title) {
            throw new Error('Could not extract title from IMDb page');
        }
        // Extract year from the release info
        const yearElement = $('ul.ipc-inline-list a[href*="/releaseinfo/"]');
        const yearText = yearElement.text().trim();
        const year = parseInt(yearText) || new Date().getFullYear();
        // Determine if it's a movie or series
        const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();
        const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');
        return {
            title,
            year,
            type: isMovie ? 'movie' : 'series'
        };
    }
    extractRating($) {
        const ratingElement = $('ul.ipc-inline-list a[href*="/parentalguide/"]');
        return ratingElement.text().trim() || undefined;
    }
    extractRuntime($) {
        const runtimeElements = $('ul.ipc-inline-list li');
        for(let i = 0; i < runtimeElements.length; i++){
            const text = $(runtimeElements[i]).text().trim();
            if (text.includes('h') || text.includes('min')) {
                return text;
            }
        }
        return undefined;
    }
    extractIMDbRating($) {
        const ratingElement = $('div[data-testid="hero-rating-bar__aggregate-rating__score"] span');
        const rating = parseFloat(ratingElement.text().trim()) || undefined;
        const votesElement = $('div.sc-d541859f-3');
        const votes = votesElement.text().trim() || undefined;
        return {
            rating,
            votes
        };
    }
    extractPopularity($) {
        const popularityElement = $('div[data-testid="hero-rating-bar__popularity__score"]');
        const popularity = parseInt(popularityElement.text().trim()) || undefined;
        const deltaElement = $('div[data-testid="hero-rating-bar__popularity__delta"]');
        const deltaText = deltaElement.text().trim();
        const delta = deltaText ? parseInt(deltaText.replace(/[^\d-]/g, '')) : undefined;
        return {
            popularity,
            delta
        };
    }
    extractPosterUrl($) {
        // Try multiple selectors for poster image
        const selectors = [
            'div[data-testid="hero-media__poster"] img',
            '.ipc-image[data-testid="hero-media__poster"]',
            '.poster img',
            '.ipc-media img',
            'img[class*="poster"]',
            'a[class*="ipc-lockup-overlay"] img'
        ];
        for (const selector of selectors){
            const element = $(selector);
            const src = element.attr('src');
            if (src && src.includes('media-amazon.com')) {
                // Clean up the URL to get high quality image
                return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1000_.').replace(/\._.*?\./, '._V1_FMjpg_UX1000_.');
            }
        }
        return undefined;
    }
    extractBackdropUrl($) {
        // Try to extract backdrop/hero image
        const selectors = [
            '.hero-media__slate-overlay img',
            '.slate img',
            '.hero__background img',
            'div[data-testid="hero-media"] img'
        ];
        for (const selector of selectors){
            const element = $(selector);
            const src = element.attr('src');
            if (src && src.includes('media-amazon.com')) {
                return src.replace(/\._.*?_\./, '._V1_FMjpg_UX1920_.');
            }
        }
        return undefined;
    }
    extractTrailerInfo($) {
        const trailerElement = $('a[data-testid="video-player-slate-overlay"]');
        const url = trailerElement.attr('href') || undefined;
        const runtimeElement = $('span[data-testid="video-player-slate-runtime"]');
        const runtime = runtimeElement.text().trim() || undefined;
        const likesElement = $('span.ipc-reaction-summary__label');
        const likes = likesElement.text().trim() || undefined;
        return {
            url,
            runtime,
            likes
        };
    }
    extractDescription($) {
        // Try multiple selectors for description
        const selectors = [
            'div[data-testid="hero-media__slate"] img',
            'span[data-testid="plot-xl"]',
            'span[data-testid="plot-l"]',
            'span[data-testid="plot"]'
        ];
        for (const selector of selectors){
            const element = $(selector);
            if (selector.includes('img')) {
                const alt = element.attr('alt');
                if (alt && alt.length > 20) return alt;
            } else {
                const text = element.text().trim();
                if (text && text.length > 10) return text;
            }
        }
        return undefined;
    }
    extractGenres($) {
        const genres = [];
        // Updated genre selectors based on current IMDb structure
        const genreSelectors = [
            // Current IMDb hero section genres (most common)
            '[data-testid="genres"] .ipc-chip .ipc-chip__text',
            '[data-testid="genres"] .ipc-chip__text',
            '[data-testid="genres"] a .ipc-chip__text',
            // Hero section alternative formats
            '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',
            '.GenresAndPlot__GenreChip .ipc-chip__text',
            // Storyline section (your provided structure - keep as fallback)
            'li[data-testid="storyline-genres"] .ipc-metadata-list-item__list-content-item',
            'li[data-testid="storyline-genres"] a[href*="genres="]',
            // General genre link selectors (broader search)
            'a[href*="/search/title/?genres="] span',
            'a[href*="genres="] span',
            'a[href*="genres="]',
            // Schema.org microdata
            '[itemprop="genre"]',
            'span[itemprop="genre"]',
            // Fallback selectors
            '.see-more.inline.canwrap a[href*="genres="]',
            '.titlePageSprite.star-box-giga-star + div a[href*="genres="]',
            // Very broad fallback - any link with genre in URL
            'a[href*="explore=genres"]'
        ];
        console.log(`🔍 Starting genre extraction...`);
        for(let i = 0; i < genreSelectors.length; i++){
            const selector = genreSelectors[i];
            const elements = $(selector);
            console.log(`🔍 Selector ${i + 1}/${genreSelectors.length}: "${selector}" found ${elements.length} elements`);
            if (elements.length > 0) {
                elements.each((_, element)=>{
                    const genre = $(element).text().trim();
                    console.log(`📝 Found genre text: "${genre}"`);
                    // Clean up genre text and validate
                    if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {
                        // Skip common non-genre text
                        const skipTexts = [
                            'Genres',
                            'Genre',
                            'See all',
                            'More',
                            'All',
                            '...'
                        ];
                        if (!skipTexts.includes(genre)) {
                            genres.push(genre);
                            console.log(`✅ Added genre: "${genre}"`);
                        }
                    }
                });
                if (genres.length > 0) {
                    console.log(`✅ Successfully extracted ${genres.length} genres: [${genres.join(', ')}]`);
                    break; // Use the first selector that finds results
                }
            }
        }
        if (genres.length === 0) {
            console.log('⚠️ No genres found with any selector');
            // Debug: Let's see what's actually in the storyline section
            const storylineSection = $('li[data-testid="storyline-genres"]');
            if (storylineSection.length > 0) {
                console.log('📋 Storyline section HTML:', storylineSection.html());
            } else {
                console.log('❌ No storyline-genres section found');
            }
        }
        return genres;
    }
    extractCast($) {
        const cast = [];
        // Try multiple selectors for cast
        const castSelectors = [
            'section[data-testid="title-cast"] a[data-testid="title-cast-item__actor"]',
            '.cast_list .primary_photo + td a',
            '.titleCast .primary_photo + td a',
            'div[data-testid="title-cast-item"] a[href*="/name/"]'
        ];
        for (const selector of castSelectors){
            const elements = $(selector);
            if (elements.length > 0) {
                elements.each((_, element)=>{
                    const actorName = $(element).text().trim();
                    if (actorName && !cast.includes(actorName) && cast.length < 10) {
                        cast.push(actorName);
                    }
                });
                break; // Use the first selector that finds results
            }
        }
        return cast;
    }
    extractDirector($) {
        // Try multiple selectors for director
        const directorSelectors = [
            'li[data-testid="title-pc-principal-credit"]:contains("Director") .ipc-metadata-list-item__list-content-item',
            'li[data-testid="title-pc-principal-credit"]:contains("Directors") .ipc-metadata-list-item__list-content-item',
            'a[href*="/name/"][href*="ref_=tt_ov_dr"]',
            '.credit_summary_item:contains("Director") a'
        ];
        for (const selector of directorSelectors){
            const element = $(selector).first();
            const director = element.text().trim();
            if (director) {
                return director;
            }
        }
        return undefined;
    }
    extractCreator($) {
        // Try multiple selectors for creator (for TV series)
        const creatorSelectors = [
            'li[data-testid="title-pc-principal-credit"]:contains("Creator") .ipc-metadata-list-item__list-content-item',
            'li[data-testid="title-pc-principal-credit"]:contains("Creators") .ipc-metadata-list-item__list-content-item',
            '.credit_summary_item:contains("Creator") a',
            '.credit_summary_item:contains("Created by") a'
        ];
        for (const selector of creatorSelectors){
            const element = $(selector).first();
            const creator = element.text().trim();
            if (creator) {
                return creator;
            }
        }
        return undefined;
    }
    extractLanguage($) {
        // Try multiple selectors for language
        const languageSelectors = [
            'li[data-testid="title-details-languages"] .ipc-metadata-list-item__list-content-item',
            'div[data-testid="title-details-section"] li:contains("Language") .ipc-metadata-list-item__list-content-item',
            'a[href*="primary_language="]',
            '.txt-block:contains("Language") a'
        ];
        for (const selector of languageSelectors){
            const element = $(selector).first();
            const language = element.text().trim();
            if (language) {
                return language;
            }
        }
        return undefined;
    }
    extractCountry($) {
        // Try multiple selectors for country
        const countrySelectors = [
            'li[data-testid="title-details-origin"] .ipc-metadata-list-item__list-content-item',
            'div[data-testid="title-details-section"] li:contains("Country") .ipc-metadata-list-item__list-content-item',
            'a[href*="country_of_origin="]',
            '.txt-block:contains("Country") a'
        ];
        for (const selector of countrySelectors){
            const element = $(selector).first();
            const country = element.text().trim();
            if (country) {
                return country;
            }
        }
        return undefined;
    }
    async scrapeMovie(imdbId) {
        const $ = await this.fetchPage(imdbId);
        const basicInfo = this.extractBasicInfo($);
        if (basicInfo.type !== 'movie') {
            throw new Error('IMDb ID does not correspond to a movie');
        }
        const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
        const { popularity, delta: popularityDelta } = this.extractPopularity($);
        const { url: trailerUrl, runtime: trailerRuntime, likes: trailerLikes } = this.extractTrailerInfo($);
        return {
            title: basicInfo.title,
            year: basicInfo.year,
            rating: this.extractRating($),
            runtime: this.extractRuntime($),
            imdbRating,
            imdbVotes,
            popularity,
            popularityDelta,
            posterUrl: this.extractPosterUrl($),
            backdropUrl: this.extractBackdropUrl($),
            trailerUrl,
            trailerRuntime,
            trailerLikes,
            description: this.extractDescription($),
            genres: this.extractGenres($),
            director: this.extractDirector($),
            cast: this.extractCast($),
            language: this.extractLanguage($),
            country: this.extractCountry($)
        };
    }
    async scrapeSeries(imdbId) {
        const $ = await this.fetchPage(imdbId);
        const basicInfo = this.extractBasicInfo($);
        if (basicInfo.type !== 'series') {
            throw new Error('IMDb ID does not correspond to a TV series');
        }
        const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);
        const { popularity, delta: popularityDelta } = this.extractPopularity($);
        const { url: trailerUrl } = this.extractTrailerInfo($);
        return {
            title: basicInfo.title,
            startYear: basicInfo.year,
            endYear: undefined,
            rating: this.extractRating($),
            imdbRating,
            imdbVotes,
            popularity,
            popularityDelta,
            posterUrl: this.extractPosterUrl($),
            backdropUrl: this.extractBackdropUrl($),
            trailerUrl,
            description: this.extractDescription($),
            genres: this.extractGenres($),
            creator: this.extractCreator($),
            cast: this.extractCast($),
            language: this.extractLanguage($),
            country: this.extractCountry($),
            totalSeasons: undefined,
            status: 'ongoing'
        };
    }
}
const __TURBOPACK__default__export__ = IMDbScraper;
}}),
"[project]/src/lib/vidsrc.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "STREAMING_SOURCES": (()=>STREAMING_SOURCES),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const VIDSRC_BASE_URL = process.env.VIDSRC_BASE_URL || 'https://vidsrc.xyz';
const STREAMING_SOURCES = {
    vidsrc_xyz: {
        name: 'VidSrc XYZ',
        baseUrl: 'https://vidsrc.xyz',
        quality: 'HD',
        priority: 1
    },
    vidsrc_cc_v2: {
        name: 'VidSrc CC v2',
        baseUrl: 'https://vidsrc.cc/v2',
        quality: 'HD',
        priority: 2
    },
    vidsrc_cc_v3: {
        name: 'VidSrc CC v3',
        baseUrl: 'https://vidsrc.cc/v3',
        quality: 'HD',
        priority: 3
    },
    vidsrc_me: {
        name: 'VidSrc ME',
        baseUrl: 'https://vidsrc.me',
        quality: 'HD',
        priority: 4
    },
    superembed: {
        name: 'SuperEmbed',
        baseUrl: 'https://multiembed.mov',
        quality: 'HD',
        priority: 5
    },
    superembed_vip: {
        name: 'SuperEmbed VIP',
        baseUrl: 'https://multiembed.mov/directstream.php',
        quality: 'Premium HD',
        priority: 6
    }
};
class VidSrcAPI {
    static instance;
    static getInstance() {
        if (!VidSrcAPI.instance) {
            VidSrcAPI.instance = new VidSrcAPI();
        }
        return VidSrcAPI.instance;
    }
    /**
   * Generate movie embed URLs for all sources
   */ generateAllMovieEmbedUrls(imdbId, tmdbId) {
        const cleanImdbId = imdbId.replace('tt', '');
        const urls = [];
        // VidSrc XYZ
        urls.push({
            source: 'vidsrc_xyz',
            name: STREAMING_SOURCES.vidsrc_xyz.name,
            url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/movie?imdb=${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_xyz.quality,
            priority: STREAMING_SOURCES.vidsrc_xyz.priority
        });
        // VidSrc CC v2
        urls.push({
            source: 'vidsrc_cc_v2',
            name: STREAMING_SOURCES.vidsrc_cc_v2.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/movie/${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v2.priority
        });
        // VidSrc CC v3
        urls.push({
            source: 'vidsrc_cc_v3',
            name: STREAMING_SOURCES.vidsrc_cc_v3.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/movie/${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v3.priority
        });
        // VidSrc ME
        urls.push({
            source: 'vidsrc_me',
            name: STREAMING_SOURCES.vidsrc_me.name,
            url: `${STREAMING_SOURCES.vidsrc_me.baseUrl}/embed/${imdbId}`,
            quality: STREAMING_SOURCES.vidsrc_me.quality,
            priority: STREAMING_SOURCES.vidsrc_me.priority
        });
        // SuperEmbed
        urls.push({
            source: 'superembed',
            name: STREAMING_SOURCES.superembed.name,
            url: `${STREAMING_SOURCES.superembed.baseUrl}/?video_id=${imdbId}`,
            quality: STREAMING_SOURCES.superembed.quality,
            priority: STREAMING_SOURCES.superembed.priority
        });
        // SuperEmbed VIP
        urls.push({
            source: 'superembed_vip',
            name: STREAMING_SOURCES.superembed_vip.name,
            url: `${STREAMING_SOURCES.superembed_vip.baseUrl}?video_id=${imdbId}`,
            quality: STREAMING_SOURCES.superembed_vip.quality,
            priority: STREAMING_SOURCES.superembed_vip.priority
        });
        return urls.sort((a, b)=>a.priority - b.priority);
    }
    /**
   * Generate movie embed URL (legacy method for backward compatibility)
   */ generateMovieEmbedUrl(imdbId, options) {
        const baseUrl = `${VIDSRC_BASE_URL}/embed/movie`;
        const params = new URLSearchParams();
        if (options?.tmdbId) {
            params.append('tmdb', options.tmdbId);
        } else {
            params.append('imdb', imdbId);
        }
        if (options?.subUrl) {
            params.append('sub_url', encodeURIComponent(options.subUrl));
        }
        if (options?.dsLang) {
            params.append('ds_lang', options.dsLang);
        }
        if (options?.autoplay !== undefined) {
            params.append('autoplay', options.autoplay ? '1' : '0');
        }
        return `${baseUrl}?${params.toString()}`;
    }
    /**
   * Generate episode embed URLs for all sources
   */ generateAllEpisodeEmbedUrls(imdbId, season, episode, tmdbId) {
        const cleanImdbId = imdbId.replace('tt', '');
        const urls = [];
        // VidSrc XYZ
        urls.push({
            source: 'vidsrc_xyz',
            name: STREAMING_SOURCES.vidsrc_xyz.name,
            url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/tv?imdb=${imdbId}&season=${season}&episode=${episode}`,
            quality: STREAMING_SOURCES.vidsrc_xyz.quality,
            priority: STREAMING_SOURCES.vidsrc_xyz.priority
        });
        // VidSrc CC v2
        urls.push({
            source: 'vidsrc_cc_v2',
            name: STREAMING_SOURCES.vidsrc_cc_v2.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v2.priority
        });
        // VidSrc CC v3
        urls.push({
            source: 'vidsrc_cc_v3',
            name: STREAMING_SOURCES.vidsrc_cc_v3.name,
            url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,
            quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,
            priority: STREAMING_SOURCES.vidsrc_cc_v3.priority
        });
        // VidSrc ME
        urls.push({
            source: 'vidsrc_me',
            name: STREAMING_SOURCES.vidsrc_me.name,
            url: `${STREAMING_SOURCES.vidsrc_me.baseUrl}/embed/${imdbId}/${season}-${episode}`,
            quality: STREAMING_SOURCES.vidsrc_me.quality,
            priority: STREAMING_SOURCES.vidsrc_me.priority
        });
        // SuperEmbed
        urls.push({
            source: 'superembed',
            name: STREAMING_SOURCES.superembed.name,
            url: `${STREAMING_SOURCES.superembed.baseUrl}/?video_id=${imdbId}&s=${season}&e=${episode}`,
            quality: STREAMING_SOURCES.superembed.quality,
            priority: STREAMING_SOURCES.superembed.priority
        });
        // SuperEmbed VIP
        urls.push({
            source: 'superembed_vip',
            name: STREAMING_SOURCES.superembed_vip.name,
            url: `${STREAMING_SOURCES.superembed_vip.baseUrl}?video_id=${imdbId}&s=${season}&e=${episode}`,
            quality: STREAMING_SOURCES.superembed_vip.quality,
            priority: STREAMING_SOURCES.superembed_vip.priority
        });
        return urls.sort((a, b)=>a.priority - b.priority);
    }
    /**
   * Generate series embed URL (legacy method for backward compatibility)
   */ generateSeriesEmbedUrl(imdbId, options) {
        const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;
        const params = new URLSearchParams();
        if (options?.tmdbId) {
            params.append('tmdb', options.tmdbId);
        } else {
            params.append('imdb', imdbId);
        }
        if (options?.dsLang) {
            params.append('ds_lang', options.dsLang);
        }
        return `${baseUrl}?${params.toString()}`;
    }
    /**
   * Generate episode embed URL
   */ generateEpisodeEmbedUrl(imdbId, season, episode, options) {
        const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;
        const params = new URLSearchParams();
        if (options?.tmdbId) {
            params.append('tmdb', options.tmdbId);
        } else {
            params.append('imdb', imdbId);
        }
        params.append('season', season.toString());
        params.append('episode', episode.toString());
        if (options?.subUrl) {
            params.append('sub_url', encodeURIComponent(options.subUrl));
        }
        if (options?.dsLang) {
            params.append('ds_lang', options.dsLang);
        }
        if (options?.autoplay !== undefined) {
            params.append('autoplay', options.autoplay ? '1' : '0');
        }
        if (options?.autonext !== undefined) {
            params.append('autonext', options.autonext ? '1' : '0');
        }
        return `${baseUrl}?${params.toString()}`;
    }
    /**
   * Fetch latest movies from VidSrc
   */ async getLatestMovies(page = 1) {
        try {
            const url = `${VIDSRC_BASE_URL}/movies/latest/page-${page}.json`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url);
            return response.data.result || [];
        } catch (error) {
            console.error(`Error fetching latest movies from VidSrc (page ${page}):`, error);
            return [];
        }
    }
    /**
   * Fetch latest TV shows from VidSrc
   */ async getLatestSeries(page = 1) {
        try {
            const url = `${VIDSRC_BASE_URL}/tvshows/latest/page-${page}.json`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url);
            return response.data.result || [];
        } catch (error) {
            console.error(`Error fetching latest series from VidSrc (page ${page}):`, error);
            return [];
        }
    }
    /**
   * Fetch latest episodes from VidSrc
   */ async getLatestEpisodes(page = 1) {
        try {
            const url = `${VIDSRC_BASE_URL}/episodes/latest/page-${page}.json`;
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url);
            return response.data.result || [];
        } catch (error) {
            console.error(`Error fetching latest episodes from VidSrc (page ${page}):`, error);
            return [];
        }
    }
    /**
   * SIMPLE: Get episodes for a specific series from VidSrc (for embed URLs only)
   * This is now used only to get streaming links, not for episode discovery
   */ async getSeriesEpisodes(imdbId) {
        try {
            console.log(`🔍 ADVANCED: Comprehensive episode search for series: ${imdbId}`);
            const allEpisodes = new Map();
            // Strategy 1: Search through ALL latest episodes (not just 15 pages)
            console.log(`📡 Strategy 1: Scanning latest episodes across all pages...`);
            let foundEpisodesInLatest = 0;
            for(let page = 1; page <= 50; page++){
                try {
                    const episodes = await this.getLatestEpisodes(page);
                    if (episodes.length === 0) break; // No more episodes
                    const seriesEpisodes = episodes.filter((episode)=>episode.imdb_id === imdbId);
                    seriesEpisodes.forEach((episode)=>{
                        const key = `S${episode.season}E${episode.episode}`;
                        if (!allEpisodes.has(key)) {
                            allEpisodes.set(key, {
                                season: episode.season,
                                episode: episode.episode,
                                embed_url: episode.embed_url,
                                embed_url_tmdb: episode.embed_url_tmdb
                            });
                            foundEpisodesInLatest++;
                        }
                    });
                    if (seriesEpisodes.length > 0) {
                        console.log(`📺 Page ${page}: Found ${seriesEpisodes.length} episodes`);
                    }
                    // If no episodes found in last 10 pages, likely reached the end
                    if (page > 10 && seriesEpisodes.length === 0) {
                        let emptyPages = 0;
                        for(let checkPage = page - 9; checkPage <= page; checkPage++){
                            const checkEpisodes = await this.getLatestEpisodes(checkPage);
                            if (checkEpisodes.filter((ep)=>ep.imdb_id === imdbId).length === 0) {
                                emptyPages++;
                            }
                        }
                        if (emptyPages >= 8) break; // Stop if 8/10 recent pages are empty
                    }
                } catch (error) {
                    console.error(`Error fetching episodes page ${page}:`, error);
                }
            }
            console.log(`✅ Strategy 1 Complete: Found ${foundEpisodesInLatest} episodes in latest pages`);
            // Strategy 2: Search through series-specific pages (if available)
            console.log(`📡 Strategy 2: Searching series-specific endpoints...`);
            let foundEpisodesInSeries = 0;
            for(let page = 1; page <= 20; page++){
                try {
                    const seriesEpisodes = await this.getLatestSeries(page);
                    const matchingSeries = seriesEpisodes.filter((series)=>series.imdb_id === imdbId);
                    if (matchingSeries.length > 0) {
                        console.log(`📺 Series page ${page}: Found matching series, checking for episode data`);
                    // If series data includes episode information, extract it
                    // This is a placeholder for potential series-specific episode data
                    }
                } catch (error) {
                // Series endpoint might not exist, continue
                }
            }
            // Strategy 3: Systematic season-by-season verification
            console.log(`📡 Strategy 3: Systematic season verification...`);
            const episodesBySeason = new Map();
            // Group found episodes by season
            allEpisodes.forEach((episode, key)=>{
                if (!episodesBySeason.has(episode.season)) {
                    episodesBySeason.set(episode.season, []);
                }
                episodesBySeason.get(episode.season).push(episode.episode);
            });
            // Analyze each season for gaps and missing episodes
            for (const [season, episodes] of episodesBySeason){
                episodes.sort((a, b)=>a - b);
                const minEp = Math.min(...episodes);
                const maxEp = Math.max(...episodes);
                const missingEpisodes = [];
                for(let ep = 1; ep <= maxEp; ep++){
                    if (!episodes.includes(ep)) {
                        missingEpisodes.push(ep);
                    }
                }
                if (missingEpisodes.length > 0) {
                    console.log(`⚠️ Season ${season}: Missing episodes ${missingEpisodes.join(', ')} (Have: ${episodes.join(', ')})`);
                    // Strategy 3a: Search for missing episodes specifically
                    console.log(`🔍 Searching for missing episodes in Season ${season}...`);
                // This would involve more targeted searches if VidSrc had episode-specific endpoints
                }
            }
            // Convert Map back to array and sort
            const uniqueEpisodes = Array.from(allEpisodes.values()).sort((a, b)=>{
                if (a.season !== b.season) return a.season - b.season;
                return a.episode - b.episode;
            });
            // Final analysis
            const totalSeasons = episodesBySeason.size;
            const totalEpisodes = uniqueEpisodes.length;
            const seasonRanges = Array.from(episodesBySeason.keys()).sort((a, b)=>a - b);
            const minSeason = seasonRanges[0] || 0;
            const maxSeason = seasonRanges[seasonRanges.length - 1] || 0;
            console.log(`🎯 COMPREHENSIVE SEARCH COMPLETE:`);
            console.log(`   📺 Total Episodes Found: ${totalEpisodes}`);
            console.log(`   🗂️ Seasons Found: ${totalSeasons} (S${minSeason}-S${maxSeason})`);
            console.log(`   📊 Episodes per Season:`);
            episodesBySeason.forEach((episodes, season)=>{
                episodes.sort((a, b)=>a - b);
                const gaps = [];
                const maxEp = Math.max(...episodes);
                for(let ep = 1; ep <= maxEp; ep++){
                    if (!episodes.includes(ep)) gaps.push(ep);
                }
                console.log(`      S${season}: ${episodes.length} episodes (${episodes.join(', ')})${gaps.length > 0 ? ` - Missing: ${gaps.join(', ')}` : ' - Complete'}`);
            });
            return uniqueEpisodes;
        } catch (error) {
            console.error(`Error in comprehensive episode search for series ${imdbId}:`, error);
            return [];
        }
    }
    /**
   * Sync latest content from VidSrc to our database
   */ async syncLatestContent(pages = 5) {
        const movies = [];
        const series = [];
        const episodes = [];
        // Fetch multiple pages in parallel
        const moviePromises = Array.from({
            length: pages
        }, (_, i)=>this.getLatestMovies(i + 1));
        const seriesPromises = Array.from({
            length: pages
        }, (_, i)=>this.getLatestSeries(i + 1));
        const episodePromises = Array.from({
            length: pages
        }, (_, i)=>this.getLatestEpisodes(i + 1));
        try {
            const [movieResults, seriesResults, episodeResults] = await Promise.all([
                Promise.all(moviePromises),
                Promise.all(seriesPromises),
                Promise.all(episodePromises)
            ]);
            // Flatten results
            movieResults.forEach((pageMovies)=>movies.push(...pageMovies));
            seriesResults.forEach((pageSeries)=>series.push(...pageSeries));
            episodeResults.forEach((pageEpisodes)=>episodes.push(...pageEpisodes));
            return {
                movies,
                series,
                episodes
            };
        } catch (error) {
            console.error('Error syncing latest content from VidSrc:', error);
            return {
                movies,
                series,
                episodes
            };
        }
    }
}
const __TURBOPACK__default__export__ = VidSrcAPI;
}}),
"[project]/src/lib/contentService.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Movie.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Series.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Episode.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Request$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Request.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scraper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/scraper.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vidsrc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/vidsrc.ts [app-route] (ecmascript)");
;
;
;
;
;
;
;
class ContentService {
    static instance;
    scraper;
    vidsrc;
    constructor(){
        this.scraper = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$scraper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getInstance();
        this.vidsrc = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$vidsrc$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getInstance();
    }
    static getInstance() {
        if (!ContentService.instance) {
            ContentService.instance = new ContentService();
        }
        return ContentService.instance;
    }
    async getMovies(filters = {}) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { genre, year, language, country, rating, quality, sortBy = 'createdAt', sortOrder = 'desc', page = 1, limit = 20, search } = filters;
        // Build query
        const query = {};
        if (genre) query.genres = {
            $in: [
                genre
            ]
        };
        if (year) query.year = year;
        if (language) query.language = language;
        if (country) query.country = country;
        if (rating) query.rating = rating;
        if (quality) query.quality = quality;
        if (search) {
            // Use regex search instead of text search to avoid language override issues
            query.$or = [
                {
                    title: {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    description: {
                        $regex: search,
                        $options: 'i'
                    }
                }
            ];
        }
        // Build sort
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const skip = (page - 1) * limit;
        const [data, total] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(query).sort(sort).skip(skip).limit(limit).lean(),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(query)
        ]);
        return {
            data,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        };
    }
    async getSeries(filters = {}) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { genre, year, language, country, rating, sortBy = 'createdAt', sortOrder = 'desc', page = 1, limit = 20, search } = filters;
        const query = {};
        if (genre) query.genres = {
            $in: [
                genre
            ]
        };
        if (year) query.startYear = year;
        if (language) query.language = language;
        if (country) query.country = country;
        if (rating) query.rating = rating;
        if (search) {
            // Use regex search instead of text search to avoid language override issues
            query.$or = [
                {
                    title: {
                        $regex: search,
                        $options: 'i'
                    }
                },
                {
                    description: {
                        $regex: search,
                        $options: 'i'
                    }
                }
            ];
        }
        const sort = {};
        sort[sortBy === 'year' ? 'startYear' : sortBy] = sortOrder === 'asc' ? 1 : -1;
        const skip = (page - 1) * limit;
        const [data, total] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(query).sort(sort).skip(skip).limit(limit).lean(),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(query)
        ]);
        return {
            data,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        };
    }
    async getEpisodes(filters = {}) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { genre, quality, sortBy = 'createdAt', sortOrder = 'desc', page = 1, limit = 20, search } = filters;
        const query = {};
        if (genre) query.genres = {
            $in: [
                genre
            ]
        };
        if (quality) query.quality = quality;
        if (search) {
            query.seriesTitle = {
                $regex: search,
                $options: 'i'
            };
        }
        const sort = {};
        sort[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const skip = (page - 1) * limit;
        // Now that episodes have genres, we can use simple queries!
        let data, total;
        if (sortBy === 'createdAt' && sortOrder === 'desc') {
            // For latest episodes, get the latest episode from each series for diversity
            const aggregationPipeline = [
                {
                    $match: query
                },
                {
                    $sort: {
                        imdbId: 1,
                        season: -1,
                        episode: -1,
                        createdAt: -1 // Latest created first
                    }
                },
                {
                    $group: {
                        _id: '$imdbId',
                        latestEpisode: {
                            $first: '$$ROOT'
                        } // Get the latest episode from each series
                    }
                },
                {
                    $replaceRoot: {
                        newRoot: '$latestEpisode'
                    } // Replace root with the episode document
                },
                {
                    $sort: {
                        createdAt: -1
                    } // Sort series by their latest episode creation date
                },
                {
                    $skip: skip
                },
                {
                    $limit: limit
                }
            ];
            // Use aggregation for latest episodes (for diversity)
            [data, total] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate(aggregationPipeline),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(query)
            ]);
        } else {
            // Use simple query for other sorting
            [data, total] = await Promise.all([
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find(query).sort(sort).skip(skip).limit(limit),
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].countDocuments(query)
            ]);
        }
        // Add series poster URLs for episodes without posters
        const enrichedData = await Promise.all(data.map(async (episode)=>{
            if ((!episode.posterUrl || episode.posterUrl === '') && episode.imdbId) {
                const series = await this.getSeriesByImdbId(episode.imdbId);
                if (series?.posterUrl) {
                    episode.posterUrl = series.posterUrl;
                    episode.seriesPosterUrl = series.posterUrl;
                }
            }
            return episode;
        }));
        return {
            data: enrichedData,
            pagination: {
                page,
                limit,
                total,
                pages: Math.ceil(total / limit)
            }
        };
    }
    async getMovieById(id) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id).lean();
    }
    async getMovieByImdbId(imdbId) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            imdbId
        }).lean();
    }
    async getSeriesById(id) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(id).lean();
    }
    async getSeriesByImdbId(imdbId) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            imdbId
        }).lean();
    }
    async getSeriesEpisodes(imdbId, season) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const query = {
            imdbId
        };
        if (season) query.season = season;
        // Use aggregation to join with series data and get poster URLs
        const existingEpisodes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: query
            },
            {
                $sort: {
                    season: 1,
                    episode: 1
                }
            },
            {
                $lookup: {
                    from: 'series',
                    localField: 'imdbId',
                    foreignField: 'imdbId',
                    as: 'seriesData'
                }
            },
            {
                $addFields: {
                    seriesPosterUrl: {
                        $arrayElemAt: [
                            '$seriesData.posterUrl',
                            0
                        ]
                    },
                    posterUrl: {
                        $cond: {
                            if: {
                                $ne: [
                                    '$posterUrl',
                                    ''
                                ]
                            },
                            then: '$posterUrl',
                            else: {
                                $arrayElemAt: [
                                    '$seriesData.posterUrl',
                                    0
                                ]
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    seriesData: 0 // Remove the joined series data to keep response clean
                }
            }
        ]);
        // Auto-populate missing episodes
        const populatedEpisodes = await this.autoPopulateMissingEpisodes(imdbId, existingEpisodes, season);
        return populatedEpisodes.sort((a, b)=>{
            if (a.season !== b.season) return a.season - b.season;
            return a.episode - b.episode;
        });
    }
    async autoPopulateMissingEpisodes(imdbId, existingEpisodes, targetSeason) {
        const episodeMap = new Map();
        // Add existing episodes to map
        existingEpisodes.forEach((ep)=>{
            episodeMap.set(`${ep.season}-${ep.episode}`, ep);
        });
        // Get series info for poster
        const series = await this.getSeriesByImdbId(imdbId);
        const seriesPosterUrl = series?.posterUrl || '';
        // Group by season and find missing episodes
        const seasonGroups = new Map();
        existingEpisodes.forEach((ep)=>{
            if (!seasonGroups.has(ep.season)) {
                seasonGroups.set(ep.season, []);
            }
            seasonGroups.get(ep.season).push(ep.episode);
        });
        // For each season, fill in missing episodes
        for (const [seasonNum, episodes] of seasonGroups){
            if (targetSeason && seasonNum !== targetSeason) continue;
            const maxEpisode = Math.max(...episodes);
            // Create missing episodes from 1 to maxEpisode
            for(let epNum = 1; epNum <= maxEpisode; epNum++){
                const key = `${seasonNum}-${epNum}`;
                if (!episodeMap.has(key)) {
                    // Create placeholder episode
                    const placeholderEpisode = {
                        _id: new Date().getTime().toString() + Math.random().toString(36),
                        imdbId,
                        season: seasonNum,
                        episode: epNum,
                        episodeTitle: `Episode ${epNum}`,
                        description: `Episode ${epNum} of Season ${seasonNum}`,
                        posterUrl: seriesPosterUrl,
                        seriesPosterUrl: seriesPosterUrl,
                        runtime: '45 min',
                        imdbRating: 0,
                        airDate: new Date().toISOString(),
                        createdAt: new Date(),
                        updatedAt: new Date()
                    };
                    episodeMap.set(key, placeholderEpisode);
                }
            }
        }
        return Array.from(episodeMap.values());
    }
    async processImdbId(imdbId) {
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            // Check if already exists
            const existingMovie = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                imdbId
            });
            const existingSeries = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                imdbId
            });
            if (existingMovie || existingSeries) {
                return {
                    success: true,
                    type: existingMovie ? 'movie' : 'series'
                };
            }
            // Try to scrape as movie first
            try {
                const movieData = await this.scraper.scrapeMovie(imdbId);
                const embedUrl = this.vidsrc.generateMovieEmbedUrl(imdbId);
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create({
                    imdbId,
                    ...movieData,
                    embedUrl
                });
                return {
                    success: true,
                    type: 'movie'
                };
            } catch (movieError) {
                // If movie scraping fails, try as series
                try {
                    const seriesData = await this.scraper.scrapeSeries(imdbId);
                    const embedUrl = this.vidsrc.generateSeriesEmbedUrl(imdbId);
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create({
                        imdbId,
                        ...seriesData,
                        embedUrl
                    });
                    return {
                        success: true,
                        type: 'series'
                    };
                } catch (seriesError) {
                    throw new Error(`Failed to scrape as both movie and series: ${movieError.message}, ${seriesError.message}`);
                }
            }
        } catch (error) {
            console.error(`Error processing IMDb ID ${imdbId}:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    async createBulkRequest(imdbIds, submittedBy, contentType = 'auto') {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const request = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Request$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].create({
            imdbIds,
            totalCount: imdbIds.length,
            submittedBy,
            contentType
        });
        // Process in background (don't await)
        this.processBulkRequest(request._id.toString()).catch(console.error);
        return request;
    }
    async processBulkRequest(requestId) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const request = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Request$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(requestId);
        if (!request) return;
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Request$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(requestId, {
            status: 'processing'
        });
        let processedCount = 0;
        const failedIds = [];
        const errorMessages = [];
        for (const imdbId of request.imdbIds){
            try {
                const result = await this.processImdbId(imdbId);
                if (result.success) {
                    processedCount++;
                } else {
                    failedIds.push(imdbId);
                    errorMessages.push(result.error || 'Unknown error');
                }
            } catch (error) {
                failedIds.push(imdbId);
                errorMessages.push(error.message);
            }
            // Update progress
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Request$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(requestId, {
                processedCount,
                failedIds,
                errorMessages
            });
            // Rate limiting
            await new Promise((resolve)=>setTimeout(resolve, 2000));
        }
        // Mark as completed
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Request$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findByIdAndUpdate(requestId, {
            status: processedCount === request.imdbIds.length ? 'completed' : 'failed',
            completedAt: new Date()
        });
    }
    async getRequestStatus(requestId) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Request$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findById(requestId).lean();
    }
    async syncLatestContent() {
        try {
            const { movies, series, episodes } = await this.vidsrc.syncLatestContent();
            let movieCount = 0;
            let seriesCount = 0;
            let episodeCount = 0;
            // Process movies
            for (const movie of movies){
                const result = await this.processImdbId(movie.imdb_id);
                if (result.success && result.type === 'movie') movieCount++;
            }
            // Process series
            for (const show of series){
                const result = await this.processImdbId(show.imdb_id);
                if (result.success && result.type === 'series') seriesCount++;
            }
            // Process episodes
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
            for (const episode of episodes){
                try {
                    const embedUrl = this.vidsrc.generateEpisodeEmbedUrl(episode.imdb_id, parseInt(episode.season), parseInt(episode.episode));
                    // Get genres from series for new episodes
                    const genres = await this.populateEpisodeGenres(episode.imdb_id);
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOneAndUpdate({
                        imdbId: episode.imdb_id,
                        season: parseInt(episode.season),
                        episode: parseInt(episode.episode)
                    }, {
                        imdbId: episode.imdb_id,
                        seriesTitle: episode.show_title,
                        season: parseInt(episode.season),
                        episode: parseInt(episode.episode),
                        embedUrl,
                        quality: episode.quality,
                        genres: genres // Populate genres from series
                    }, {
                        upsert: true
                    });
                    episodeCount++;
                } catch (error) {
                    console.error(`Error processing episode ${episode.imdb_id} S${episode.season}E${episode.episode}:`, error);
                }
            }
            return {
                success: true,
                counts: {
                    movies: movieCount,
                    series: seriesCount,
                    episodes: episodeCount
                }
            };
        } catch (error) {
            console.error('Error syncing latest content:', error);
            return {
                success: false,
                counts: {
                    movies: 0,
                    series: 0,
                    episodes: 0
                }
            };
        }
    }
    async addEpisode(episodeData) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // Check if series exists, if not create it
        if (episodeData.imdbId) {
            await this.ensureSeriesExists(episodeData.imdbId, episodeData);
            // Populate genres from series if not already provided
            if (!episodeData.genres) {
                episodeData.genres = await this.populateEpisodeGenres(episodeData.imdbId);
            }
        }
        const episode = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"](episodeData);
        return episode.save();
    }
    async ensureSeriesExists(imdbId, episodeData) {
        const existingSeries = await this.getSeriesByImdbId(imdbId);
        if (!existingSeries) {
            // Create series from episode data
            const seriesData = {
                imdbId,
                title: episodeData.seriesTitle || `Series ${imdbId}`,
                description: episodeData.description || `Series for ${episodeData.seriesTitle || imdbId}`,
                posterUrl: episodeData.seriesPosterUrl || episodeData.posterUrl || '',
                backdropUrl: episodeData.seriesPosterUrl || episodeData.posterUrl || '',
                releaseDate: episodeData.airDate || new Date().toISOString(),
                genres: [
                    'Drama'
                ],
                imdbRating: episodeData.imdbRating || 0,
                runtime: episodeData.runtime || '45 min',
                status: 'Ongoing',
                totalSeasons: episodeData.season || 1,
                totalEpisodes: 1,
                language: 'English',
                country: 'US',
                network: 'Unknown',
                createdAt: new Date(),
                updatedAt: new Date()
            };
            const series = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"](seriesData);
            await series.save();
            console.log(`Auto-created series: ${seriesData.title} (${imdbId})`);
        }
    }
    async getMovieFilterOptions() {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // Get genre counts using aggregation
        const genreCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $unwind: '$genres'
            },
            {
                $group: {
                    _id: '$genres',
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            },
            {
                $project: {
                    genre: '$_id',
                    count: 1,
                    _id: 0
                }
            }
        ]);
        // Get language counts
        const languageCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    language: {
                        $exists: true,
                        $ne: null,
                        $ne: ''
                    }
                }
            },
            {
                $group: {
                    _id: '$language',
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            },
            {
                $project: {
                    language: '$_id',
                    count: 1,
                    _id: 0
                }
            }
        ]);
        // Get country counts
        const countryCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    country: {
                        $exists: true,
                        $ne: null,
                        $ne: ''
                    }
                }
            },
            {
                $group: {
                    _id: '$country',
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            },
            {
                $project: {
                    country: '$_id',
                    count: 1,
                    _id: 0
                }
            }
        ]);
        // Get unique years
        const years = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].distinct('year', {
            year: {
                $exists: true,
                $ne: null
            }
        });
        // Get unique ratings
        const ratings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].distinct('rating', {
            rating: {
                $exists: true,
                $ne: null
            }
        });
        // Get unique qualities
        const qualities = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Movie$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].distinct('quality', {
            quality: {
                $exists: true,
                $ne: null
            }
        });
        return {
            genres: genreCounts,
            languages: languageCounts,
            countries: countryCounts,
            years: years.filter(Boolean).sort((a, b)=>b - a),
            ratings: ratings.filter(Boolean).sort(),
            qualities: qualities.filter(Boolean).sort()
        };
    }
    async getSeriesFilterOptions() {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // Get genre counts using aggregation
        const genreCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $unwind: '$genres'
            },
            {
                $group: {
                    _id: '$genres',
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            },
            {
                $project: {
                    genre: '$_id',
                    count: 1,
                    _id: 0
                }
            }
        ]);
        // Get language counts
        const languageCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    language: {
                        $exists: true,
                        $ne: null,
                        $ne: ''
                    }
                }
            },
            {
                $group: {
                    _id: '$language',
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            },
            {
                $project: {
                    language: '$_id',
                    count: 1,
                    _id: 0
                }
            }
        ]);
        // Get country counts
        const countryCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $match: {
                    country: {
                        $exists: true,
                        $ne: null,
                        $ne: ''
                    }
                }
            },
            {
                $group: {
                    _id: '$country',
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            },
            {
                $project: {
                    country: '$_id',
                    count: 1,
                    _id: 0
                }
            }
        ]);
        // Get unique years (using startYear for series)
        const years = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].distinct('startYear', {
            startYear: {
                $exists: true,
                $ne: null
            }
        });
        // Get unique ratings
        const ratings = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].distinct('rating', {
            rating: {
                $exists: true,
                $ne: null
            }
        });
        return {
            genres: genreCounts,
            languages: languageCounts,
            countries: countryCounts,
            years: years.filter(Boolean).sort((a, b)=>b - a),
            ratings: ratings.filter(Boolean).sort(),
            qualities: [] // Series don't have quality field
        };
    }
    async getEpisodeFilterOptions() {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        // Now episodes have their own genres field - much simpler!
        const genreCounts = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].aggregate([
            {
                $unwind: '$genres'
            },
            {
                $group: {
                    _id: '$genres',
                    count: {
                        $sum: 1
                    }
                }
            },
            {
                $sort: {
                    count: -1
                }
            },
            {
                $project: {
                    genre: '$_id',
                    count: 1,
                    _id: 0
                }
            }
        ]);
        // Get unique qualities from episodes
        const qualities = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].distinct('quality', {
            quality: {
                $exists: true,
                $ne: null
            }
        });
        return {
            genres: genreCounts,
            languages: [],
            countries: [],
            years: [],
            ratings: [],
            qualities: qualities.filter(Boolean).sort()
        };
    }
    /**
   * Helper function to populate episode genres from its series
   * This should be called whenever a new episode is created
   */ async populateEpisodeGenres(imdbId) {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        try {
            const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
                imdbId
            }, {
                genres: 1
            });
            return series?.genres || [];
        } catch (error) {
            console.error(`Error fetching genres for series ${imdbId}:`, error);
            return [];
        }
    }
    /**
   * Utility function to update existing episodes without genres
   * This can be run manually if needed to fix episodes that were created before genre population
   */ async updateEpisodesWithoutGenres() {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        let updated = 0;
        let errors = 0;
        try {
            // Find episodes without genres
            const episodesWithoutGenres = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find({
                $or: [
                    {
                        genres: {
                            $exists: false
                        }
                    },
                    {
                        genres: {
                            $size: 0
                        }
                    }
                ]
            }, {
                imdbId: 1,
                seriesTitle: 1,
                season: 1,
                episode: 1
            }).limit(1000); // Process in batches
            console.log(`🔄 Found ${episodesWithoutGenres.length} episodes without genres`);
            // Group by imdbId to minimize series lookups
            const episodesByImdb = episodesWithoutGenres.reduce((acc, ep)=>{
                if (!acc[ep.imdbId]) acc[ep.imdbId] = [];
                acc[ep.imdbId].push(ep);
                return acc;
            }, {});
            // Update episodes by series
            for (const [imdbId, episodes] of Object.entries(episodesByImdb)){
                try {
                    const genres = await this.populateEpisodeGenres(imdbId);
                    if (genres.length > 0) {
                        const episodeIds = episodes.map((ep)=>ep._id);
                        const result = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].updateMany({
                            _id: {
                                $in: episodeIds
                            }
                        }, {
                            $set: {
                                genres: genres
                            }
                        });
                        updated += result.modifiedCount;
                        console.log(`✅ Updated ${result.modifiedCount} episodes for series ${imdbId} with genres: ${genres.join(', ')}`);
                    }
                } catch (error) {
                    console.error(`❌ Error updating episodes for series ${imdbId}:`, error);
                    errors++;
                }
            }
            console.log(`🎉 Genre update completed: ${updated} episodes updated, ${errors} errors`);
        } catch (error) {
            console.error('❌ Error in updateEpisodesWithoutGenres:', error);
            errors++;
        }
        return {
            updated,
            errors
        };
    }
}
const __TURBOPACK__default__export__ = ContentService;
}}),
"[project]/src/app/api/series/[id]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$contentService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/contentService.ts [app-route] (ecmascript)");
;
;
const contentService = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$contentService$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getInstance();
async function GET(request, { params }) {
    try {
        const { id } = await params;
        // Check if it's an IMDb ID or MongoDB ObjectId
        const isImdbId = id.startsWith('tt');
        const series = isImdbId ? await contentService.getSeriesByImdbId(id) : await contentService.getSeriesById(id);
        if (!series) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Series not found'
            }, {
                status: 404
            });
        }
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json(series);
    } catch (error) {
        console.error('Error fetching series:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch series'
        }, {
            status: 500
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__2bee2cd7._.js.map