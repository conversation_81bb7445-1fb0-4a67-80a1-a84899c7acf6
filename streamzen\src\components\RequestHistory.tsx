'use client';

import React, { useState, useEffect } from 'react';
import { Clock, CheckCircle, XCircle, Loader2, RefreshCw } from 'lucide-react';
import Button from './ui/Button';
import { cn } from '@/lib/utils';

interface RequestStatus {
  _id: string;
  imdbIds: string[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  processedCount: number;
  totalCount: number;
  failedIds: string[];
  errorMessages: string[];
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

const RequestHistory: React.FC = () => {
  const [requests, setRequests] = useState<RequestStatus[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // Load requests from localStorage on component mount
  useEffect(() => {
    const savedRequests = localStorage.getItem('streamzen_requests');
    if (savedRequests) {
      try {
        const parsed = JSON.parse(savedRequests);
        setRequests(parsed);
      } catch (error) {
        console.error('Error parsing saved requests:', error);
      }
    }
  }, []);

  // Save requests to localStorage whenever requests change
  useEffect(() => {
    if (requests.length > 0) {
      localStorage.setItem('streamzen_requests', JSON.stringify(requests));
    }
  }, [requests]);

  const refreshRequest = async (requestId: string) => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/requests/${requestId}`);
      if (response.ok) {
        const updatedRequest = await response.json();
        setRequests(prev => 
          prev.map(req => req._id === requestId ? updatedRequest : req)
        );
      }
    } catch (error) {
      console.error('Error refreshing request:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="text-yellow-400" size={16} />;
      case 'processing':
        return <Loader2 className="text-blue-400 animate-spin" size={16} />;
      case 'completed':
        return <CheckCircle className="text-green-400" size={16} />;
      case 'failed':
        return <XCircle className="text-red-400" size={16} />;
      default:
        return <Clock className="text-gray-400" size={16} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-400';
      case 'processing':
        return 'text-blue-400';
      case 'completed':
        return 'text-green-400';
      case 'failed':
        return 'text-red-400';
      default:
        return 'text-gray-400';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getProgressPercentage = (request: RequestStatus) => {
    return Math.round((request.processedCount / request.totalCount) * 100);
  };

  // Add new request to history (called from parent component)
  const addRequest = (request: RequestStatus) => {
    setRequests(prev => [request, ...prev.slice(0, 9)]); // Keep only last 10 requests
  };

  // Expose addRequest function to parent
  React.useEffect(() => {
    (window as any).addRequestToHistory = addRequest;
    return () => {
      delete (window as any).addRequestToHistory;
    };
  }, []);

  if (requests.length === 0) {
    return (
      <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6">
        <h2 className="text-white font-semibold text-xl mb-6">Request History</h2>
        <div className="text-center py-8">
          <Clock className="mx-auto text-gray-400 mb-4" size={48} />
          <p className="text-gray-400">No requests yet</p>
          <p className="text-gray-500 text-sm mt-2">
            Submit your first request to see it here
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-white font-semibold text-xl">Request History</h2>
        <Button
          onClick={() => window.location.reload()}
          variant="ghost"
          size="sm"
          className="flex items-center space-x-1"
        >
          <RefreshCw size={16} />
          <span>Refresh</span>
        </Button>
      </div>

      <div className="space-y-4 max-h-96 overflow-y-auto">
        {requests.map((request) => (
          <div
            key={request._id}
            className="bg-gray-800/50 rounded-lg p-4 border border-gray-700"
          >
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                {getStatusIcon(request.status)}
                <span className={cn('font-medium capitalize', getStatusColor(request.status))}>
                  {request.status}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-gray-400 text-sm">
                  {formatDate(request.createdAt)}
                </span>
                {(request.status === 'processing' || request.status === 'pending') && (
                  <Button
                    onClick={() => refreshRequest(request._id)}
                    disabled={isLoading}
                    variant="ghost"
                    size="sm"
                  >
                    <RefreshCw size={14} className={isLoading ? 'animate-spin' : ''} />
                  </Button>
                )}
              </div>
            </div>

            <div className="mb-3">
              <div className="flex items-center justify-between text-sm mb-1">
                <span className="text-gray-300">
                  Progress: {request.processedCount} / {request.totalCount}
                </span>
                <span className="text-gray-400">
                  {getProgressPercentage(request)}%
                </span>
              </div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className={cn(
                    'h-2 rounded-full transition-all duration-300',
                    request.status === 'completed' ? 'bg-green-500' :
                    request.status === 'failed' ? 'bg-red-500' :
                    request.status === 'processing' ? 'bg-blue-500' : 'bg-yellow-500'
                  )}
                  style={{ width: `${getProgressPercentage(request)}%` }}
                />
              </div>
            </div>

            <div className="text-sm text-gray-400">
              <p>{request.totalCount} items requested</p>
              {request.failedIds.length > 0 && (
                <p className="text-red-400 mt-1">
                  {request.failedIds.length} failed
                </p>
              )}
              {request.completedAt && (
                <p className="mt-1">
                  Completed: {formatDate(request.completedAt)}
                </p>
              )}
            </div>

            {request.errorMessages.length > 0 && (
              <div className="mt-3 p-2 bg-red-900/20 border border-red-500/20 rounded text-red-400 text-xs">
                <p className="font-medium mb-1">Errors:</p>
                <ul className="space-y-1">
                  {request.errorMessages.slice(0, 3).map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                  {request.errorMessages.length > 3 && (
                    <li>• ... and {request.errorMessages.length - 3} more</li>
                  )}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default RequestHistory;
