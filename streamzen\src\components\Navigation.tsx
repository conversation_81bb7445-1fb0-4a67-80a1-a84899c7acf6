'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Search, Home, Film, Tv, Calendar, Plus, Play, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import SearchBar from './SearchBar';

const Navigation: React.FC = () => {
  const pathname = usePathname();
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  const navItems = [
    { href: '/', label: 'Home', icon: Home },
    { href: '/movies', label: 'Movies', icon: Film },
    { href: '/series', label: 'Series', icon: Tv },
    { href: '/episodes', label: 'Episodes', icon: Calendar },
  ];

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 glass border-b border-white/5 backdrop-blur-xl">
      <div className="max-w-[1920px] mx-auto px-8 lg:px-16">
        <div className="flex items-center justify-between h-24">
          {/* Apple TV + Netflix Style Logo */}
          <Link href="/" className="flex items-center space-x-4 focus-ring rounded-2xl px-3 py-2 transition-all duration-300 hover:bg-white/5">
            <div className="relative group">
              <div className="w-12 h-12 bg-white rounded-2xl flex items-center justify-center shadow-2xl transition-all duration-300 group-hover:scale-110">
                <Play size={24} className="text-black fill-current ml-1" />
              </div>
              <div className="absolute -inset-2 bg-white/20 rounded-2xl blur-lg opacity-0 group-hover:opacity-100 transition-all duration-300" />
            </div>
            <div className="flex flex-col">
              <span className="text-white text-3xl font-black tracking-tight">
                Stream<span className="text-red-600">Zen</span>
              </span>
              <span className="text-gray-400 text-sm font-medium -mt-1 tracking-wide">
                Premium Entertainment
              </span>
            </div>
          </Link>

          {/* Ultra Premium Navigation Links */}
          <div className="hidden lg:flex items-center space-x-1">
            {navItems.map((item) => {
              const Icon = item.icon;
              const isActive = pathname === item.href;

              return (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'relative flex items-center space-x-3 px-8 py-4 rounded-2xl transition-all duration-300 focus-ring group',
                    isActive
                      ? 'bg-gradient-to-r from-gray-800/40 to-gray-700/40 text-white shadow-2xl border border-white/15 backdrop-blur-sm'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  )}
                >
                  <Icon size={22} className={cn(
                    'transition-all duration-300',
                    isActive ? 'text-gray-300' : 'group-hover:text-white'
                  )} />
                  <span className="font-semibold text-lg">{item.label}</span>
                  {isActive && (
                    <div className="absolute -inset-1 bg-gradient-to-r from-gray-600/20 to-gray-500/20 rounded-2xl blur opacity-75" />
                  )}
                </Link>
              );
            })}
          </div>

          {/* Ultra Premium Actions */}
          <div className="flex items-center space-x-4">
            {/* Premium Search Button */}
            <button
              onClick={() => setIsSearchOpen(!isSearchOpen)}
              className="p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group"
            >
              {isSearchOpen ? (
                <X size={24} className="group-hover:scale-110 transition-transform duration-300" />
              ) : (
                <Search size={24} className="group-hover:scale-110 transition-transform duration-300" />
              )}
            </button>

            {/* Apple TV + Netflix Style Request Button */}
            <Link
              href="/request"
              className="hidden sm:flex items-center space-x-3 px-8 py-4 bg-red-600 rounded-2xl text-white hover:bg-red-700 transition-all duration-300 focus-ring font-semibold shadow-2xl hover:scale-105"
            >
              <Plus size={20} />
              <span className="text-lg">Request</span>
            </Link>

            {/* Premium Mobile Menu Button */}
            <button className="lg:hidden p-4 text-gray-300 hover:text-white hover:bg-white/10 rounded-2xl transition-all duration-300 focus-ring group">
              <div className="w-6 h-6 flex flex-col justify-center space-y-1.5">
                <div className="w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4" />
                <div className="w-full h-0.5 bg-current rounded" />
                <div className="w-full h-0.5 bg-current rounded transition-all duration-300 group-hover:w-3/4 group-hover:ml-auto" />
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* Premium Mobile Navigation */}
      <div className="lg:hidden border-t border-white/5 glass">
        <div className="flex items-center justify-around py-4 px-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;

            return (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex flex-col items-center space-y-2 px-4 py-3 rounded-xl transition-all duration-300 focus-ring',
                  isActive
                    ? 'text-white bg-white/15'
                    : 'text-gray-400 hover:text-white hover:bg-white/10'
                )}
              >
                <Icon size={20} />
                <span className="text-xs font-medium">{item.label}</span>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Search Overlay */}
      {isSearchOpen && (
        <div className="absolute top-full left-0 right-0 bg-black/95 backdrop-blur-xl border-b border-gray-800/50 z-40">
          <div className="max-w-[1920px] mx-auto px-8 lg:px-16 py-6">
            <div className="max-w-2xl mx-auto">
              <SearchBar
                placeholder="Search movies, series, episodes..."
                className="w-full"
                onSearch={() => setIsSearchOpen(false)}
              />
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;
