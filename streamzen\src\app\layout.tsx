import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import SyncInitializer from "@/components/SyncInitializer";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "StreamZen - Premium Streaming Experience",
  description: "Immersive, premium streaming platform with ultra-modern interface",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.className} antialiased bg-black text-white`}>
        <SyncInitializer />
        <Navigation />
        <main className="pt-24 page-transition min-h-screen">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
