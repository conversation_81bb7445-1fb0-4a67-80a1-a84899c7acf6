module.exports = {

"[project]/.next-internal/server/app/api/series/[id]/episodes/check/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/mongoose [external] (mongoose, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("mongoose", () => require("mongoose"));

module.exports = mod;
}}),
"[project]/src/lib/mongodb.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';
if ("TURBOPACK compile-time falsy", 0) {
    "TURBOPACK unreachable";
}
/**
 * Global is used here to maintain a cached connection across hot reloads
 * in development. This prevents connections growing exponentially
 * during API Route usage.
 */ let cached = global.mongoose;
if (!cached) {
    cached = global.mongoose = {
        conn: null,
        promise: null
    };
}
async function connectDB() {
    if (cached.conn) {
        return cached.conn;
    }
    if (!cached.promise) {
        const opts = {
            bufferCommands: false
        };
        cached.promise = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].connect(MONGODB_URI, opts).then((mongoose)=>{
            return mongoose;
        });
    }
    try {
        cached.conn = await cached.promise;
    } catch (e) {
        cached.promise = null;
        throw e;
    }
    return cached.conn;
}
const __TURBOPACK__default__export__ = connectDB;
}}),
"[project]/src/models/Series.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const SeriesSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        unique: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    title: {
        type: String,
        required: true,
        index: true
    },
    startYear: {
        type: Number,
        required: true,
        index: true
    },
    endYear: {
        type: Number,
        index: true
    },
    rating: String,
    imdbRating: {
        type: Number,
        index: true
    },
    imdbVotes: String,
    popularity: {
        type: Number,
        index: true
    },
    popularityDelta: Number,
    posterUrl: String,
    trailerUrl: String,
    description: String,
    genres: [
        {
            type: String,
            index: true
        }
    ],
    creator: String,
    cast: [
        String
    ],
    language: {
        type: String,
        index: true
    },
    country: {
        type: String,
        index: true
    },
    totalSeasons: Number,
    status: {
        type: String,
        enum: [
            'ongoing',
            'ended',
            'cancelled'
        ],
        index: true
    },
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String // VidSrc TMDB embed URL
}, {
    timestamps: true
});
// Compound indexes for better query performance
SeriesSchema.index({
    startYear: -1,
    imdbRating: -1
});
SeriesSchema.index({
    genres: 1,
    startYear: -1
});
SeriesSchema.index({
    status: 1,
    startYear: -1
});
// Removed text index to avoid language override issues
SeriesSchema.index({
    title: 1
});
SeriesSchema.index({
    language: 1,
    country: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Series || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Series', SeriesSchema);
}}),
"[project]/src/models/Episode.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const EpisodeSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    imdbId: {
        type: String,
        required: true,
        index: true
    },
    tmdbId: {
        type: String,
        index: true
    },
    seriesTitle: {
        type: String,
        required: true,
        index: true
    },
    season: {
        type: Number,
        required: true,
        index: true
    },
    episode: {
        type: Number,
        required: true,
        index: true
    },
    episodeTitle: String,
    airDate: {
        type: Date,
        index: true
    },
    runtime: String,
    imdbRating: Number,
    description: String,
    embedUrl: {
        type: String,
        required: true
    },
    embedUrlTmdb: String,
    vidsrcUrl: String,
    vidsrcTmdbUrl: String,
    quality: {
        type: String,
        index: true
    },
    genres: [
        {
            type: String,
            index: true
        }
    ]
}, {
    timestamps: true
});
// Compound indexes for better query performance
EpisodeSchema.index({
    imdbId: 1,
    season: 1,
    episode: 1
}, {
    unique: true
});
EpisodeSchema.index({
    airDate: -1
});
EpisodeSchema.index({
    seriesTitle: 1,
    season: 1,
    episode: 1
});
EpisodeSchema.index({
    createdAt: -1
}); // For latest episodes
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.Episode || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('Episode', EpisodeSchema);
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/node:stream [external] (node:stream, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:stream", () => require("node:stream"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/string_decoder [external] (string_decoder, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("string_decoder", () => require("string_decoder"));

module.exports = mod;
}}),
"[externals]/node:assert [external] (node:assert, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:assert", () => require("node:assert"));

module.exports = mod;
}}),
"[externals]/node:net [external] (node:net, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:net", () => require("node:net"));

module.exports = mod;
}}),
"[externals]/node:http [external] (node:http, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http", () => require("node:http"));

module.exports = mod;
}}),
"[externals]/node:buffer [external] (node:buffer, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}}),
"[externals]/node:querystring [external] (node:querystring, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:querystring", () => require("node:querystring"));

module.exports = mod;
}}),
"[externals]/node:events [external] (node:events, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:events", () => require("node:events"));

module.exports = mod;
}}),
"[externals]/node:diagnostics_channel [external] (node:diagnostics_channel, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:diagnostics_channel", () => require("node:diagnostics_channel"));

module.exports = mod;
}}),
"[externals]/node:util [external] (node:util, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}}),
"[externals]/node:tls [external] (node:tls, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:tls", () => require("node:tls"));

module.exports = mod;
}}),
"[externals]/node:zlib [external] (node:zlib, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:zlib", () => require("node:zlib"));

module.exports = mod;
}}),
"[externals]/node:perf_hooks [external] (node:perf_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:perf_hooks", () => require("node:perf_hooks"));

module.exports = mod;
}}),
"[externals]/node:util/types [external] (node:util/types, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:util/types", () => require("node:util/types"));

module.exports = mod;
}}),
"[externals]/node:worker_threads [external] (node:worker_threads, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:worker_threads", () => require("node:worker_threads"));

module.exports = mod;
}}),
"[externals]/node:crypto [external] (node:crypto, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}}),
"[externals]/node:http2 [external] (node:http2, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:http2", () => require("node:http2"));

module.exports = mod;
}}),
"[externals]/node:url [external] (node:url, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:url", () => require("node:url"));

module.exports = mod;
}}),
"[externals]/node:async_hooks [external] (node:async_hooks, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:async_hooks", () => require("node:async_hooks"));

module.exports = mod;
}}),
"[externals]/node:console [external] (node:console, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:console", () => require("node:console"));

module.exports = mod;
}}),
"[externals]/node:dns [external] (node:dns, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("node:dns", () => require("node:dns"));

module.exports = mod;
}}),
"[project]/src/lib/imdbEpisodeScraper.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/index.js [app-route] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/cheerio/dist/esm/load-parse.js [app-route] (ecmascript)");
;
;
class IMDbEpisodeScraper {
    static instance;
    baseUrl = 'https://www.imdb.com';
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    };
    static getInstance() {
        if (!IMDbEpisodeScraper.instance) {
            IMDbEpisodeScraper.instance = new IMDbEpisodeScraper();
        }
        return IMDbEpisodeScraper.instance;
    }
    /**
   * ADVANCED: Get comprehensive episode information for a series from IMDb
   */ async getSeriesEpisodes(imdbId) {
        try {
            console.log(`🎬 ADVANCED: Scraping complete episode data from IMDb for ${imdbId}`);
            // Step 1: Get main series page to extract basic info
            const mainPageData = await this.getMainPageData(imdbId);
            if (!mainPageData) {
                console.log(`❌ Could not access main page for ${imdbId}`);
                return null;
            }
            // Step 2: Get episodes page for detailed episode information
            const episodesData = await this.getEpisodesPageData(imdbId);
            if (!episodesData) {
                console.log(`❌ Could not access episodes page for ${imdbId}`);
                return null;
            }
            // Step 3: Get detailed episode information
            let detailedSeasons = [];
            if (episodesData.seasons.length > 0) {
                // Multi-season show: scrape each season
                detailedSeasons = await this.getDetailedSeasonData(imdbId, episodesData.seasons);
            } else if (mainPageData.totalEpisodes && mainPageData.totalEpisodes > 0) {
                // Single season or no season structure: try to scrape episodes directly
                console.log(`📺 No seasons found, attempting to scrape episodes directly...`);
                const directEpisodes = await this.getEpisodesDirectly(imdbId);
                if (directEpisodes && directEpisodes.length > 0) {
                    detailedSeasons = [
                        {
                            seasonNumber: 1,
                            episodeCount: directEpisodes.length,
                            episodes: directEpisodes,
                            year: mainPageData.year
                        }
                    ];
                } else {
                    // Fallback: Create episodes based on total count from main page
                    console.log(`📺 Direct scraping failed, creating episodes based on total count: ${mainPageData.totalEpisodes}`);
                    const fallbackEpisodes = [];
                    const totalSeasons = mainPageData.totalSeasons || 1;
                    const episodesPerSeason = Math.ceil(mainPageData.totalEpisodes / totalSeasons);
                    for(let season = 1; season <= totalSeasons; season++){
                        const seasonEpisodes = [];
                        const startEpisode = (season - 1) * episodesPerSeason + 1;
                        const endEpisode = Math.min(season * episodesPerSeason, mainPageData.totalEpisodes);
                        for(let episode = startEpisode; episode <= endEpisode; episode++){
                            const actualEpisodeNumber = episode - startEpisode + 1;
                            seasonEpisodes.push({
                                season: season,
                                episode: actualEpisodeNumber,
                                title: `Episode ${actualEpisodeNumber}`,
                                description: undefined,
                                airDate: undefined,
                                rating: undefined,
                                imdbId: undefined,
                                duration: undefined
                            });
                        }
                        if (seasonEpisodes.length > 0) {
                            detailedSeasons.push({
                                seasonNumber: season,
                                episodeCount: seasonEpisodes.length,
                                episodes: seasonEpisodes,
                                year: mainPageData.year
                            });
                        }
                    }
                    console.log(`✅ Fallback: Created ${detailedSeasons.length} seasons with ${mainPageData.totalEpisodes} episodes`);
                }
            }
            const result = {
                totalEpisodes: mainPageData.totalEpisodes || episodesData.totalEpisodes,
                totalSeasons: Math.max(mainPageData.totalSeasons || 0, detailedSeasons.length),
                seasons: detailedSeasons,
                seriesTitle: mainPageData.title,
                seriesYear: mainPageData.year,
                lastUpdated: new Date().toISOString()
            };
            console.log(`✅ IMDb scraping complete: ${result.totalEpisodes} episodes across ${result.totalSeasons} seasons`);
            return result;
        } catch (error) {
            console.error(`❌ Error scraping IMDb episodes for ${imdbId}:`, error);
            return null;
        }
    }
    /**
   * Get basic series information from main IMDb page
   */ async getMainPageData(imdbId) {
        try {
            const url = `${this.baseUrl}/title/${imdbId}/`;
            console.log(`📄 Fetching main page: ${url}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: this.headers
            });
            const $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
            // Extract series title
            const title = $('h1[data-testid="hero__pageTitle"] span').first().text().trim() || $('h1').first().text().trim() || 'Unknown Title';
            // Extract year
            const yearText = $('h1[data-testid="hero__pageTitle"]').text();
            const yearMatch = yearText.match(/\((\d{4})\)/);
            const year = yearMatch ? yearMatch[1] : undefined;
            // Extract episode count from episodes widget
            const episodeCountText = $('section[data-testid="episodes-widget"] .ipc-title__subtext').text();
            const totalEpisodes = episodeCountText ? parseInt(episodeCountText) : undefined;
            // Extract season count from season selector - try multiple selectors
            const seasonSelectors = [
                'select[id="browse-episodes-season"] option',
                '#browse-episodes-season option',
                'select[aria-label*="season"] option',
                '.ipc-simple-select__input option'
            ];
            let totalSeasons = 0;
            let foundSeasons = false;
            for (const selector of seasonSelectors){
                const seasonOptions = $(selector);
                if (seasonOptions.length > 0) {
                    console.log(`📍 Found season options on main page using: ${selector}`);
                    seasonOptions.each((_, element)=>{
                        const value = $(element).attr('value');
                        if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {
                            totalSeasons = Math.max(totalSeasons, parseInt(value));
                            foundSeasons = true;
                        }
                    });
                    if (foundSeasons) break;
                }
            }
            if (!foundSeasons) {
                console.log(`❌ No season selector found on main page`);
            }
            console.log(`📊 Main page data: ${title} (${year}) - ${totalEpisodes} episodes, ${totalSeasons} seasons`);
            return {
                title,
                year,
                totalEpisodes,
                totalSeasons
            };
        } catch (error) {
            console.error(`❌ Error fetching main page for ${imdbId}:`, error);
            return null;
        }
    }
    /**
   * Get episodes page data with season information
   */ async getEpisodesPageData(imdbId) {
        try {
            const url = `${this.baseUrl}/title/${imdbId}/episodes/`;
            console.log(`📺 Fetching episodes page: ${url}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: this.headers
            });
            const $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
            // Extract total episodes
            const episodeCountText = $('section[data-testid="episodes-widget"] .ipc-title__subtext').text() || $('.episodes-section .ipc-title__subtext').text();
            const totalEpisodes = episodeCountText ? parseInt(episodeCountText) : 0;
            // Extract seasons from dropdown - use multiple selectors to find the season dropdown
            const seasons = [];
            const seasonSelectors = [
                'select[id="browse-episodes-season"] option',
                '#browse-episodes-season option',
                'select[aria-label*="season"] option',
                '.ipc-simple-select__input option'
            ];
            let seasonOptions = null;
            for (const selector of seasonSelectors){
                seasonOptions = $(selector);
                if (seasonOptions.length > 0) {
                    console.log(`📍 Found season options using selector: ${selector}`);
                    break;
                }
            }
            if (seasonOptions && seasonOptions.length > 0) {
                seasonOptions.each((_, element)=>{
                    const value = $(element).attr('value');
                    const text = $(element).text().trim();
                    console.log(`🔍 Season option: value="${value}", text="${text}"`);
                    if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {
                        seasons.push({
                            seasonNumber: parseInt(value),
                            year: undefined // Will be filled in detailed scraping
                        });
                        console.log(`✅ Added season ${parseInt(value)}`);
                    }
                });
            } else {
                console.log(`❌ No season dropdown found with any selector`);
            }
            // Also extract years if available
            const yearOptions = $('select[id="browse-episodes-year"] option[value]');
            const years = [];
            yearOptions.each((_, element)=>{
                const value = $(element).attr('value');
                if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {
                    years.push(value);
                }
            });
            console.log(`📊 Episodes page data: ${totalEpisodes} episodes, ${seasons.length} seasons, years: ${years.join(', ')}`);
            return {
                totalEpisodes,
                seasons: seasons.sort((a, b)=>a.seasonNumber - b.seasonNumber)
            };
        } catch (error) {
            console.error(`❌ Error fetching episodes page for ${imdbId}:`, error);
            return null;
        }
    }
    /**
   * Get detailed episode data for each season
   */ async getDetailedSeasonData(imdbId, seasons) {
        const detailedSeasons = [];
        for (const season of seasons){
            try {
                console.log(`🔍 Scraping detailed data for Season ${season.seasonNumber}...`);
                const seasonData = await this.getSeasonEpisodes(imdbId, season.seasonNumber);
                if (seasonData) {
                    detailedSeasons.push(seasonData);
                }
                // Add delay to avoid rate limiting
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            } catch (error) {
                console.error(`❌ Error scraping Season ${season.seasonNumber}:`, error);
            // Continue with other seasons
            }
        }
        return detailedSeasons.sort((a, b)=>a.seasonNumber - b.seasonNumber);
    }
    /**
   * Get episodes directly from the episodes page (for single-season shows)
   */ async getEpisodesDirectly(imdbId) {
        try {
            const url = `${this.baseUrl}/title/${imdbId}/episodes/`;
            console.log(`📺 Fetching episodes directly: ${url}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: this.headers
            });
            const $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
            const episodes = [];
            // Try multiple selectors for episode items
            const episodeSelectors = [
                '.episode-item-wrapper',
                '.list_item',
                '[data-testid*="episode"]',
                '.titleColumn',
                '.episode-list-item',
                '.ipc-list-card'
            ];
            let foundEpisodes = false;
            for (const selector of episodeSelectors){
                const episodeElements = $(selector);
                if (episodeElements.length > 0) {
                    console.log(`📍 Found ${episodeElements.length} episodes using selector: ${selector}`);
                    episodeElements.each((index, element)=>{
                        try {
                            const $episode = $(element);
                            // Extract episode number (try multiple patterns)
                            let episodeNumber = index + 1; // fallback to index
                            const episodeText = $episode.find('.episode-number, .episode_number, [data-testid*="episode-number"]').text() || $episode.find('strong').first().text() || $episode.text();
                            const episodeMatch = episodeText.match(/E(\d+)/i) || episodeText.match(/Episode\s*(\d+)/i) || episodeText.match(/(\d+)\./);
                            if (episodeMatch) {
                                episodeNumber = parseInt(episodeMatch[1]);
                            }
                            // Extract title
                            const title = $episode.find('.episode-title, .episode_title, a[title]').text().trim() || $episode.find('a').first().text().trim() || $episode.find('h4, h3').text().trim() || `Episode ${episodeNumber}`;
                            // Extract description
                            const description = $episode.find('.episode-description, .item_description, .plot').text().trim();
                            // Extract air date
                            const airDate = $episode.find('.airdate, .episode-aired, [data-testid*="air-date"]').text().trim();
                            // Extract rating
                            const ratingText = $episode.find('.rating, .ipl-rating-star__rating, [data-testid*="rating"]').text();
                            const rating = ratingText ? parseFloat(ratingText) : undefined;
                            // Extract episode IMDb ID
                            const episodeLink = $episode.find('a[href*="/title/tt"]').attr('href');
                            const episodeImdbId = episodeLink ? episodeLink.match(/\/title\/(tt\d+)/)?.[1] : undefined;
                            if (title && title !== `Episode ${episodeNumber}` || episodeNumber > 0) {
                                episodes.push({
                                    season: 1,
                                    episode: episodeNumber,
                                    title: title || `Episode ${episodeNumber}`,
                                    description: description || undefined,
                                    airDate: airDate || undefined,
                                    rating: rating,
                                    imdbId: episodeImdbId,
                                    duration: undefined
                                });
                            }
                        } catch (error) {
                            console.error(`❌ Error parsing episode:`, error);
                        }
                    });
                    foundEpisodes = true;
                    break; // Stop after finding episodes with first working selector
                }
            }
            if (!foundEpisodes) {
                console.log(`❌ No episodes found with any selector`);
            }
            // Sort episodes by episode number
            episodes.sort((a, b)=>a.episode - b.episode);
            console.log(`✅ Direct episode scraping: Found ${episodes.length} episodes`);
            return episodes;
        } catch (error) {
            console.error(`❌ Error fetching episodes directly for ${imdbId}:`, error);
            return [];
        }
    }
    /**
   * Get episodes for a specific season
   */ async getSeasonEpisodes(imdbId, seasonNumber) {
        try {
            const url = `${this.baseUrl}/title/${imdbId}/episodes/?season=${seasonNumber}`;
            console.log(`📺 Fetching season ${seasonNumber}: ${url}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(url, {
                headers: this.headers
            });
            const $ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$cheerio$2f$dist$2f$esm$2f$load$2d$parse$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["load"])(response.data);
            const episodes = [];
            // Extract episodes from the page
            $('.episode-item-wrapper, .list_item, [data-testid*="episode"]').each((_, element)=>{
                try {
                    const $episode = $(element);
                    // Extract episode number
                    const episodeText = $episode.find('.episode-number, .episode_number, [data-testid*="episode-number"]').text() || $episode.find('strong').first().text();
                    const episodeMatch = episodeText.match(/E(\d+)/i) || episodeText.match(/(\d+)\./);
                    const episodeNumber = episodeMatch ? parseInt(episodeMatch[1]) : 0;
                    if (episodeNumber === 0) return; // Skip if we can't find episode number
                    // Extract title
                    const title = $episode.find('.episode-title, .episode_title, a[title]').text().trim() || $episode.find('a').first().text().trim() || `Episode ${episodeNumber}`;
                    // Extract description
                    const description = $episode.find('.episode-description, .item_description, .plot').text().trim();
                    // Extract air date
                    const airDate = $episode.find('.airdate, .episode-aired, [data-testid*="air-date"]').text().trim();
                    // Extract rating
                    const ratingText = $episode.find('.rating, .ipl-rating-star__rating, [data-testid*="rating"]').text();
                    const rating = ratingText ? parseFloat(ratingText) : undefined;
                    // Extract episode IMDb ID
                    const episodeLink = $episode.find('a[href*="/title/tt"]').attr('href');
                    const episodeImdbId = episodeLink ? episodeLink.match(/\/title\/(tt\d+)/)?.[1] : undefined;
                    episodes.push({
                        season: seasonNumber,
                        episode: episodeNumber,
                        title: title || `Episode ${episodeNumber}`,
                        description: description || undefined,
                        airDate: airDate || undefined,
                        rating: rating,
                        imdbId: episodeImdbId,
                        duration: undefined
                    });
                } catch (error) {
                    console.error(`❌ Error parsing episode:`, error);
                }
            });
            // Sort episodes by episode number
            episodes.sort((a, b)=>a.episode - b.episode);
            console.log(`✅ Season ${seasonNumber}: Found ${episodes.length} episodes`);
            return {
                seasonNumber,
                episodeCount: episodes.length,
                episodes,
                year: undefined // Could be extracted from air dates if needed
            };
        } catch (error) {
            console.error(`❌ Error fetching season ${seasonNumber} for ${imdbId}:`, error);
            return null;
        }
    }
}
const __TURBOPACK__default__export__ = IMDbEpisodeScraper;
}}),
"[project]/src/app/api/series/[id]/episodes/check/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mongodb.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Series.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/models/Episode.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$imdbEpisodeScraper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/imdbEpisodeScraper.ts [app-route] (ecmascript)");
;
;
;
;
;
async function POST(request, { params }) {
    try {
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mongodb$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])();
        const { id } = await params;
        // Get the series by IMDb ID (the id parameter is actually the IMDb ID)
        const series = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Series$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].findOne({
            imdbId: id
        });
        if (!series) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Series not found'
            }, {
                status: 404
            });
        }
        console.log(`🔍 Advanced Episode Analysis for: ${series.title} (${series.imdbId})`);
        // Initialize advanced statistics
        const stats = {
            totalFound: 0,
            newEpisodesAdded: 0,
            existingEpisodesUpdated: 0,
            totalEpisodesInDB: 0,
            seasonBreakdown: {},
            episodeRange: {
                minSeason: Infinity,
                maxSeason: 0,
                minEpisode: Infinity,
                maxEpisode: 0
            },
            duplicatesRemoved: 0,
            errors: [],
            verificationResults: {
                totalSeasonsFound: 0,
                totalSeasonsWithGaps: 0,
                overallCompletionPercentage: 0,
                missingSeasonRanges: [],
                suspiciousGaps: []
            }
        };
        // Get existing episodes from database using both seriesId and imdbId for accuracy
        const existingEpisodes = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find({
            $or: [
                {
                    seriesId: series._id
                },
                {
                    imdbId: series.imdbId
                }
            ]
        });
        const existingEpisodeMap = new Map();
        existingEpisodes.forEach((ep)=>{
            const key = `S${ep.season}E${ep.episode}`;
            existingEpisodeMap.set(key, ep);
        });
        console.log(`📊 Current database state: ${existingEpisodes.length} episodes (seriesId: ${series._id}, imdbId: ${series.imdbId})`);
        // ADVANCED: Get comprehensive episode data from IMDb (most reliable source)
        console.log(`🎬 ADVANCED: IMDb-based episode verification for series: ${series.title} (${series.imdbId})`);
        const imdbScraper = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$imdbEpisodeScraper$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].getInstance();
        const imdbEpisodeData = await imdbScraper.getSeriesEpisodes(series.imdbId);
        if (!imdbEpisodeData) {
            console.log(`❌ Could not retrieve episode data from IMDb for ${series.title}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Could not retrieve episode data from IMDb',
                stats: {
                    ...stats,
                    totalEpisodesInDB: existingEpisodes.length
                }
            });
        }
        console.log(`🎯 IMDb Analysis: Found ${imdbEpisodeData.totalEpisodes} episodes across ${imdbEpisodeData.totalSeasons} seasons`);
        console.log(`📊 Season breakdown: ${imdbEpisodeData.seasons.map((s)=>`S${s.seasonNumber}(${s.episodeCount})`).join(', ')}`);
        // Step 2: Generate VidSrc embed URLs for all IMDb episodes
        console.log(`🔗 Generating VidSrc embed URLs for all IMDb episodes...`);
        // Step 3: Process ALL episodes from IMDb data (IMDb is the authoritative source)
        const allIMDbEpisodes = [];
        // Process every episode that IMDb says exists and generate VidSrc URLs
        imdbEpisodeData.seasons.forEach((season)=>{
            season.episodes.forEach((episode)=>{
                // Generate VidSrc embed URL directly
                const vidsrcEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${episode.season}&episode=${episode.episode}`;
                const vidsrcTmdbUrl = `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId || ''}&season=${episode.season}&episode=${episode.episode}`;
                allIMDbEpisodes.push({
                    season: episode.season,
                    episode: episode.episode,
                    title: episode.title,
                    description: episode.description,
                    airDate: episode.airDate,
                    rating: episode.rating && !isNaN(episode.rating) ? episode.rating : undefined,
                    embedUrl: vidsrcEmbedUrl,
                    embedUrlTmdb: vidsrcTmdbUrl
                });
                console.log(`🔗 Generated VidSrc URL for S${episode.season}E${episode.episode}: ${vidsrcEmbedUrl}`);
            });
        });
        stats.totalFound = allIMDbEpisodes.length;
        console.log(`✅ IMDb-Based Analysis: ${allIMDbEpisodes.length} episodes from IMDb, all with generated VidSrc streaming links`);
        // Analyze episode range from IMDb data
        if (allIMDbEpisodes.length > 0) {
            stats.episodeRange.minSeason = Math.min(...allIMDbEpisodes.map((e)=>e.season));
            stats.episodeRange.maxSeason = Math.max(...allIMDbEpisodes.map((e)=>e.season));
            stats.episodeRange.minEpisode = Math.min(...allIMDbEpisodes.map((e)=>e.episode));
            stats.episodeRange.maxEpisode = Math.max(...allIMDbEpisodes.map((e)=>e.episode));
        }
        console.log(`📈 Episode Range: S${stats.episodeRange.minSeason}-S${stats.episodeRange.maxSeason}, E${stats.episodeRange.minEpisode}-E${stats.episodeRange.maxEpisode}`);
        // Process each episode from IMDb data (authoritative source)
        for (const imdbEpisode of allIMDbEpisodes){
            try {
                const episodeKey = `S${imdbEpisode.season}E${imdbEpisode.episode}`;
                const existingEpisode = existingEpisodeMap.get(episodeKey);
                if (existingEpisode) {
                    // Update existing episode with new data from IMDb and VidSrc
                    let updated = false;
                    // Update title if different
                    if (existingEpisode.episodeTitle !== imdbEpisode.title) {
                        existingEpisode.episodeTitle = imdbEpisode.title;
                        updated = true;
                    }
                    // Update description if available
                    if (imdbEpisode.description && existingEpisode.description !== imdbEpisode.description) {
                        existingEpisode.description = imdbEpisode.description;
                        updated = true;
                    }
                    // Update air date if available
                    if (imdbEpisode.airDate && existingEpisode.airDate !== imdbEpisode.airDate) {
                        existingEpisode.airDate = imdbEpisode.airDate;
                        updated = true;
                    }
                    // Update IMDb rating if available and valid
                    if (imdbEpisode.rating && !isNaN(imdbEpisode.rating) && existingEpisode.imdbRating !== imdbEpisode.rating) {
                        existingEpisode.imdbRating = imdbEpisode.rating;
                        updated = true;
                    }
                    // Update embed URLs if available
                    if (imdbEpisode.embedUrl && existingEpisode.embedUrl !== imdbEpisode.embedUrl) {
                        existingEpisode.embedUrl = imdbEpisode.embedUrl;
                        existingEpisode.vidsrcUrl = imdbEpisode.embedUrl;
                        updated = true;
                    }
                    if (imdbEpisode.embedUrlTmdb && existingEpisode.embedUrlTmdb !== imdbEpisode.embedUrlTmdb) {
                        existingEpisode.embedUrlTmdb = imdbEpisode.embedUrlTmdb;
                        existingEpisode.vidsrcTmdbUrl = imdbEpisode.embedUrlTmdb;
                        updated = true;
                    }
                    if (updated) {
                        existingEpisode.updatedAt = new Date();
                        await existingEpisode.save();
                        stats.existingEpisodesUpdated++;
                        console.log(`🔄 Updated episode ${episodeKey} with IMDb data`);
                    }
                    continue;
                }
                // Create new episode with complete IMDb metadata and generated VidSrc embed URL
                const newEpisode = new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]({
                    seriesId: series._id,
                    seriesTitle: series.title,
                    imdbId: series.imdbId,
                    season: imdbEpisode.season,
                    episode: imdbEpisode.episode,
                    episodeTitle: imdbEpisode.title,
                    description: imdbEpisode.description || `${series.title} - Season ${imdbEpisode.season}, Episode ${imdbEpisode.episode}`,
                    posterUrl: series.posterUrl,
                    genres: series.genres || [],
                    embedUrl: imdbEpisode.embedUrl,
                    embedUrlTmdb: imdbEpisode.embedUrlTmdb || '',
                    vidsrcUrl: imdbEpisode.embedUrl,
                    vidsrcTmdbUrl: imdbEpisode.embedUrlTmdb || '',
                    airDate: imdbEpisode.airDate || new Date().toISOString(),
                    runtime: series.runtime || '45 min',
                    imdbRating: imdbEpisode.rating && !isNaN(imdbEpisode.rating) ? imdbEpisode.rating : undefined,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
                await newEpisode.save();
                stats.newEpisodesAdded++;
                console.log(`✅ Added new episode ${episodeKey} with IMDb data and VidSrc embed URL`);
            } catch (error) {
                const errorMsg = `Failed to process episode S${imdbEpisode.season}E${imdbEpisode.episode}: ${error instanceof Error ? error.message : 'Unknown error'}`;
                stats.errors.push(errorMsg);
                console.error(`❌ ${errorMsg}`);
            }
        }
        // Advanced season analysis and gap detection
        const allEpisodesInDB = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$models$2f$Episode$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].find({
            $or: [
                {
                    seriesId: series._id
                },
                {
                    imdbId: series.imdbId
                }
            ]
        }).sort({
            season: 1,
            episode: 1
        });
        stats.totalEpisodesInDB = allEpisodesInDB.length;
        console.log(`📊 Final database query: Found ${allEpisodesInDB.length} episodes for series ${series.title}`);
        // ADVANCED: Build comprehensive season breakdown with intelligent gap analysis
        const seasonMap = new Map();
        allEpisodesInDB.forEach((ep)=>{
            if (!seasonMap.has(ep.season)) {
                seasonMap.set(ep.season, []);
            }
            seasonMap.get(ep.season).push(ep.episode);
        });
        // Use IMDb data as the authoritative source for expected episodes
        const imdbSeasonMap = new Map();
        if (imdbEpisodeData) {
            imdbEpisodeData.seasons.forEach((season)=>{
                imdbSeasonMap.set(season.seasonNumber, season.episodeCount);
            });
        }
        console.log(`🔍 ADVANCED: Analyzing ${imdbSeasonMap.size} seasons based on IMDb data...`);
        // Advanced gap analysis - ONLY analyze seasons that exist in IMDb data
        for (const [season, expectedEpisodes] of imdbSeasonMap){
            episodes.sort((a, b)=>a - b);
            const completionPercentage = Math.round(episodes.length / expectedEpisodes * 100);
            console.log(`📊 Season ${season}: ${episodes.length}/${expectedEpisodes} episodes (${completionPercentage}% complete)`);
            // Find all missing episodes based on IMDb data
            const allMissingEpisodes = [];
            // Check against IMDb episode count
            for(let i = 1; i <= expectedEpisodes; i++){
                if (!episodes.includes(i)) {
                    allMissingEpisodes.push(i);
                }
                // Analyze gap severity based on IMDb reference
                let gapSeverity = 'minor';
                const missingPercentage = allMissingEpisodes.length / expectedEpisodes * 100;
                if (missingPercentage > 50) {
                    gapSeverity = 'critical'; // More than 50% missing
                } else if (missingPercentage > 20) {
                    gapSeverity = 'major'; // More than 20% missing
                }
                stats.seasonBreakdown[season] = {
                    episodeCount: episodes.length,
                    episodes: episodes,
                    missingEpisodes: allMissingEpisodes,
                    hasGaps: allMissingEpisodes.length > 0,
                    completionPercentage: completionPercentage,
                    expectedEpisodes: expectedEpisodes
                };
                // Log detailed analysis
                if (allMissingEpisodes.length > 0) {
                    console.log(`⚠️ Season ${season} Analysis:`);
                    console.log(`   📊 Episodes Found: ${episodes.length} (${episodes.join(', ')})`);
                    console.log(`   ❌ Missing Episodes: ${allMissingEpisodes.length} (${allMissingEpisodes.join(', ')})`);
                    console.log(`   📈 Completion: ${completionPercentage}%${expectedEpisodes ? ` of estimated ${expectedEpisodes} episodes` : ''}`);
                    console.log(`   🚨 Severity: ${gapSeverity.toUpperCase()}`);
                    // Add to suspicious gaps
                    if (allMissingEpisodes.length > 0) {
                        stats.verificationResults.suspiciousGaps.push({
                            season: season,
                            missingRange: allMissingEpisodes.length > 5 ? `${allMissingEpisodes[0]}-${allMissingEpisodes[allMissingEpisodes.length - 1]} (${allMissingEpisodes.length} episodes)` : allMissingEpisodes.join(', '),
                            severity: gapSeverity
                        });
                    }
                } else {
                    console.log(`✅ Season ${season}: Complete (${episodes.length} episodes: ${episodes.join(', ')})`);
                }
            }
            // Check for missing season ranges
            const seasonNumbers = Array.from(seasonMap.keys()).sort((a, b)=>a - b);
            const missingSeasonRanges = [];
            if (seasonNumbers.length > 1) {
                const minSeason = seasonNumbers[0];
                const maxSeason = seasonNumbers[seasonNumbers.length - 1];
                for(let s = minSeason; s <= maxSeason; s++){
                    if (!seasonNumbers.includes(s)) {
                        missingSeasonRanges.push(`Season ${s}`);
                    }
                }
            }
            // Calculate overall verification results
            const totalSeasonsWithGaps = Object.values(stats.seasonBreakdown).filter((s)=>s.hasGaps).length;
            const totalEpisodesExpected = Object.values(stats.seasonBreakdown).reduce((sum, s)=>sum + (s.expectedEpisodes || s.episodeCount), 0);
            const overallCompletionPercentage = totalEpisodesExpected > 0 ? Math.round(stats.totalEpisodesInDB / totalEpisodesExpected * 100) : 100;
            stats.verificationResults = {
                totalSeasonsFound: seasonMap.size,
                totalSeasonsWithGaps: totalSeasonsWithGaps,
                overallCompletionPercentage: overallCompletionPercentage,
                missingSeasonRanges: missingSeasonRanges,
                suspiciousGaps: stats.verificationResults.suspiciousGaps
            };
            // Update series total episodes count
            if (series.totalEpisodes !== stats.totalEpisodesInDB) {
                series.totalEpisodes = stats.totalEpisodesInDB;
                series.updatedAt = new Date();
                await series.save();
                console.log(`📊 Updated series total episodes: ${series.totalEpisodes} → ${stats.totalEpisodesInDB}`);
            }
            // ADVANCED: IMDb-based verification summary
            console.log(`🎯 IMDB-BASED EPISODE VERIFICATION COMPLETE:`);
            console.log(`   🎬 IMDb Episodes Found: ${imdbEpisodeData.totalEpisodes} across ${imdbEpisodeData.totalSeasons} seasons`);
            console.log(`   📺 Episodes in Database: ${stats.totalEpisodesInDB}`);
            console.log(`   🔗 All Episodes have Generated VidSrc URLs: YES`);
            console.log(`   ➕ New Episodes Added: ${stats.newEpisodesAdded}`);
            console.log(`   🔄 Existing Episodes Updated: ${stats.existingEpisodesUpdated}`);
            console.log(`   🗂️ Total Seasons Found: ${stats.verificationResults.totalSeasonsFound}`);
            console.log(`   ⚠️ Seasons with Gaps: ${stats.verificationResults.totalSeasonsWithGaps}`);
            console.log(`   📊 Overall Completion: ${stats.verificationResults.overallCompletionPercentage}%`);
            console.log(`   ❌ Processing Errors: ${stats.errors.length}`);
            if (stats.verificationResults.missingSeasonRanges.length > 0) {
                console.log(`   🚨 Missing Seasons: ${stats.verificationResults.missingSeasonRanges.join(', ')}`);
            }
            if (stats.verificationResults.suspiciousGaps.length > 0) {
                console.log(`   🔍 Suspicious Gaps Detected:`);
                stats.verificationResults.suspiciousGaps.forEach((gap)=>{
                    console.log(`      Season ${gap.season}: ${gap.missingRange} (${gap.severity.toUpperCase()})`);
                });
            }
            // Quality assessment
            let qualityAssessment = 'EXCELLENT';
            if (stats.verificationResults.overallCompletionPercentage < 90) qualityAssessment = 'GOOD';
            if (stats.verificationResults.overallCompletionPercentage < 70) qualityAssessment = 'FAIR';
            if (stats.verificationResults.overallCompletionPercentage < 50) qualityAssessment = 'POOR';
            if (stats.verificationResults.suspiciousGaps.some((g)=>g.severity === 'critical')) qualityAssessment = 'CRITICAL';
            console.log(`   🏆 Episode Collection Quality: ${qualityAssessment}`);
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                message: 'Advanced episode analysis completed successfully',
                stats: stats,
                episodes: allEpisodesInDB,
                lastChecked: new Date().toISOString()
            });
        }
        <invalid>;
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__86911c6a._.js.map