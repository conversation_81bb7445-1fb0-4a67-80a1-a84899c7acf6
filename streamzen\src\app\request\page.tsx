import RequestForm from '@/components/RequestForm';
import RequestHistory from '@/components/RequestHistory';

export default function RequestPage() {
  return (
    <div className="min-h-screen bg-black">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Request Content
          </h1>
          <p className="text-gray-400 text-lg max-w-2xl mx-auto">
            Submit IMDb IDs for movies and TV series you'd like to see added to StreamZen. 
            Our system will automatically scrape the metadata and add streaming links.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Request Form */}
          <div>
            <RequestForm />
          </div>

          {/* Request History */}
          <div>
            <RequestHistory />
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-12 bg-gray-900/50 backdrop-blur-sm rounded-xl p-6">
          <h2 className="text-white font-semibold text-lg mb-4">How to Request Content</h2>
          <div className="space-y-4 text-gray-300">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                1
              </div>
              <div>
                <h3 className="font-medium text-white mb-1">Find IMDb IDs</h3>
                <p className="text-sm">
                  Go to IMDb.com and find the movies or TV series you want. 
                  Copy the IMDb ID from the URL (e.g., tt0111161 for The Shawshank Redemption).
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                2
              </div>
              <div>
                <h3 className="font-medium text-white mb-1">Submit Request</h3>
                <p className="text-sm">
                  Paste the IMDb IDs in the form (one per line) and submit your request. 
                  You can submit up to 50 IDs at once.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                3
              </div>
              <div>
                <h3 className="font-medium text-white mb-1">Processing</h3>
                <p className="text-sm">
                  Our system will automatically scrape metadata from IMDb and add streaming links. 
                  Processing typically takes 2-5 minutes per item.
                </p>
              </div>
            </div>
            
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                4
              </div>
              <div>
                <h3 className="font-medium text-white mb-1">Enjoy Streaming</h3>
                <p className="text-sm">
                  Once processed, the content will be available in the appropriate section 
                  (Movies, Series, or Episodes) and ready to stream.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Examples */}
        <div className="mt-8 bg-gray-900/30 backdrop-blur-sm rounded-xl p-6">
          <h2 className="text-white font-semibold text-lg mb-4">Example IMDb IDs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h3 className="text-white font-medium mb-2">Movies</h3>
              <div className="space-y-1 text-gray-400">
                <div>tt0111161 - The Shawshank Redemption</div>
                <div>tt0068646 - The Godfather</div>
                <div>tt0468569 - The Dark Knight</div>
                <div>tt0108052 - Schindler's List</div>
              </div>
            </div>
            <div>
              <h3 className="text-white font-medium mb-2">TV Series</h3>
              <div className="space-y-1 text-gray-400">
                <div>tt0944947 - Game of Thrones</div>
                <div>tt0903747 - Breaking Bad</div>
                <div>tt2356777 - True Detective</div>
                <div>tt0141842 - The Sopranos</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
