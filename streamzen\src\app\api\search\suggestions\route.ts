import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Movie from '@/models/Movie';
import Series from '@/models/Series';
import Episode from '@/models/Episode';

export async function GET(request: NextRequest) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    const limit = parseInt(searchParams.get('limit') || '8');
    
    if (!query || query.trim().length < 2) {
      return NextResponse.json({ suggestions: [] });
    }

    const searchQuery = query.trim();
    const searchRegex = new RegExp(searchQuery, 'i');
    const exactRegex = new RegExp(`^${searchQuery}`, 'i');
    
    const suggestions: any[] = [];

    // Get movie suggestions
    const movies = await Movie.aggregate([
      {
        $match: {
          $or: [
            { title: exactRegex },
            { title: searchRegex }
          ]
        }
      },
      {
        $addFields: {
          score: {
            $add: [
              // Exact start match gets highest score
              { $cond: [{ $regexMatch: { input: "$title", regex: exactRegex } }, 100, 0] },
              // Contains match gets lower score
              { $cond: [{ $regexMatch: { input: "$title", regex: searchRegex } }, 50, 0] },
              // Rating bonus (only for numeric ratings)
              { $multiply: [
                { $cond: [
                  { $regexMatch: { input: { $toString: "$rating" }, regex: "^[0-9]+(\\.[0-9]+)?$" } },
                  { $toDouble: "$rating" },
                  0
                ]},
                2
              ]}
            ]
          },
          type: { $literal: "movie" }
        }
      },
      { $sort: { score: -1, rating: -1 } },
      { $limit: Math.ceil(limit / 3) },
      {
        $project: {
          title: 1,
          posterUrl: 1,
          rating: 1,
          releaseDate: 1,
          type: 1,
          imdbId: 1,
          score: 1
        }
      }
    ]);

    // Get series suggestions
    const series = await Series.aggregate([
      {
        $match: {
          $or: [
            { title: exactRegex },
            { title: searchRegex }
          ]
        }
      },
      {
        $addFields: {
          score: {
            $add: [
              // Exact start match gets highest score
              { $cond: [{ $regexMatch: { input: "$title", regex: exactRegex } }, 100, 0] },
              // Contains match gets lower score
              { $cond: [{ $regexMatch: { input: "$title", regex: searchRegex } }, 50, 0] },
              // Rating bonus (only for numeric ratings)
              { $multiply: [
                { $cond: [
                  { $regexMatch: { input: { $toString: "$rating" }, regex: "^[0-9]+(\\.[0-9]+)?$" } },
                  { $toDouble: "$rating" },
                  0
                ]},
                2
              ]}
            ]
          },
          type: { $literal: "series" }
        }
      },
      { $sort: { score: -1, rating: -1 } },
      { $limit: Math.ceil(limit / 3) },
      {
        $project: {
          title: 1,
          posterUrl: 1,
          rating: 1,
          releaseDate: 1,
          totalSeasons: 1,
          type: 1,
          imdbId: 1,
          score: 1
        }
      }
    ]);

    // Get episode suggestions (with series info)
    const episodes = await Episode.aggregate([
      {
        $match: {
          $or: [
            { title: exactRegex },
            { title: searchRegex }
          ]
        }
      },
      {
        $lookup: {
          from: 'series',
          localField: 'seriesImdbId',
          foreignField: 'imdbId',
          as: 'series'
        }
      },
      {
        $addFields: {
          score: {
            $add: [
              // Exact start match gets highest score
              { $cond: [{ $regexMatch: { input: "$title", regex: exactRegex } }, 100, 0] },
              // Contains match gets lower score
              { $cond: [{ $regexMatch: { input: "$title", regex: searchRegex } }, 50, 0] },
              // Rating bonus (only for numeric ratings)
              { $multiply: [
                { $cond: [
                  { $regexMatch: { input: { $toString: "$rating" }, regex: "^[0-9]+(\\.[0-9]+)?$" } },
                  { $toDouble: "$rating" },
                  0
                ]},
                2
              ]},
              // Recent episodes bonus
              { $cond: [{ $gte: ["$createdAt", new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] }, 10, 0] }
            ]
          },
          type: { $literal: "episode" },
          seriesTitle: { $arrayElemAt: ["$series.title", 0] },
          seriesPoster: { $arrayElemAt: ["$series.posterUrl", 0] }
        }
      },
      { $sort: { score: -1, rating: -1, createdAt: -1 } },
      { $limit: Math.ceil(limit / 3) },
      {
        $project: {
          title: 1,
          season: 1,
          episode: 1,
          rating: 1,
          airDate: 1,
          seriesTitle: 1,
          seriesPoster: 1,
          seriesImdbId: 1,
          type: 1,
          score: 1
        }
      }
    ]);

    // Combine and sort all suggestions
    suggestions.push(...movies, ...series, ...episodes);
    suggestions.sort((a, b) => {
      if (b.score !== a.score) return b.score - a.score;
      if (b.rating !== a.rating) return (b.rating || 0) - (a.rating || 0);
      return new Date(b.createdAt || b.releaseDate).getTime() - new Date(a.createdAt || a.releaseDate).getTime();
    });

    return NextResponse.json({
      suggestions: suggestions.slice(0, limit),
      query: searchQuery
    });

  } catch (error) {
    console.error('Search suggestions error:', error);
    return NextResponse.json(
      { error: 'Failed to get suggestions' },
      { status: 500 }
    );
  }
}
