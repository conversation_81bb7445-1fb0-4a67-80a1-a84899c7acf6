(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/models/SyncStatus.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mongoose/dist/browser.umd.js [app-client] (ecmascript)");
;
const SyncStatusSchema = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Schema"]({
    lastSyncTime: {
        type: Date,
        default: null
    },
    nextSyncTime: {
        type: Date,
        required: true,
        index: true
    },
    isRunning: {
        type: Boolean,
        default: false,
        index: true
    },
    lastSyncResults: {
        movies: {
            type: Number,
            default: 0
        },
        series: {
            type: Number,
            default: 0
        },
        episodes: {
            type: Number,
            default: 0
        }
    }
}, {
    timestamps: true
});
// Index for querying sync status
SyncStatusSchema.index({
    createdAt: -1
});
SyncStatusSchema.index({
    nextSyncTime: 1,
    isRunning: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].models.SyncStatus || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mongoose$2f$dist$2f$browser$2e$umd$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].model('SyncStatus', SyncStatusSchema);
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_models_SyncStatus_ts_64f319f2._.js.map