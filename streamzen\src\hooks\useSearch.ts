import { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/lib/api';

export interface SearchResult {
  _id: string;
  title: string;
  type: 'movie' | 'series' | 'episode';
  posterUrl?: string;
  rating?: number;
  releaseDate?: string;
  imdbId?: string;
  score: number;
  
  // Series specific
  totalSeasons?: number;
  
  // Episode specific
  season?: number;
  episode?: number;
  seriesTitle?: string;
  seriesPoster?: string;
  seriesImdbId?: string;
  airDate?: string;
}

export interface SearchResponse {
  results: SearchResult[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
  query: string;
}

export interface SearchSuggestion {
  _id: string;
  title: string;
  type: 'movie' | 'series' | 'episode';
  posterUrl?: string;
  rating?: number;
  releaseDate?: string;
  imdbId?: string;
  score: number;
  
  // Series specific
  totalSeasons?: number;
  
  // Episode specific
  season?: number;
  episode?: number;
  seriesTitle?: string;
  seriesPoster?: string;
  seriesImdbId?: string;
  airDate?: string;
}

export interface SuggestionsResponse {
  suggestions: SearchSuggestion[];
  query: string;
}

export const useSearch = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const search = useCallback(async (
    query: string,
    type: 'all' | 'movies' | 'series' | 'episodes' = 'all',
    page: number = 1,
    limit: number = 20
  ): Promise<SearchResponse | null> => {
    if (!query.trim()) {
      return {
        results: [],
        pagination: { page: 1, limit, total: 0, pages: 0 },
        query: ''
      };
    }

    setIsLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        q: query.trim(),
        type,
        page: page.toString(),
        limit: limit.toString()
      });

      const response = await apiClient.get(`/search?${params}`);
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed';
      setError(errorMessage);
      console.error('Search error:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const getSuggestions = useCallback(async (
    query: string,
    limit: number = 8
  ): Promise<SuggestionsResponse | null> => {
    if (!query.trim() || query.trim().length < 2) {
      return { suggestions: [], query: '' };
    }

    try {
      const params = new URLSearchParams({
        q: query.trim(),
        limit: limit.toString()
      });

      const response = await apiClient.get(`/search/suggestions?${params}`);
      return response.data;
    } catch (err) {
      console.error('Suggestions error:', err);
      return { suggestions: [], query: query.trim() };
    }
  }, []);

  return {
    search,
    getSuggestions,
    isLoading,
    error
  };
};

export const useSearchSuggestions = (query: string, limit: number = 8) => {
  const [suggestions, setSuggestions] = useState<SearchSuggestion[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { getSuggestions } = useSearch();

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!query.trim() || query.trim().length < 2) {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      const result = await getSuggestions(query, limit);
      if (result) {
        setSuggestions(result.suggestions);
      }
      setIsLoading(false);
    };

    const timeoutId = setTimeout(fetchSuggestions, 300); // Debounce
    return () => clearTimeout(timeoutId);
  }, [query, limit, getSuggestions]);

  return {
    suggestions,
    isLoading
  };
};
