'use client';

import React, { useState } from 'react';
import { Send, Loader2, CheckCircle, AlertCircle, Info } from 'lucide-react';
import Button from './ui/Button';
import { apiClient } from '@/lib/api';

const RequestForm: React.FC = () => {
  const [imdbIds, setImdbIds] = useState('');
  const [contentType, setContentType] = useState<'auto' | 'movie' | 'series'>('auto');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  const [requestId, setRequestId] = useState<string | null>(null);

  const validateImdbIds = (ids: string[]): { valid: string[]; invalid: string[] } => {
    const valid: string[] = [];
    const invalid: string[] = [];
    
    ids.forEach(id => {
      const trimmedId = id.trim();
      if (trimmedId.match(/^tt\d{7,}$/)) {
        valid.push(trimmedId);
      } else if (trimmedId) {
        invalid.push(trimmedId);
      }
    });
    
    return { valid, invalid };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!imdbIds.trim()) {
      setStatus('error');
      setMessage('Please enter at least one IMDb ID');
      return;
    }

    const ids = imdbIds.split('\n').map(id => id.trim()).filter(Boolean);
    
    if (ids.length === 0) {
      setStatus('error');
      setMessage('Please enter at least one IMDb ID');
      return;
    }

    if (ids.length > 50) {
      setStatus('error');
      setMessage('Maximum 50 IMDb IDs allowed per request');
      return;
    }

    const { valid, invalid } = validateImdbIds(ids);
    
    if (invalid.length > 0) {
      setStatus('error');
      setMessage(`Invalid IMDb IDs: ${invalid.join(', ')}. IMDb IDs should start with 'tt' followed by at least 7 digits.`);
      return;
    }

    if (valid.length === 0) {
      setStatus('error');
      setMessage('No valid IMDb IDs found');
      return;
    }

    setIsSubmitting(true);
    setStatus('loading');
    setMessage('Submitting request...');

    try {
      const response = await apiClient.createBulkRequest(valid, contentType);

      setStatus('success');
      setMessage(`Request submitted successfully! Processing ${response.totalCount} items as ${contentType === 'auto' ? 'auto-detected content' : contentType === 'movie' ? 'movies' : 'series'}.`);
      setRequestId(response.requestId);
      setImdbIds('');

      // Start polling for status updates
      pollRequestStatus(response.requestId);

    } catch (error) {
      setStatus('error');
      setMessage('Failed to submit request. Please try again.');
      console.error('Request submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const pollRequestStatus = async (id: string) => {
    try {
      const status = await apiClient.getRequestStatus(id);
      
      if (status.status === 'completed') {
        setMessage(`Request completed! Successfully processed ${status.processedCount} out of ${status.totalCount} items.`);
      } else if (status.status === 'failed') {
        setMessage(`Request completed with errors. Processed ${status.processedCount} out of ${status.totalCount} items.`);
      } else if (status.status === 'processing') {
        setMessage(`Processing... ${status.processedCount} of ${status.totalCount} completed.`);
        // Continue polling
        setTimeout(() => pollRequestStatus(id), 5000);
      }
    } catch (error) {
      console.error('Error polling request status:', error);
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="animate-spin" size={20} />;
      case 'success':
        return <CheckCircle className="text-green-400" size={20} />;
      case 'error':
        return <AlertCircle className="text-red-400" size={20} />;
      default:
        return <Send size={20} />;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'bg-green-900/20 border-green-500/20 text-green-400';
      case 'error':
        return 'bg-red-900/20 border-red-500/20 text-red-400';
      case 'loading':
        return 'bg-blue-900/20 border-blue-500/20 text-blue-400';
      default:
        return '';
    }
  };

  return (
    <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6">
      <h2 className="text-white font-semibold text-xl mb-6">Submit Request</h2>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Content Type Selector */}
        <div>
          <label className="block text-white font-medium mb-3">
            Content Type
          </label>
          <div className="grid grid-cols-3 gap-3">
            <button
              type="button"
              onClick={() => setContentType('auto')}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                contentType === 'auto'
                  ? 'bg-red-600 border-red-500 text-white'
                  : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <div className="text-center">
                <div className="font-medium">Auto Detect</div>
                <div className="text-xs opacity-75 mt-1">Recommended</div>
              </div>
            </button>
            <button
              type="button"
              onClick={() => setContentType('movie')}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                contentType === 'movie'
                  ? 'bg-red-600 border-red-500 text-white'
                  : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <div className="text-center">
                <div className="font-medium">Movies Only</div>
                <div className="text-xs opacity-75 mt-1">Force movie type</div>
              </div>
            </button>
            <button
              type="button"
              onClick={() => setContentType('series')}
              className={`p-3 rounded-lg border transition-all duration-200 ${
                contentType === 'series'
                  ? 'bg-red-600 border-red-500 text-white'
                  : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <div className="text-center">
                <div className="font-medium">Series Only</div>
                <div className="text-xs opacity-75 mt-1">Force series type</div>
              </div>
            </button>
          </div>
          <p className="text-gray-400 text-sm mt-2">
            {contentType === 'auto' && 'System will automatically detect if each IMDb ID is a movie or TV series'}
            {contentType === 'movie' && 'All provided IMDb IDs will be processed as movies'}
            {contentType === 'series' && 'All provided IMDb IDs will be processed as TV series'}
          </p>
        </div>

        <div>
          <label htmlFor="imdbIds" className="block text-white font-medium mb-2">
            IMDb IDs
          </label>
          <textarea
            id="imdbIds"
            value={imdbIds}
            onChange={(e) => setImdbIds(e.target.value)}
            placeholder="Enter IMDb IDs, one per line&#10;Example:&#10;tt0111161&#10;tt0068646&#10;tt0944947"
            className="w-full h-32 px-3 py-2 bg-gray-800 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent resize-none"
            disabled={isSubmitting}
          />
          <div className="mt-2 flex items-center justify-between text-sm">
            <span className="text-gray-400">
              {imdbIds.split('\n').filter(Boolean).length} / 50 IDs
            </span>
            <div className="flex items-center space-x-1 text-gray-400">
              <Info size={14} />
              <span>One IMDb ID per line</span>
            </div>
          </div>
        </div>

        <Button
          type="submit"
          disabled={isSubmitting || !imdbIds.trim()}
          className="w-full flex items-center justify-center space-x-2"
        >
          {getStatusIcon()}
          <span>{isSubmitting ? 'Submitting...' : 'Submit Request'}</span>
        </Button>
      </form>

      {message && (
        <div className={`mt-4 p-4 rounded-lg border ${getStatusColor()}`}>
          <p className="text-sm">{message}</p>
          {requestId && (
            <p className="text-xs mt-2 opacity-75">
              Request ID: {requestId}
            </p>
          )}
        </div>
      )}

      <div className="mt-6 p-4 bg-gray-800/50 rounded-lg">
        <h3 className="text-white font-medium mb-2">Tips</h3>
        <ul className="text-sm text-gray-400 space-y-1">
          <li>• IMDb IDs start with "tt" followed by numbers (e.g., tt0111161)</li>
          <li>• You can find IMDb IDs in the URL of any IMDb page</li>
          <li>• Maximum 50 IDs per request</li>
          <li>• Processing takes 2-5 minutes per item</li>
        </ul>
      </div>
    </div>
  );
};

export default RequestForm;
