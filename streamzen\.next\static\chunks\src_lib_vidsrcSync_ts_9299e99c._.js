(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/vidsrcSync.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_models_SyncStatus_ts_2bf568c8._.js",
  "static/chunks/node_modules_axios_lib_99999129._.js",
  "static/chunks/node_modules_mongoose_dist_browser_umd_7117f1a5.js",
  "static/chunks/node_modules_baabe4fd._.js",
  "static/chunks/node_modules_parse5_dist_56095e2e._.js",
  "static/chunks/node_modules_5d9c1fdc._.js",
  "static/chunks/src_ade7b8a1._.js",
  "static/chunks/src_lib_vidsrcSync_ts_893543cf._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/vidsrcSync.ts [app-client] (ecmascript)");
    });
});
}}),
}]);