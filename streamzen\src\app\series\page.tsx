import { Suspense } from 'react';
import { apiClient } from '@/lib/api';
import ContentGrid from '@/components/ContentGrid';
import FilterSidebar from '@/components/FilterSidebar';
import LoadingSpinner from '@/components/LoadingSpinner';
import SearchBar from '@/components/SearchBar';

interface SeriesPageProps {
  searchParams: {
    page?: string;
    genre?: string;
    year?: string;
    language?: string;
    country?: string;
    rating?: string;
    sortBy?: string;
    sortOrder?: string;
    search?: string;
  };
}

async function getSeries(searchParams: Awaited<SeriesPageProps['searchParams']>) {
  try {
    const filters = {
      page: searchParams.page ? parseInt(searchParams.page) : 1,
      limit: 24,
      genre: searchParams.genre,
      year: searchParams.year ? parseInt(searchParams.year) : undefined,
      language: searchParams.language,
      country: searchParams.country,
      rating: searchParams.rating,
      sortBy: (searchParams.sortBy as any) || 'createdAt',
      sortOrder: (searchParams.sortOrder as 'asc' | 'desc') || 'desc',
      search: searchParams.search,
    };

    const response = await apiClient.getSeries(filters);
    return response;
  } catch (error) {
    console.error('Error fetching series:', error);
    return {
      data: [],
      pagination: {
        page: 1,
        limit: 24,
        total: 0,
        pages: 0
      }
    };
  }
}

function transformSeriesToContentItem(series: any) {
  return {
    id: series._id,
    imdbId: series.imdbId,
    title: series.title,
    year: series.startYear,
    posterUrl: series.posterUrl,
    imdbRating: series.imdbRating,
    description: series.description,
    type: 'series' as const
  };
}

export default async function SeriesPage({ searchParams }: SeriesPageProps) {
  const resolvedSearchParams = await searchParams;
  const { data: series, pagination } = await getSeries(resolvedSearchParams);

  return (
    <div className="min-h-screen bg-black">
      {/* Enhanced Hero Header */}
      <div className="relative bg-black border-b border-gray-800/50 overflow-hidden">
        {/* Premium Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-0 right-1/4 w-96 h-96 bg-purple-900/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-80 h-80 bg-gray-800/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1.5s' }} />
          <div className="absolute bottom-0 right-1/2 transform translate-x-1/2 w-[600px] h-32 bg-gradient-to-t from-gray-900/30 to-transparent blur-xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-16 lg:py-24">
          <div className="mb-12">
            {/* Premium Title with Gradient Text */}
            <div className="mb-6">
              <h1 className="text-5xl lg:text-7xl xl:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-white via-purple-200 to-gray-400 mb-6 tracking-tight leading-none">
                TV Series
              </h1>
              <div className="w-24 h-1 bg-gradient-to-r from-purple-500 to-gray-600 rounded-full mb-8"></div>
            </div>

            <p className="text-xl lg:text-2xl text-gray-300 max-w-3xl leading-relaxed font-light mb-8">
              Explore premium TV series and binge-watch your favorites. From award-winning dramas to thrilling adventures, discover your next obsession.
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mb-8">
              <SearchBar
                placeholder="Search series by title, genre, cast..."
                onSearch={(query) => {
                  const params = new URLSearchParams();
                  params.set('search', query);
                  params.set('page', '1');
                  window.location.href = `/series?${params.toString()}`;
                }}
                showSuggestions={false}
                className="w-full"
              />
            </div>
          </div>

          {/* Enhanced Stats Cards */}
          <div className="flex flex-wrap items-center gap-6">
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-purple-500 rounded-full animate-pulse"></div>
                <span className="text-purple-400 font-bold text-lg">{pagination.total.toLocaleString()}</span>
                <span className="text-gray-400 text-lg">Series Available</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                <span className="text-green-400 font-bold text-lg">4K Ultra HD</span>
                <span className="text-gray-400 text-lg">Premium Quality</span>
              </div>
            </div>
            <div className="glass-elevated px-6 py-4 rounded-2xl border border-gray-700/50 backdrop-blur-sm hover:border-gray-600/50 transition-all duration-300">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-yellow-500 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
                <span className="text-yellow-400 font-bold text-lg">Binge Ready</span>
                <span className="text-gray-400 text-lg">Complete Seasons</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="relative">
        {/* Subtle Background Effects */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute top-1/3 left-1/4 w-72 h-72 bg-purple-800/5 rounded-full blur-3xl" />
          <div className="absolute bottom-1/3 right-1/4 w-64 h-64 bg-gray-700/5 rounded-full blur-3xl" />
        </div>

        <div className="relative max-w-[2560px] mx-auto px-6 lg:px-12 py-12">
          <div className="flex gap-12">
            {/* Enhanced Sidebar */}
            <div className="w-80 flex-shrink-0">
              <div className="sticky top-8">
                <FilterSidebar
                  currentFilters={searchParams}
                  basePath="/series"
                  contentType="series"
                />
              </div>
            </div>

            {/* Enhanced Content Grid */}
            <div className="flex-1">
              <div className="mb-8">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="text-3xl font-bold text-white tracking-tight">
                    {searchParams.search ? `Search Results for "${searchParams.search}"` : 'All Series'}
                  </h2>
                  <div className="glass-elevated px-4 py-2 rounded-xl border border-gray-700/50">
                    <span className="text-gray-300 text-base font-medium">
                      Page {pagination.page} of {pagination.pages}
                    </span>
                  </div>
                </div>

                <Suspense fallback={<LoadingSpinner />}>
                  <ContentGrid
                    items={series.map(transformSeriesToContentItem)}
                    pagination={pagination}
                    basePath="/series"
                    currentFilters={searchParams}
                  />
                </Suspense>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
