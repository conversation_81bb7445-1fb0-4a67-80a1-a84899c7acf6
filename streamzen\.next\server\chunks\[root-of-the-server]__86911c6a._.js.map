{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Series.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface ISeries extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string; // MPAA rating\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string; // 'ongoing', 'ended', 'cancelled'\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst SeriesSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: { \n    type: String, \n    required: true,\n    index: true \n  },\n  startYear: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  endYear: { \n    type: Number,\n    index: true \n  },\n  rating: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  creator: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  totalSeasons: Number,\n  status: { \n    type: String,\n    enum: ['ongoing', 'ended', 'cancelled'],\n    index: true \n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String // VidSrc TMDB embed URL\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nSeriesSchema.index({ startYear: -1, imdbRating: -1 });\nSeriesSchema.index({ genres: 1, startYear: -1 });\nSeriesSchema.index({ status: 1, startYear: -1 });\n// Removed text index to avoid language override issues\nSeriesSchema.index({ title: 1 });\nSeriesSchema.index({ language: 1, country: 1 });\n\nexport default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA+BA,MAAM,eAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;IACR,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,SAAS;IACT,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,cAAc;IACd,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAS;SAAY;QACvC,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe,OAAO,wBAAwB;AAChD,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,aAAa,KAAK,CAAC;IAAE,WAAW,CAAC;IAAG,YAAY,CAAC;AAAE;AACnD,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,uDAAuD;AACvD,aAAa,KAAK,CAAC;IAAE,OAAO;AAAE;AAC9B,aAAa,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;uCAE9B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Episode.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IEpisode extends Document {\n  imdbId: string; // Series IMDb ID\n  tmdbId?: string; // Series TMDB ID\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  genres?: string[]; // Genres inherited from series\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst EpisodeSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  seriesTitle: { \n    type: String, \n    required: true,\n    index: true \n  },\n  season: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episode: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episodeTitle: String,\n  airDate: { \n    type: Date,\n    index: true \n  },\n  runtime: String,\n  imdbRating: Number,\n  description: String,\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  },\n  genres: [{\n    type: String,\n    index: true\n  }]\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nEpisodeSchema.index({ imdbId: 1, season: 1, episode: 1 }, { unique: true });\nEpisodeSchema.index({ airDate: -1 });\nEpisodeSchema.index({ seriesTitle: 1, season: 1, episode: 1 });\nEpisodeSchema.index({ createdAt: -1 }); // For latest episodes\n\nexport default mongoose.models.Episode || mongoose.model<IEpisode>('Episode', EpisodeSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAuBA,MAAM,gBAAwB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACvC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,cAAc;IACd,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,SAAS;IACT,YAAY;IACZ,aAAa;IACb,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;AACJ,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,cAAc,KAAK,CAAC;IAAE,QAAQ;IAAG,QAAQ;IAAG,SAAS;AAAE,GAAG;IAAE,QAAQ;AAAK;AACzE,cAAc,KAAK,CAAC;IAAE,SAAS,CAAC;AAAE;AAClC,cAAc,KAAK,CAAC;IAAE,aAAa;IAAG,QAAQ;IAAG,SAAS;AAAE;AAC5D,cAAc,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE,IAAI,sBAAsB;uCAE/C,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAW,WAAW", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/imdbEpisodeScraper.ts"], "sourcesContent": ["import axios from 'axios';\nimport * as cheerio from 'cheerio';\n\ninterface IMDbEpisodeData {\n  season: number;\n  episode: number;\n  title: string;\n  description?: string;\n  airDate?: string;\n  rating?: number;\n  imdbId?: string;\n  duration?: string;\n}\n\ninterface IMDbSeasonData {\n  seasonNumber: number;\n  episodeCount: number;\n  episodes: IMDbEpisodeData[];\n  year?: string;\n}\n\ninterface IMDbSeriesEpisodeInfo {\n  totalEpisodes: number;\n  totalSeasons: number;\n  seasons: IMDbSeasonData[];\n  seriesTitle: string;\n  seriesYear?: string;\n  lastUpdated: string;\n}\n\nclass IMDbEpisodeScraper {\n  private static instance: IMDbEpisodeScraper;\n  private readonly baseUrl = 'https://www.imdb.com';\n  private readonly headers = {\n    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n    'Accept-Language': 'en-US,en;q=0.5',\n    'Accept-Encoding': 'gzip, deflate, br',\n    'Connection': 'keep-alive',\n    'Upgrade-Insecure-Requests': '1',\n  };\n\n  static getInstance(): IMDbEpisodeScraper {\n    if (!IMDbEpisodeScraper.instance) {\n      IMDbEpisodeScraper.instance = new IMDbEpisodeScraper();\n    }\n    return IMDbEpisodeScraper.instance;\n  }\n\n  /**\n   * ADVANCED: Get comprehensive episode information for a series from IMDb\n   */\n  async getSeriesEpisodes(imdbId: string): Promise<IMDbSeriesEpisodeInfo | null> {\n    try {\n      console.log(`🎬 ADVANCED: Scraping complete episode data from IMDb for ${imdbId}`);\n      \n      // Step 1: Get main series page to extract basic info\n      const mainPageData = await this.getMainPageData(imdbId);\n      if (!mainPageData) {\n        console.log(`❌ Could not access main page for ${imdbId}`);\n        return null;\n      }\n\n      // Step 2: Get episodes page for detailed episode information\n      const episodesData = await this.getEpisodesPageData(imdbId);\n      if (!episodesData) {\n        console.log(`❌ Could not access episodes page for ${imdbId}`);\n        return null;\n      }\n\n      // Step 3: Get detailed episode information\n      let detailedSeasons: IMDbSeasonData[] = [];\n\n      if (episodesData.seasons.length > 0) {\n        // Multi-season show: scrape each season\n        detailedSeasons = await this.getDetailedSeasonData(imdbId, episodesData.seasons);\n      } else if (mainPageData.totalEpisodes && mainPageData.totalEpisodes > 0) {\n        // Single season or no season structure: try to scrape episodes directly\n        console.log(`📺 No seasons found, attempting to scrape episodes directly...`);\n        const directEpisodes = await this.getEpisodesDirectly(imdbId);\n        if (directEpisodes && directEpisodes.length > 0) {\n          detailedSeasons = [{\n            seasonNumber: 1,\n            episodeCount: directEpisodes.length,\n            episodes: directEpisodes,\n            year: mainPageData.year\n          }];\n        } else {\n          // Fallback: Create episodes based on total count from main page\n          console.log(`📺 Direct scraping failed, creating episodes based on total count: ${mainPageData.totalEpisodes}`);\n          const fallbackEpisodes: IMDbEpisodeData[] = [];\n          const totalSeasons = mainPageData.totalSeasons || 1;\n          const episodesPerSeason = Math.ceil(mainPageData.totalEpisodes / totalSeasons);\n\n          for (let season = 1; season <= totalSeasons; season++) {\n            const seasonEpisodes: IMDbEpisodeData[] = [];\n            const startEpisode = (season - 1) * episodesPerSeason + 1;\n            const endEpisode = Math.min(season * episodesPerSeason, mainPageData.totalEpisodes);\n\n            for (let episode = startEpisode; episode <= endEpisode; episode++) {\n              const actualEpisodeNumber = episode - startEpisode + 1;\n              seasonEpisodes.push({\n                season: season,\n                episode: actualEpisodeNumber,\n                title: `Episode ${actualEpisodeNumber}`,\n                description: undefined,\n                airDate: undefined,\n                rating: undefined,\n                imdbId: undefined,\n                duration: undefined\n              });\n            }\n\n            if (seasonEpisodes.length > 0) {\n              detailedSeasons.push({\n                seasonNumber: season,\n                episodeCount: seasonEpisodes.length,\n                episodes: seasonEpisodes,\n                year: mainPageData.year\n              });\n            }\n          }\n\n          console.log(`✅ Fallback: Created ${detailedSeasons.length} seasons with ${mainPageData.totalEpisodes} episodes`);\n        }\n      }\n\n      const result: IMDbSeriesEpisodeInfo = {\n        totalEpisodes: mainPageData.totalEpisodes || episodesData.totalEpisodes,\n        totalSeasons: Math.max(mainPageData.totalSeasons || 0, detailedSeasons.length),\n        seasons: detailedSeasons,\n        seriesTitle: mainPageData.title,\n        seriesYear: mainPageData.year,\n        lastUpdated: new Date().toISOString()\n      };\n\n      console.log(`✅ IMDb scraping complete: ${result.totalEpisodes} episodes across ${result.totalSeasons} seasons`);\n      return result;\n\n    } catch (error) {\n      console.error(`❌ Error scraping IMDb episodes for ${imdbId}:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Get basic series information from main IMDb page\n   */\n  private async getMainPageData(imdbId: string): Promise<{\n    title: string;\n    year?: string;\n    totalEpisodes?: number;\n    totalSeasons?: number;\n  } | null> {\n    try {\n      const url = `${this.baseUrl}/title/${imdbId}/`;\n      console.log(`📄 Fetching main page: ${url}`);\n      \n      const response = await axios.get(url, { headers: this.headers });\n      const $ = cheerio.load(response.data);\n\n      // Extract series title\n      const title = $('h1[data-testid=\"hero__pageTitle\"] span').first().text().trim() ||\n                   $('h1').first().text().trim() ||\n                   'Unknown Title';\n\n      // Extract year\n      const yearText = $('h1[data-testid=\"hero__pageTitle\"]').text();\n      const yearMatch = yearText.match(/\\((\\d{4})\\)/);\n      const year = yearMatch ? yearMatch[1] : undefined;\n\n      // Extract episode count from episodes widget\n      const episodeCountText = $('section[data-testid=\"episodes-widget\"] .ipc-title__subtext').text();\n      const totalEpisodes = episodeCountText ? parseInt(episodeCountText) : undefined;\n\n      // Extract season count from season selector - try multiple selectors\n      const seasonSelectors = [\n        'select[id=\"browse-episodes-season\"] option',\n        '#browse-episodes-season option',\n        'select[aria-label*=\"season\"] option',\n        '.ipc-simple-select__input option'\n      ];\n\n      let totalSeasons = 0;\n      let foundSeasons = false;\n\n      for (const selector of seasonSelectors) {\n        const seasonOptions = $(selector);\n        if (seasonOptions.length > 0) {\n          console.log(`📍 Found season options on main page using: ${selector}`);\n          seasonOptions.each((_, element) => {\n            const value = $(element).attr('value');\n            if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {\n              totalSeasons = Math.max(totalSeasons, parseInt(value));\n              foundSeasons = true;\n            }\n          });\n          if (foundSeasons) break;\n        }\n      }\n\n      if (!foundSeasons) {\n        console.log(`❌ No season selector found on main page`);\n      }\n\n      console.log(`📊 Main page data: ${title} (${year}) - ${totalEpisodes} episodes, ${totalSeasons} seasons`);\n\n      return {\n        title,\n        year,\n        totalEpisodes,\n        totalSeasons\n      };\n\n    } catch (error) {\n      console.error(`❌ Error fetching main page for ${imdbId}:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Get episodes page data with season information\n   */\n  private async getEpisodesPageData(imdbId: string): Promise<{\n    totalEpisodes: number;\n    seasons: { seasonNumber: number; year?: string }[];\n  } | null> {\n    try {\n      const url = `${this.baseUrl}/title/${imdbId}/episodes/`;\n      console.log(`📺 Fetching episodes page: ${url}`);\n      \n      const response = await axios.get(url, { headers: this.headers });\n      const $ = cheerio.load(response.data);\n\n      // Extract total episodes\n      const episodeCountText = $('section[data-testid=\"episodes-widget\"] .ipc-title__subtext').text() ||\n                              $('.episodes-section .ipc-title__subtext').text();\n      const totalEpisodes = episodeCountText ? parseInt(episodeCountText) : 0;\n\n      // Extract seasons from dropdown - use multiple selectors to find the season dropdown\n      const seasons: { seasonNumber: number; year?: string }[] = [];\n      const seasonSelectors = [\n        'select[id=\"browse-episodes-season\"] option',\n        '#browse-episodes-season option',\n        'select[aria-label*=\"season\"] option',\n        '.ipc-simple-select__input option'\n      ];\n\n      let seasonOptions: cheerio.Cheerio<cheerio.Element> | null = null;\n      for (const selector of seasonSelectors) {\n        seasonOptions = $(selector);\n        if (seasonOptions.length > 0) {\n          console.log(`📍 Found season options using selector: ${selector}`);\n          break;\n        }\n      }\n\n      if (seasonOptions && seasonOptions.length > 0) {\n        seasonOptions.each((_, element) => {\n          const value = $(element).attr('value');\n          const text = $(element).text().trim();\n\n          console.log(`🔍 Season option: value=\"${value}\", text=\"${text}\"`);\n\n          if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {\n            seasons.push({\n              seasonNumber: parseInt(value),\n              year: undefined // Will be filled in detailed scraping\n            });\n            console.log(`✅ Added season ${parseInt(value)}`);\n          }\n        });\n      } else {\n        console.log(`❌ No season dropdown found with any selector`);\n      }\n\n      // Also extract years if available\n      const yearOptions = $('select[id=\"browse-episodes-year\"] option[value]');\n      const years: string[] = [];\n      yearOptions.each((_, element) => {\n        const value = $(element).attr('value');\n        if (value && value !== '' && value !== 'SEE_ALL' && !isNaN(parseInt(value))) {\n          years.push(value);\n        }\n      });\n\n      console.log(`📊 Episodes page data: ${totalEpisodes} episodes, ${seasons.length} seasons, years: ${years.join(', ')}`);\n\n      return {\n        totalEpisodes,\n        seasons: seasons.sort((a, b) => a.seasonNumber - b.seasonNumber)\n      };\n\n    } catch (error) {\n      console.error(`❌ Error fetching episodes page for ${imdbId}:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Get detailed episode data for each season\n   */\n  private async getDetailedSeasonData(imdbId: string, seasons: { seasonNumber: number; year?: string }[]): Promise<IMDbSeasonData[]> {\n    const detailedSeasons: IMDbSeasonData[] = [];\n\n    for (const season of seasons) {\n      try {\n        console.log(`🔍 Scraping detailed data for Season ${season.seasonNumber}...`);\n        \n        const seasonData = await this.getSeasonEpisodes(imdbId, season.seasonNumber);\n        if (seasonData) {\n          detailedSeasons.push(seasonData);\n        }\n\n        // Add delay to avoid rate limiting\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n      } catch (error) {\n        console.error(`❌ Error scraping Season ${season.seasonNumber}:`, error);\n        // Continue with other seasons\n      }\n    }\n\n    return detailedSeasons.sort((a, b) => a.seasonNumber - b.seasonNumber);\n  }\n\n  /**\n   * Get episodes directly from the episodes page (for single-season shows)\n   */\n  private async getEpisodesDirectly(imdbId: string): Promise<IMDbEpisodeData[]> {\n    try {\n      const url = `${this.baseUrl}/title/${imdbId}/episodes/`;\n      console.log(`📺 Fetching episodes directly: ${url}`);\n\n      const response = await axios.get(url, { headers: this.headers });\n      const $ = cheerio.load(response.data);\n\n      const episodes: IMDbEpisodeData[] = [];\n\n      // Try multiple selectors for episode items\n      const episodeSelectors = [\n        '.episode-item-wrapper',\n        '.list_item',\n        '[data-testid*=\"episode\"]',\n        '.titleColumn',\n        '.episode-list-item',\n        '.ipc-list-card'\n      ];\n\n      let foundEpisodes = false;\n      for (const selector of episodeSelectors) {\n        const episodeElements = $(selector);\n        if (episodeElements.length > 0) {\n          console.log(`📍 Found ${episodeElements.length} episodes using selector: ${selector}`);\n\n          episodeElements.each((index, element) => {\n            try {\n              const $episode = $(element);\n\n              // Extract episode number (try multiple patterns)\n              let episodeNumber = index + 1; // fallback to index\n              const episodeText = $episode.find('.episode-number, .episode_number, [data-testid*=\"episode-number\"]').text() ||\n                                 $episode.find('strong').first().text() ||\n                                 $episode.text();\n\n              const episodeMatch = episodeText.match(/E(\\d+)/i) ||\n                                  episodeText.match(/Episode\\s*(\\d+)/i) ||\n                                  episodeText.match(/(\\d+)\\./);\n              if (episodeMatch) {\n                episodeNumber = parseInt(episodeMatch[1]);\n              }\n\n              // Extract title\n              const title = $episode.find('.episode-title, .episode_title, a[title]').text().trim() ||\n                           $episode.find('a').first().text().trim() ||\n                           $episode.find('h4, h3').text().trim() ||\n                           `Episode ${episodeNumber}`;\n\n              // Extract description\n              const description = $episode.find('.episode-description, .item_description, .plot').text().trim();\n\n              // Extract air date\n              const airDate = $episode.find('.airdate, .episode-aired, [data-testid*=\"air-date\"]').text().trim();\n\n              // Extract rating\n              const ratingText = $episode.find('.rating, .ipl-rating-star__rating, [data-testid*=\"rating\"]').text();\n              const rating = ratingText ? parseFloat(ratingText) : undefined;\n\n              // Extract episode IMDb ID\n              const episodeLink = $episode.find('a[href*=\"/title/tt\"]').attr('href');\n              const episodeImdbId = episodeLink ? episodeLink.match(/\\/title\\/(tt\\d+)/)?.[1] : undefined;\n\n              if (title && title !== `Episode ${episodeNumber}` || episodeNumber > 0) {\n                episodes.push({\n                  season: 1, // Default to season 1 for direct episodes\n                  episode: episodeNumber,\n                  title: title || `Episode ${episodeNumber}`,\n                  description: description || undefined,\n                  airDate: airDate || undefined,\n                  rating: rating,\n                  imdbId: episodeImdbId,\n                  duration: undefined\n                });\n              }\n\n            } catch (error) {\n              console.error(`❌ Error parsing episode:`, error);\n            }\n          });\n\n          foundEpisodes = true;\n          break; // Stop after finding episodes with first working selector\n        }\n      }\n\n      if (!foundEpisodes) {\n        console.log(`❌ No episodes found with any selector`);\n      }\n\n      // Sort episodes by episode number\n      episodes.sort((a, b) => a.episode - b.episode);\n\n      console.log(`✅ Direct episode scraping: Found ${episodes.length} episodes`);\n      return episodes;\n\n    } catch (error) {\n      console.error(`❌ Error fetching episodes directly for ${imdbId}:`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Get episodes for a specific season\n   */\n  private async getSeasonEpisodes(imdbId: string, seasonNumber: number): Promise<IMDbSeasonData | null> {\n    try {\n      const url = `${this.baseUrl}/title/${imdbId}/episodes/?season=${seasonNumber}`;\n      console.log(`📺 Fetching season ${seasonNumber}: ${url}`);\n      \n      const response = await axios.get(url, { headers: this.headers });\n      const $ = cheerio.load(response.data);\n\n      const episodes: IMDbEpisodeData[] = [];\n\n      // Extract episodes from the page\n      $('.episode-item-wrapper, .list_item, [data-testid*=\"episode\"]').each((_, element) => {\n        try {\n          const $episode = $(element);\n          \n          // Extract episode number\n          const episodeText = $episode.find('.episode-number, .episode_number, [data-testid*=\"episode-number\"]').text() ||\n                             $episode.find('strong').first().text();\n          const episodeMatch = episodeText.match(/E(\\d+)/i) || episodeText.match(/(\\d+)\\./);\n          const episodeNumber = episodeMatch ? parseInt(episodeMatch[1]) : 0;\n\n          if (episodeNumber === 0) return; // Skip if we can't find episode number\n\n          // Extract title\n          const title = $episode.find('.episode-title, .episode_title, a[title]').text().trim() ||\n                       $episode.find('a').first().text().trim() ||\n                       `Episode ${episodeNumber}`;\n\n          // Extract description\n          const description = $episode.find('.episode-description, .item_description, .plot').text().trim();\n\n          // Extract air date\n          const airDate = $episode.find('.airdate, .episode-aired, [data-testid*=\"air-date\"]').text().trim();\n\n          // Extract rating\n          const ratingText = $episode.find('.rating, .ipl-rating-star__rating, [data-testid*=\"rating\"]').text();\n          const rating = ratingText ? parseFloat(ratingText) : undefined;\n\n          // Extract episode IMDb ID\n          const episodeLink = $episode.find('a[href*=\"/title/tt\"]').attr('href');\n          const episodeImdbId = episodeLink ? episodeLink.match(/\\/title\\/(tt\\d+)/)?.[1] : undefined;\n\n          episodes.push({\n            season: seasonNumber,\n            episode: episodeNumber,\n            title: title || `Episode ${episodeNumber}`,\n            description: description || undefined,\n            airDate: airDate || undefined,\n            rating: rating,\n            imdbId: episodeImdbId,\n            duration: undefined\n          });\n\n        } catch (error) {\n          console.error(`❌ Error parsing episode:`, error);\n        }\n      });\n\n      // Sort episodes by episode number\n      episodes.sort((a, b) => a.episode - b.episode);\n\n      console.log(`✅ Season ${seasonNumber}: Found ${episodes.length} episodes`);\n\n      return {\n        seasonNumber,\n        episodeCount: episodes.length,\n        episodes,\n        year: undefined // Could be extracted from air dates if needed\n      };\n\n    } catch (error) {\n      console.error(`❌ Error fetching season ${seasonNumber} for ${imdbId}:`, error);\n      return null;\n    }\n  }\n}\n\nexport default IMDbEpisodeScraper;\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AA6BA,MAAM;IACJ,OAAe,SAA6B;IAC3B,UAAU,uBAAuB;IACjC,UAAU;QACzB,cAAc;QACd,UAAU;QACV,mBAAmB;QACnB,mBAAmB;QACnB,cAAc;QACd,6BAA6B;IAC/B,EAAE;IAEF,OAAO,cAAkC;QACvC,IAAI,CAAC,mBAAmB,QAAQ,EAAE;YAChC,mBAAmB,QAAQ,GAAG,IAAI;QACpC;QACA,OAAO,mBAAmB,QAAQ;IACpC;IAEA;;GAEC,GACD,MAAM,kBAAkB,MAAc,EAAyC;QAC7E,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,0DAA0D,EAAE,QAAQ;YAEjF,qDAAqD;YACrD,MAAM,eAAe,MAAM,IAAI,CAAC,eAAe,CAAC;YAChD,IAAI,CAAC,cAAc;gBACjB,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,QAAQ;gBACxD,OAAO;YACT;YAEA,6DAA6D;YAC7D,MAAM,eAAe,MAAM,IAAI,CAAC,mBAAmB,CAAC;YACpD,IAAI,CAAC,cAAc;gBACjB,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,QAAQ;gBAC5D,OAAO;YACT;YAEA,2CAA2C;YAC3C,IAAI,kBAAoC,EAAE;YAE1C,IAAI,aAAa,OAAO,CAAC,MAAM,GAAG,GAAG;gBACnC,wCAAwC;gBACxC,kBAAkB,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,aAAa,OAAO;YACjF,OAAO,IAAI,aAAa,aAAa,IAAI,aAAa,aAAa,GAAG,GAAG;gBACvE,wEAAwE;gBACxE,QAAQ,GAAG,CAAC,CAAC,8DAA8D,CAAC;gBAC5E,MAAM,iBAAiB,MAAM,IAAI,CAAC,mBAAmB,CAAC;gBACtD,IAAI,kBAAkB,eAAe,MAAM,GAAG,GAAG;oBAC/C,kBAAkB;wBAAC;4BACjB,cAAc;4BACd,cAAc,eAAe,MAAM;4BACnC,UAAU;4BACV,MAAM,aAAa,IAAI;wBACzB;qBAAE;gBACJ,OAAO;oBACL,gEAAgE;oBAChE,QAAQ,GAAG,CAAC,CAAC,mEAAmE,EAAE,aAAa,aAAa,EAAE;oBAC9G,MAAM,mBAAsC,EAAE;oBAC9C,MAAM,eAAe,aAAa,YAAY,IAAI;oBAClD,MAAM,oBAAoB,KAAK,IAAI,CAAC,aAAa,aAAa,GAAG;oBAEjE,IAAK,IAAI,SAAS,GAAG,UAAU,cAAc,SAAU;wBACrD,MAAM,iBAAoC,EAAE;wBAC5C,MAAM,eAAe,CAAC,SAAS,CAAC,IAAI,oBAAoB;wBACxD,MAAM,aAAa,KAAK,GAAG,CAAC,SAAS,mBAAmB,aAAa,aAAa;wBAElF,IAAK,IAAI,UAAU,cAAc,WAAW,YAAY,UAAW;4BACjE,MAAM,sBAAsB,UAAU,eAAe;4BACrD,eAAe,IAAI,CAAC;gCAClB,QAAQ;gCACR,SAAS;gCACT,OAAO,CAAC,QAAQ,EAAE,qBAAqB;gCACvC,aAAa;gCACb,SAAS;gCACT,QAAQ;gCACR,QAAQ;gCACR,UAAU;4BACZ;wBACF;wBAEA,IAAI,eAAe,MAAM,GAAG,GAAG;4BAC7B,gBAAgB,IAAI,CAAC;gCACnB,cAAc;gCACd,cAAc,eAAe,MAAM;gCACnC,UAAU;gCACV,MAAM,aAAa,IAAI;4BACzB;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,gBAAgB,MAAM,CAAC,cAAc,EAAE,aAAa,aAAa,CAAC,SAAS,CAAC;gBACjH;YACF;YAEA,MAAM,SAAgC;gBACpC,eAAe,aAAa,aAAa,IAAI,aAAa,aAAa;gBACvE,cAAc,KAAK,GAAG,CAAC,aAAa,YAAY,IAAI,GAAG,gBAAgB,MAAM;gBAC7E,SAAS;gBACT,aAAa,aAAa,KAAK;gBAC/B,YAAY,aAAa,IAAI;gBAC7B,aAAa,IAAI,OAAO,WAAW;YACrC;YAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,OAAO,aAAa,CAAC,iBAAiB,EAAE,OAAO,YAAY,CAAC,QAAQ,CAAC;YAC9G,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC/D,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAc,gBAAgB,MAAc,EAKlC;QACR,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;YAC9C,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,KAAK;YAE3C,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBAAE,SAAS,IAAI,CAAC,OAAO;YAAC;YAC9D,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;YAEpC,uBAAuB;YACvB,MAAM,QAAQ,EAAE,0CAA0C,KAAK,GAAG,IAAI,GAAG,IAAI,MAChE,EAAE,MAAM,KAAK,GAAG,IAAI,GAAG,IAAI,MAC3B;YAEb,eAAe;YACf,MAAM,WAAW,EAAE,qCAAqC,IAAI;YAC5D,MAAM,YAAY,SAAS,KAAK,CAAC;YACjC,MAAM,OAAO,YAAY,SAAS,CAAC,EAAE,GAAG;YAExC,6CAA6C;YAC7C,MAAM,mBAAmB,EAAE,8DAA8D,IAAI;YAC7F,MAAM,gBAAgB,mBAAmB,SAAS,oBAAoB;YAEtE,qEAAqE;YACrE,MAAM,kBAAkB;gBACtB;gBACA;gBACA;gBACA;aACD;YAED,IAAI,eAAe;YACnB,IAAI,eAAe;YAEnB,KAAK,MAAM,YAAY,gBAAiB;gBACtC,MAAM,gBAAgB,EAAE;gBACxB,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,UAAU;oBACrE,cAAc,IAAI,CAAC,CAAC,GAAG;wBACrB,MAAM,QAAQ,EAAE,SAAS,IAAI,CAAC;wBAC9B,IAAI,SAAS,UAAU,MAAM,UAAU,aAAa,CAAC,MAAM,SAAS,SAAS;4BAC3E,eAAe,KAAK,GAAG,CAAC,cAAc,SAAS;4BAC/C,eAAe;wBACjB;oBACF;oBACA,IAAI,cAAc;gBACpB;YACF;YAEA,IAAI,CAAC,cAAc;gBACjB,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD;YAEA,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,EAAE,EAAE,KAAK,IAAI,EAAE,cAAc,WAAW,EAAE,aAAa,QAAQ,CAAC;YAExG,OAAO;gBACL;gBACA;gBACA;gBACA;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,OAAO,CAAC,CAAC,EAAE;YAC3D,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAc,oBAAoB,MAAc,EAGtC;QACR,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,UAAU,CAAC;YACvD,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,KAAK;YAE/C,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBAAE,SAAS,IAAI,CAAC,OAAO;YAAC;YAC9D,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;YAEpC,yBAAyB;YACzB,MAAM,mBAAmB,EAAE,8DAA8D,IAAI,MACrE,EAAE,yCAAyC,IAAI;YACvE,MAAM,gBAAgB,mBAAmB,SAAS,oBAAoB;YAEtE,qFAAqF;YACrF,MAAM,UAAqD,EAAE;YAC7D,MAAM,kBAAkB;gBACtB;gBACA;gBACA;gBACA;aACD;YAED,IAAI,gBAAyD;YAC7D,KAAK,MAAM,YAAY,gBAAiB;gBACtC,gBAAgB,EAAE;gBAClB,IAAI,cAAc,MAAM,GAAG,GAAG;oBAC5B,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU;oBACjE;gBACF;YACF;YAEA,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;gBAC7C,cAAc,IAAI,CAAC,CAAC,GAAG;oBACrB,MAAM,QAAQ,EAAE,SAAS,IAAI,CAAC;oBAC9B,MAAM,OAAO,EAAE,SAAS,IAAI,GAAG,IAAI;oBAEnC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,MAAM,SAAS,EAAE,KAAK,CAAC,CAAC;oBAEhE,IAAI,SAAS,UAAU,MAAM,UAAU,aAAa,CAAC,MAAM,SAAS,SAAS;wBAC3E,QAAQ,IAAI,CAAC;4BACX,cAAc,SAAS;4BACvB,MAAM,UAAU,sCAAsC;wBACxD;wBACA,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,SAAS,QAAQ;oBACjD;gBACF;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;YAC5D;YAEA,kCAAkC;YAClC,MAAM,cAAc,EAAE;YACtB,MAAM,QAAkB,EAAE;YAC1B,YAAY,IAAI,CAAC,CAAC,GAAG;gBACnB,MAAM,QAAQ,EAAE,SAAS,IAAI,CAAC;gBAC9B,IAAI,SAAS,UAAU,MAAM,UAAU,aAAa,CAAC,MAAM,SAAS,SAAS;oBAC3E,MAAM,IAAI,CAAC;gBACb;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,WAAW,EAAE,QAAQ,MAAM,CAAC,iBAAiB,EAAE,MAAM,IAAI,CAAC,OAAO;YAErH,OAAO;gBACL;gBACA,SAAS,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;YACjE;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC/D,OAAO;QACT;IACF;IAEA;;GAEC,GACD,MAAc,sBAAsB,MAAc,EAAE,OAAkD,EAA6B;QACjI,MAAM,kBAAoC,EAAE;QAE5C,KAAK,MAAM,UAAU,QAAS;YAC5B,IAAI;gBACF,QAAQ,GAAG,CAAC,CAAC,qCAAqC,EAAE,OAAO,YAAY,CAAC,GAAG,CAAC;gBAE5E,MAAM,aAAa,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,OAAO,YAAY;gBAC3E,IAAI,YAAY;oBACd,gBAAgB,IAAI,CAAC;gBACvB;gBAEA,mCAAmC;gBACnC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEnD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,OAAO,YAAY,CAAC,CAAC,CAAC,EAAE;YACjE,8BAA8B;YAChC;QACF;QAEA,OAAO,gBAAgB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,YAAY,GAAG,EAAE,YAAY;IACvE;IAEA;;GAEC,GACD,MAAc,oBAAoB,MAAc,EAA8B;QAC5E,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,UAAU,CAAC;YACvD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,KAAK;YAEnD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBAAE,SAAS,IAAI,CAAC,OAAO;YAAC;YAC9D,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;YAEpC,MAAM,WAA8B,EAAE;YAEtC,2CAA2C;YAC3C,MAAM,mBAAmB;gBACvB;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,IAAI,gBAAgB;YACpB,KAAK,MAAM,YAAY,iBAAkB;gBACvC,MAAM,kBAAkB,EAAE;gBAC1B,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,gBAAgB,MAAM,CAAC,0BAA0B,EAAE,UAAU;oBAErF,gBAAgB,IAAI,CAAC,CAAC,OAAO;wBAC3B,IAAI;4BACF,MAAM,WAAW,EAAE;4BAEnB,iDAAiD;4BACjD,IAAI,gBAAgB,QAAQ,GAAG,oBAAoB;4BACnD,MAAM,cAAc,SAAS,IAAI,CAAC,qEAAqE,IAAI,MACxF,SAAS,IAAI,CAAC,UAAU,KAAK,GAAG,IAAI,MACpC,SAAS,IAAI;4BAEhC,MAAM,eAAe,YAAY,KAAK,CAAC,cACnB,YAAY,KAAK,CAAC,uBAClB,YAAY,KAAK,CAAC;4BACtC,IAAI,cAAc;gCAChB,gBAAgB,SAAS,YAAY,CAAC,EAAE;4BAC1C;4BAEA,gBAAgB;4BAChB,MAAM,QAAQ,SAAS,IAAI,CAAC,4CAA4C,IAAI,GAAG,IAAI,MACtE,SAAS,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI,MACtC,SAAS,IAAI,CAAC,UAAU,IAAI,GAAG,IAAI,MACnC,CAAC,QAAQ,EAAE,eAAe;4BAEvC,sBAAsB;4BACtB,MAAM,cAAc,SAAS,IAAI,CAAC,kDAAkD,IAAI,GAAG,IAAI;4BAE/F,mBAAmB;4BACnB,MAAM,UAAU,SAAS,IAAI,CAAC,uDAAuD,IAAI,GAAG,IAAI;4BAEhG,iBAAiB;4BACjB,MAAM,aAAa,SAAS,IAAI,CAAC,8DAA8D,IAAI;4BACnG,MAAM,SAAS,aAAa,WAAW,cAAc;4BAErD,0BAA0B;4BAC1B,MAAM,cAAc,SAAS,IAAI,CAAC,wBAAwB,IAAI,CAAC;4BAC/D,MAAM,gBAAgB,cAAc,YAAY,KAAK,CAAC,qBAAqB,CAAC,EAAE,GAAG;4BAEjF,IAAI,SAAS,UAAU,CAAC,QAAQ,EAAE,eAAe,IAAI,gBAAgB,GAAG;gCACtE,SAAS,IAAI,CAAC;oCACZ,QAAQ;oCACR,SAAS;oCACT,OAAO,SAAS,CAAC,QAAQ,EAAE,eAAe;oCAC1C,aAAa,eAAe;oCAC5B,SAAS,WAAW;oCACpB,QAAQ;oCACR,QAAQ;oCACR,UAAU;gCACZ;4BACF;wBAEF,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAE;wBAC5C;oBACF;oBAEA,gBAAgB;oBAChB,OAAO,0DAA0D;gBACnE;YACF;YAEA,IAAI,CAAC,eAAe;gBAClB,QAAQ,GAAG,CAAC,CAAC,qCAAqC,CAAC;YACrD;YAEA,kCAAkC;YAClC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;YAE7C,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;YAC1E,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,OAAO,CAAC,CAAC,EAAE;YACnE,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAc,kBAAkB,MAAc,EAAE,YAAoB,EAAkC;QACpG,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,kBAAkB,EAAE,cAAc;YAC9E,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,aAAa,EAAE,EAAE,KAAK;YAExD,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBAAE,SAAS,IAAI,CAAC,OAAO;YAAC;YAC9D,MAAM,IAAI,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;YAEpC,MAAM,WAA8B,EAAE;YAEtC,iCAAiC;YACjC,EAAE,+DAA+D,IAAI,CAAC,CAAC,GAAG;gBACxE,IAAI;oBACF,MAAM,WAAW,EAAE;oBAEnB,yBAAyB;oBACzB,MAAM,cAAc,SAAS,IAAI,CAAC,qEAAqE,IAAI,MACxF,SAAS,IAAI,CAAC,UAAU,KAAK,GAAG,IAAI;oBACvD,MAAM,eAAe,YAAY,KAAK,CAAC,cAAc,YAAY,KAAK,CAAC;oBACvE,MAAM,gBAAgB,eAAe,SAAS,YAAY,CAAC,EAAE,IAAI;oBAEjE,IAAI,kBAAkB,GAAG,QAAQ,uCAAuC;oBAExE,gBAAgB;oBAChB,MAAM,QAAQ,SAAS,IAAI,CAAC,4CAA4C,IAAI,GAAG,IAAI,MACtE,SAAS,IAAI,CAAC,KAAK,KAAK,GAAG,IAAI,GAAG,IAAI,MACtC,CAAC,QAAQ,EAAE,eAAe;oBAEvC,sBAAsB;oBACtB,MAAM,cAAc,SAAS,IAAI,CAAC,kDAAkD,IAAI,GAAG,IAAI;oBAE/F,mBAAmB;oBACnB,MAAM,UAAU,SAAS,IAAI,CAAC,uDAAuD,IAAI,GAAG,IAAI;oBAEhG,iBAAiB;oBACjB,MAAM,aAAa,SAAS,IAAI,CAAC,8DAA8D,IAAI;oBACnG,MAAM,SAAS,aAAa,WAAW,cAAc;oBAErD,0BAA0B;oBAC1B,MAAM,cAAc,SAAS,IAAI,CAAC,wBAAwB,IAAI,CAAC;oBAC/D,MAAM,gBAAgB,cAAc,YAAY,KAAK,CAAC,qBAAqB,CAAC,EAAE,GAAG;oBAEjF,SAAS,IAAI,CAAC;wBACZ,QAAQ;wBACR,SAAS;wBACT,OAAO,SAAS,CAAC,QAAQ,EAAE,eAAe;wBAC1C,aAAa,eAAe;wBAC5B,SAAS,WAAW;wBACpB,QAAQ;wBACR,QAAQ;wBACR,UAAU;oBACZ;gBAEF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,CAAC,EAAE;gBAC5C;YACF;YAEA,kCAAkC;YAClC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,OAAO,GAAG,EAAE,OAAO;YAE7C,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,aAAa,QAAQ,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;YAEzE,OAAO;gBACL;gBACA,cAAc,SAAS,MAAM;gBAC7B;gBACA,MAAM,UAAU,8CAA8C;YAChE;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wBAAwB,EAAE,aAAa,KAAK,EAAE,OAAO,CAAC,CAAC,EAAE;YACxE,OAAO;QACT;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/series/%5Bid%5D/episodes/check/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport connectDB from '@/lib/mongodb';\nimport Series from '@/models/Series';\nimport Episode from '@/models/Episode';\nimport VidSrcAPI from '@/lib/vidsrc';\nimport IMDbEpisodeScraper from '@/lib/imdbEpisodeScraper';\n\ninterface EpisodeStats {\n  totalFound: number;\n  newEpisodesAdded: number;\n  existingEpisodesUpdated: number;\n  totalEpisodesInDB: number;\n  seasonBreakdown: Record<number, {\n    episodeCount: number;\n    episodes: number[];\n    missingEpisodes: number[];\n    hasGaps: boolean;\n    completionPercentage: number;\n    expectedEpisodes?: number;\n  }>;\n  episodeRange: {\n    minSeason: number;\n    maxSeason: number;\n    minEpisode: number;\n    maxEpisode: number;\n  };\n  duplicatesRemoved: number;\n  errors: string[];\n  verificationResults: {\n    totalSeasonsFound: number;\n    totalSeasonsWithGaps: number;\n    overallCompletionPercentage: number;\n    missingSeasonRanges: string[];\n    suspiciousGaps: Array<{\n      season: number;\n      missingRange: string;\n      severity: 'minor' | 'major' | 'critical';\n    }>;\n  };\n}\n\ninterface VidSrcEpisode {\n  season: number;\n  episode: number;\n  embed_url: string;\n  embed_url_tmdb?: string;\n}\n\nexport async function POST(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    await connectDB();\n    const { id } = await params;\n\n    // Get the series by IMDb ID (the id parameter is actually the IMDb ID)\n    const series = await Series.findOne({ imdbId: id });\n    if (!series) {\n      return NextResponse.json({ error: 'Series not found' }, { status: 404 });\n    }\n\n    console.log(`🔍 Advanced Episode Analysis for: ${series.title} (${series.imdbId})`);\n\n    // Initialize advanced statistics\n    const stats: EpisodeStats = {\n      totalFound: 0,\n      newEpisodesAdded: 0,\n      existingEpisodesUpdated: 0,\n      totalEpisodesInDB: 0,\n      seasonBreakdown: {},\n      episodeRange: {\n        minSeason: Infinity,\n        maxSeason: 0,\n        minEpisode: Infinity,\n        maxEpisode: 0\n      },\n      duplicatesRemoved: 0,\n      errors: [],\n      verificationResults: {\n        totalSeasonsFound: 0,\n        totalSeasonsWithGaps: 0,\n        overallCompletionPercentage: 0,\n        missingSeasonRanges: [],\n        suspiciousGaps: []\n      }\n    };\n\n    // Get existing episodes from database using both seriesId and imdbId for accuracy\n    const existingEpisodes = await Episode.find({\n      $or: [\n        { seriesId: series._id },\n        { imdbId: series.imdbId }\n      ]\n    });\n    const existingEpisodeMap = new Map<string, any>();\n\n    existingEpisodes.forEach(ep => {\n      const key = `S${ep.season}E${ep.episode}`;\n      existingEpisodeMap.set(key, ep);\n    });\n\n    console.log(`📊 Current database state: ${existingEpisodes.length} episodes (seriesId: ${series._id}, imdbId: ${series.imdbId})`);\n\n    // ADVANCED: Get comprehensive episode data from IMDb (most reliable source)\n    console.log(`🎬 ADVANCED: IMDb-based episode verification for series: ${series.title} (${series.imdbId})`);\n    const imdbScraper = IMDbEpisodeScraper.getInstance();\n    const imdbEpisodeData = await imdbScraper.getSeriesEpisodes(series.imdbId);\n\n    if (!imdbEpisodeData) {\n      console.log(`❌ Could not retrieve episode data from IMDb for ${series.title}`);\n      return NextResponse.json({\n        message: 'Could not retrieve episode data from IMDb',\n        stats: {\n          ...stats,\n          totalEpisodesInDB: existingEpisodes.length\n        }\n      });\n    }\n\n    console.log(`🎯 IMDb Analysis: Found ${imdbEpisodeData.totalEpisodes} episodes across ${imdbEpisodeData.totalSeasons} seasons`);\n    console.log(`📊 Season breakdown: ${imdbEpisodeData.seasons.map(s => `S${s.seasonNumber}(${s.episodeCount})`).join(', ')}`);\n\n    // Step 2: Generate VidSrc embed URLs for all IMDb episodes\n    console.log(`🔗 Generating VidSrc embed URLs for all IMDb episodes...`);\n\n    // Step 3: Process ALL episodes from IMDb data (IMDb is the authoritative source)\n    const allIMDbEpisodes: Array<{\n      season: number;\n      episode: number;\n      title: string;\n      description?: string;\n      airDate?: string;\n      rating?: number;\n      embedUrl?: string;\n      embedUrlTmdb?: string;\n    }> = [];\n\n    // Process every episode that IMDb says exists and generate VidSrc URLs\n    imdbEpisodeData.seasons.forEach(season => {\n      season.episodes.forEach(episode => {\n        // Generate VidSrc embed URL directly\n        const vidsrcEmbedUrl = `https://vidsrc.me/embed/tv?imdb=${series.imdbId}&season=${episode.season}&episode=${episode.episode}`;\n        const vidsrcTmdbUrl = `https://vidsrc.me/embed/tv?tmdb=${series.tmdbId || ''}&season=${episode.season}&episode=${episode.episode}`;\n\n        allIMDbEpisodes.push({\n          season: episode.season,\n          episode: episode.episode,\n          title: episode.title,\n          description: episode.description,\n          airDate: episode.airDate,\n          rating: episode.rating && !isNaN(episode.rating) ? episode.rating : undefined,\n          embedUrl: vidsrcEmbedUrl,\n          embedUrlTmdb: vidsrcTmdbUrl\n        });\n\n        console.log(`🔗 Generated VidSrc URL for S${episode.season}E${episode.episode}: ${vidsrcEmbedUrl}`);\n      });\n    });\n\n    stats.totalFound = allIMDbEpisodes.length;\n    console.log(`✅ IMDb-Based Analysis: ${allIMDbEpisodes.length} episodes from IMDb, all with generated VidSrc streaming links`);\n\n    // Analyze episode range from IMDb data\n    if (allIMDbEpisodes.length > 0) {\n      stats.episodeRange.minSeason = Math.min(...allIMDbEpisodes.map(e => e.season));\n      stats.episodeRange.maxSeason = Math.max(...allIMDbEpisodes.map(e => e.season));\n      stats.episodeRange.minEpisode = Math.min(...allIMDbEpisodes.map(e => e.episode));\n      stats.episodeRange.maxEpisode = Math.max(...allIMDbEpisodes.map(e => e.episode));\n    }\n\n    console.log(`📈 Episode Range: S${stats.episodeRange.minSeason}-S${stats.episodeRange.maxSeason}, E${stats.episodeRange.minEpisode}-E${stats.episodeRange.maxEpisode}`);\n\n    // Process each episode from IMDb data (authoritative source)\n    for (const imdbEpisode of allIMDbEpisodes) {\n      try {\n        const episodeKey = `S${imdbEpisode.season}E${imdbEpisode.episode}`;\n        const existingEpisode = existingEpisodeMap.get(episodeKey);\n\n        if (existingEpisode) {\n          // Update existing episode with new data from IMDb and VidSrc\n          let updated = false;\n\n          // Update title if different\n          if (existingEpisode.episodeTitle !== imdbEpisode.title) {\n            existingEpisode.episodeTitle = imdbEpisode.title;\n            updated = true;\n          }\n\n          // Update description if available\n          if (imdbEpisode.description && existingEpisode.description !== imdbEpisode.description) {\n            existingEpisode.description = imdbEpisode.description;\n            updated = true;\n          }\n\n          // Update air date if available\n          if (imdbEpisode.airDate && existingEpisode.airDate !== imdbEpisode.airDate) {\n            existingEpisode.airDate = imdbEpisode.airDate;\n            updated = true;\n          }\n\n          // Update IMDb rating if available and valid\n          if (imdbEpisode.rating && !isNaN(imdbEpisode.rating) && existingEpisode.imdbRating !== imdbEpisode.rating) {\n            existingEpisode.imdbRating = imdbEpisode.rating;\n            updated = true;\n          }\n\n          // Update embed URLs if available\n          if (imdbEpisode.embedUrl && existingEpisode.embedUrl !== imdbEpisode.embedUrl) {\n            existingEpisode.embedUrl = imdbEpisode.embedUrl;\n            existingEpisode.vidsrcUrl = imdbEpisode.embedUrl;\n            updated = true;\n          }\n\n          if (imdbEpisode.embedUrlTmdb && existingEpisode.embedUrlTmdb !== imdbEpisode.embedUrlTmdb) {\n            existingEpisode.embedUrlTmdb = imdbEpisode.embedUrlTmdb;\n            existingEpisode.vidsrcTmdbUrl = imdbEpisode.embedUrlTmdb;\n            updated = true;\n          }\n\n          if (updated) {\n            existingEpisode.updatedAt = new Date();\n            await existingEpisode.save();\n            stats.existingEpisodesUpdated++;\n            console.log(`🔄 Updated episode ${episodeKey} with IMDb data`);\n          }\n\n          continue;\n        }\n\n        // Create new episode with complete IMDb metadata and generated VidSrc embed URL\n        const newEpisode = new Episode({\n          seriesId: series._id,\n          seriesTitle: series.title,\n          imdbId: series.imdbId,\n          season: imdbEpisode.season,\n          episode: imdbEpisode.episode,\n          episodeTitle: imdbEpisode.title,\n          description: imdbEpisode.description || `${series.title} - Season ${imdbEpisode.season}, Episode ${imdbEpisode.episode}`,\n          posterUrl: series.posterUrl, // Use series poster as fallback\n          genres: series.genres || [], // Populate genres from series\n          embedUrl: imdbEpisode.embedUrl,\n          embedUrlTmdb: imdbEpisode.embedUrlTmdb || '',\n          vidsrcUrl: imdbEpisode.embedUrl,\n          vidsrcTmdbUrl: imdbEpisode.embedUrlTmdb || '',\n          airDate: imdbEpisode.airDate || new Date().toISOString(),\n          runtime: series.runtime || '45 min',\n          imdbRating: imdbEpisode.rating && !isNaN(imdbEpisode.rating) ? imdbEpisode.rating : undefined,\n          createdAt: new Date(),\n          updatedAt: new Date()\n        });\n\n        await newEpisode.save();\n        stats.newEpisodesAdded++;\n\n        console.log(`✅ Added new episode ${episodeKey} with IMDb data and VidSrc embed URL`);\n      } catch (error) {\n        const errorMsg = `Failed to process episode S${imdbEpisode.season}E${imdbEpisode.episode}: ${error instanceof Error ? error.message : 'Unknown error'}`;\n        stats.errors.push(errorMsg);\n        console.error(`❌ ${errorMsg}`);\n      }\n    }\n\n    // Advanced season analysis and gap detection\n    const allEpisodesInDB = await Episode.find({\n      $or: [\n        { seriesId: series._id },\n        { imdbId: series.imdbId }\n      ]\n    }).sort({ season: 1, episode: 1 });\n    stats.totalEpisodesInDB = allEpisodesInDB.length;\n\n    console.log(`📊 Final database query: Found ${allEpisodesInDB.length} episodes for series ${series.title}`);\n\n    // ADVANCED: Build comprehensive season breakdown with intelligent gap analysis\n    const seasonMap = new Map<number, number[]>();\n    allEpisodesInDB.forEach(ep => {\n      if (!seasonMap.has(ep.season)) {\n        seasonMap.set(ep.season, []);\n      }\n      seasonMap.get(ep.season)!.push(ep.episode);\n    });\n\n    // Use IMDb data as the authoritative source for expected episodes\n    const imdbSeasonMap = new Map<number, number>();\n    if (imdbEpisodeData) {\n      imdbEpisodeData.seasons.forEach(season => {\n        imdbSeasonMap.set(season.seasonNumber, season.episodeCount);\n      });\n    }\n\n    console.log(`🔍 ADVANCED: Analyzing ${imdbSeasonMap.size} seasons based on IMDb data...`);\n\n    // Advanced gap analysis - ONLY analyze seasons that exist in IMDb data\n    for (const [season, expectedEpisodes] of imdbSeasonMap) {\n      episodes.sort((a, b) => a - b);\n      const completionPercentage = Math.round((episodes.length / expectedEpisodes) * 100);\n      console.log(`📊 Season ${season}: ${episodes.length}/${expectedEpisodes} episodes (${completionPercentage}% complete)`);\n\n      // Find all missing episodes based on IMDb data\n      const allMissingEpisodes: number[] = [];\n      // Check against IMDb episode count\n      for (let i = 1; i <= expectedEpisodes; i++) {\n        if (!episodes.includes(i)) {\n          allMissingEpisodes.push(i);\n        }\n      // Analyze gap severity based on IMDb reference\n      let gapSeverity: 'minor' | 'major' | 'critical' = 'minor';\n      const missingPercentage = (allMissingEpisodes.length / expectedEpisodes) * 100;\n      if (missingPercentage > 50) {\n        gapSeverity = 'critical'; // More than 50% missing\n      } else if (missingPercentage > 20) {\n        gapSeverity = 'major'; // More than 20% missing\n      }\n\n      stats.seasonBreakdown[season] = {\n        episodeCount: episodes.length,\n        episodes: episodes,\n        missingEpisodes: allMissingEpisodes,\n        hasGaps: allMissingEpisodes.length > 0,\n        completionPercentage: completionPercentage,\n        expectedEpisodes: expectedEpisodes\n      };\n\n      // Log detailed analysis\n      if (allMissingEpisodes.length > 0) {\n        console.log(`⚠️ Season ${season} Analysis:`);\n        console.log(`   📊 Episodes Found: ${episodes.length} (${episodes.join(', ')})`);\n        console.log(`   ❌ Missing Episodes: ${allMissingEpisodes.length} (${allMissingEpisodes.join(', ')})`);\n        console.log(`   📈 Completion: ${completionPercentage}% of ${expectedEpisodes} episodes`);\n        console.log(`   🚨 Severity: ${gapSeverity.toUpperCase()}`);\n\n        // Add to suspicious gaps\n        if (allMissingEpisodes.length > 0) {\n          stats.verificationResults.suspiciousGaps.push({\n            season: season,\n            missingRange: allMissingEpisodes.length > 5\n              ? `${allMissingEpisodes[0]}-${allMissingEpisodes[allMissingEpisodes.length - 1]} (${allMissingEpisodes.length} episodes)`\n              : allMissingEpisodes.join(', '),\n            severity: gapSeverity\n          });\n        }\n      } else {\n        console.log(`✅ Season ${season}: Complete (${episodes.length} episodes: ${episodes.join(', ')})`);\n      }\n    }\n\n    // Check for missing season ranges\n    const seasonNumbers = Array.from(seasonMap.keys()).sort((a, b) => a - b);\n    const missingSeasonRanges: string[] = [];\n\n    if (seasonNumbers.length > 1) {\n      const minSeason = seasonNumbers[0];\n      const maxSeason = seasonNumbers[seasonNumbers.length - 1];\n\n      for (let s = minSeason; s <= maxSeason; s++) {\n        if (!seasonNumbers.includes(s)) {\n          missingSeasonRanges.push(`Season ${s}`);\n        }\n      }\n    }\n\n    // Calculate overall verification results\n    const totalSeasonsWithGaps = Object.values(stats.seasonBreakdown).filter(s => s.hasGaps).length;\n    const totalEpisodesExpected = Object.values(stats.seasonBreakdown)\n      .reduce((sum, s) => sum + (s.expectedEpisodes || s.episodeCount), 0);\n    const overallCompletionPercentage = totalEpisodesExpected > 0\n      ? Math.round((stats.totalEpisodesInDB / totalEpisodesExpected) * 100)\n      : 100;\n\n    stats.verificationResults = {\n      totalSeasonsFound: seasonMap.size,\n      totalSeasonsWithGaps: totalSeasonsWithGaps,\n      overallCompletionPercentage: overallCompletionPercentage,\n      missingSeasonRanges: missingSeasonRanges,\n      suspiciousGaps: stats.verificationResults.suspiciousGaps\n    };\n\n    // Update series total episodes count\n    if (series.totalEpisodes !== stats.totalEpisodesInDB) {\n      series.totalEpisodes = stats.totalEpisodesInDB;\n      series.updatedAt = new Date();\n      await series.save();\n      console.log(`📊 Updated series total episodes: ${series.totalEpisodes} → ${stats.totalEpisodesInDB}`);\n    }\n\n    // ADVANCED: IMDb-based verification summary\n    console.log(`🎯 IMDB-BASED EPISODE VERIFICATION COMPLETE:`);\n    console.log(`   🎬 IMDb Episodes Found: ${imdbEpisodeData.totalEpisodes} across ${imdbEpisodeData.totalSeasons} seasons`);\n    console.log(`   📺 Episodes in Database: ${stats.totalEpisodesInDB}`);\n    console.log(`   🔗 All Episodes have Generated VidSrc URLs: YES`);\n    console.log(`   ➕ New Episodes Added: ${stats.newEpisodesAdded}`);\n    console.log(`   🔄 Existing Episodes Updated: ${stats.existingEpisodesUpdated}`);\n    console.log(`   🗂️ Total Seasons Found: ${stats.verificationResults.totalSeasonsFound}`);\n    console.log(`   ⚠️ Seasons with Gaps: ${stats.verificationResults.totalSeasonsWithGaps}`);\n    console.log(`   📊 Overall Completion: ${stats.verificationResults.overallCompletionPercentage}%`);\n    console.log(`   ❌ Processing Errors: ${stats.errors.length}`);\n\n    if (stats.verificationResults.missingSeasonRanges.length > 0) {\n      console.log(`   🚨 Missing Seasons: ${stats.verificationResults.missingSeasonRanges.join(', ')}`);\n    }\n\n    if (stats.verificationResults.suspiciousGaps.length > 0) {\n      console.log(`   🔍 Suspicious Gaps Detected:`);\n      stats.verificationResults.suspiciousGaps.forEach(gap => {\n        console.log(`      Season ${gap.season}: ${gap.missingRange} (${gap.severity.toUpperCase()})`);\n      });\n    }\n\n    // Quality assessment\n    let qualityAssessment = 'EXCELLENT';\n    if (stats.verificationResults.overallCompletionPercentage < 90) qualityAssessment = 'GOOD';\n    if (stats.verificationResults.overallCompletionPercentage < 70) qualityAssessment = 'FAIR';\n    if (stats.verificationResults.overallCompletionPercentage < 50) qualityAssessment = 'POOR';\n    if (stats.verificationResults.suspiciousGaps.some(g => g.severity === 'critical')) qualityAssessment = 'CRITICAL';\n\n    console.log(`   🏆 Episode Collection Quality: ${qualityAssessment}`);\n\n    return NextResponse.json({\n      message: 'Advanced episode analysis completed successfully',\n      stats: stats,\n      episodes: allEpisodesInDB,\n      lastChecked: new Date().toISOString()\n    });\n\n  } catch (error) {\n    console.error('❌ Advanced episode analysis failed:', error);\n    return NextResponse.json(\n      { error: 'Failed to perform advanced episode analysis' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AAEA;;;;;;AA2CO,eAAe,KACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QAErB,uEAAuE;QACvE,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;YAAE,QAAQ;QAAG;QACjD,IAAI,CAAC,QAAQ;YACX,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAmB,GAAG;gBAAE,QAAQ;YAAI;QACxE;QAEA,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;QAElF,iCAAiC;QACjC,MAAM,QAAsB;YAC1B,YAAY;YACZ,kBAAkB;YAClB,yBAAyB;YACzB,mBAAmB;YACnB,iBAAiB,CAAC;YAClB,cAAc;gBACZ,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,YAAY;YACd;YACA,mBAAmB;YACnB,QAAQ,EAAE;YACV,qBAAqB;gBACnB,mBAAmB;gBACnB,sBAAsB;gBACtB,6BAA6B;gBAC7B,qBAAqB,EAAE;gBACvB,gBAAgB,EAAE;YACpB;QACF;QAEA,kFAAkF;QAClF,MAAM,mBAAmB,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;YAC1C,KAAK;gBACH;oBAAE,UAAU,OAAO,GAAG;gBAAC;gBACvB;oBAAE,QAAQ,OAAO,MAAM;gBAAC;aACzB;QACH;QACA,MAAM,qBAAqB,IAAI;QAE/B,iBAAiB,OAAO,CAAC,CAAA;YACvB,MAAM,MAAM,CAAC,CAAC,EAAE,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE;YACzC,mBAAmB,GAAG,CAAC,KAAK;QAC9B;QAEA,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,iBAAiB,MAAM,CAAC,qBAAqB,EAAE,OAAO,GAAG,CAAC,UAAU,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;QAEhI,4EAA4E;QAC5E,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,OAAO,KAAK,CAAC,EAAE,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;QACzG,MAAM,cAAc,kIAAA,CAAA,UAAkB,CAAC,WAAW;QAClD,MAAM,kBAAkB,MAAM,YAAY,iBAAiB,CAAC,OAAO,MAAM;QAEzE,IAAI,CAAC,iBAAiB;YACpB,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,OAAO,KAAK,EAAE;YAC7E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;oBACL,GAAG,KAAK;oBACR,mBAAmB,iBAAiB,MAAM;gBAC5C;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,gBAAgB,aAAa,CAAC,iBAAiB,EAAE,gBAAgB,YAAY,CAAC,QAAQ,CAAC;QAC9H,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO;QAE1H,2DAA2D;QAC3D,QAAQ,GAAG,CAAC,CAAC,wDAAwD,CAAC;QAEtE,iFAAiF;QACjF,MAAM,kBASD,EAAE;QAEP,uEAAuE;QACvE,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAA;YAC9B,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAA;gBACtB,qCAAqC;gBACrC,MAAM,iBAAiB,CAAC,gCAAgC,EAAE,OAAO,MAAM,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;gBAC7H,MAAM,gBAAgB,CAAC,gCAAgC,EAAE,OAAO,MAAM,IAAI,GAAG,QAAQ,EAAE,QAAQ,MAAM,CAAC,SAAS,EAAE,QAAQ,OAAO,EAAE;gBAElI,gBAAgB,IAAI,CAAC;oBACnB,QAAQ,QAAQ,MAAM;oBACtB,SAAS,QAAQ,OAAO;oBACxB,OAAO,QAAQ,KAAK;oBACpB,aAAa,QAAQ,WAAW;oBAChC,SAAS,QAAQ,OAAO;oBACxB,QAAQ,QAAQ,MAAM,IAAI,CAAC,MAAM,QAAQ,MAAM,IAAI,QAAQ,MAAM,GAAG;oBACpE,UAAU;oBACV,cAAc;gBAChB;gBAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE,gBAAgB;YACpG;QACF;QAEA,MAAM,UAAU,GAAG,gBAAgB,MAAM;QACzC,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,gBAAgB,MAAM,CAAC,8DAA8D,CAAC;QAE5H,uCAAuC;QACvC,IAAI,gBAAgB,MAAM,GAAG,GAAG;YAC9B,MAAM,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAC5E,MAAM,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,IAAI,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;YAC5E,MAAM,YAAY,CAAC,UAAU,GAAG,KAAK,GAAG,IAAI,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;YAC9E,MAAM,YAAY,CAAC,UAAU,GAAG,KAAK,GAAG,IAAI,gBAAgB,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO;QAChF;QAEA,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,MAAM,YAAY,CAAC,SAAS,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC,SAAS,CAAC,GAAG,EAAE,MAAM,YAAY,CAAC,UAAU,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC,UAAU,EAAE;QAEtK,6DAA6D;QAC7D,KAAK,MAAM,eAAe,gBAAiB;YACzC,IAAI;gBACF,MAAM,aAAa,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,EAAE;gBAClE,MAAM,kBAAkB,mBAAmB,GAAG,CAAC;gBAE/C,IAAI,iBAAiB;oBACnB,6DAA6D;oBAC7D,IAAI,UAAU;oBAEd,4BAA4B;oBAC5B,IAAI,gBAAgB,YAAY,KAAK,YAAY,KAAK,EAAE;wBACtD,gBAAgB,YAAY,GAAG,YAAY,KAAK;wBAChD,UAAU;oBACZ;oBAEA,kCAAkC;oBAClC,IAAI,YAAY,WAAW,IAAI,gBAAgB,WAAW,KAAK,YAAY,WAAW,EAAE;wBACtF,gBAAgB,WAAW,GAAG,YAAY,WAAW;wBACrD,UAAU;oBACZ;oBAEA,+BAA+B;oBAC/B,IAAI,YAAY,OAAO,IAAI,gBAAgB,OAAO,KAAK,YAAY,OAAO,EAAE;wBAC1E,gBAAgB,OAAO,GAAG,YAAY,OAAO;wBAC7C,UAAU;oBACZ;oBAEA,4CAA4C;oBAC5C,IAAI,YAAY,MAAM,IAAI,CAAC,MAAM,YAAY,MAAM,KAAK,gBAAgB,UAAU,KAAK,YAAY,MAAM,EAAE;wBACzG,gBAAgB,UAAU,GAAG,YAAY,MAAM;wBAC/C,UAAU;oBACZ;oBAEA,iCAAiC;oBACjC,IAAI,YAAY,QAAQ,IAAI,gBAAgB,QAAQ,KAAK,YAAY,QAAQ,EAAE;wBAC7E,gBAAgB,QAAQ,GAAG,YAAY,QAAQ;wBAC/C,gBAAgB,SAAS,GAAG,YAAY,QAAQ;wBAChD,UAAU;oBACZ;oBAEA,IAAI,YAAY,YAAY,IAAI,gBAAgB,YAAY,KAAK,YAAY,YAAY,EAAE;wBACzF,gBAAgB,YAAY,GAAG,YAAY,YAAY;wBACvD,gBAAgB,aAAa,GAAG,YAAY,YAAY;wBACxD,UAAU;oBACZ;oBAEA,IAAI,SAAS;wBACX,gBAAgB,SAAS,GAAG,IAAI;wBAChC,MAAM,gBAAgB,IAAI;wBAC1B,MAAM,uBAAuB;wBAC7B,QAAQ,GAAG,CAAC,CAAC,mBAAmB,EAAE,WAAW,eAAe,CAAC;oBAC/D;oBAEA;gBACF;gBAEA,gFAAgF;gBAChF,MAAM,aAAa,IAAI,0HAAA,CAAA,UAAO,CAAC;oBAC7B,UAAU,OAAO,GAAG;oBACpB,aAAa,OAAO,KAAK;oBACzB,QAAQ,OAAO,MAAM;oBACrB,QAAQ,YAAY,MAAM;oBAC1B,SAAS,YAAY,OAAO;oBAC5B,cAAc,YAAY,KAAK;oBAC/B,aAAa,YAAY,WAAW,IAAI,GAAG,OAAO,KAAK,CAAC,UAAU,EAAE,YAAY,MAAM,CAAC,UAAU,EAAE,YAAY,OAAO,EAAE;oBACxH,WAAW,OAAO,SAAS;oBAC3B,QAAQ,OAAO,MAAM,IAAI,EAAE;oBAC3B,UAAU,YAAY,QAAQ;oBAC9B,cAAc,YAAY,YAAY,IAAI;oBAC1C,WAAW,YAAY,QAAQ;oBAC/B,eAAe,YAAY,YAAY,IAAI;oBAC3C,SAAS,YAAY,OAAO,IAAI,IAAI,OAAO,WAAW;oBACtD,SAAS,OAAO,OAAO,IAAI;oBAC3B,YAAY,YAAY,MAAM,IAAI,CAAC,MAAM,YAAY,MAAM,IAAI,YAAY,MAAM,GAAG;oBACpF,WAAW,IAAI;oBACf,WAAW,IAAI;gBACjB;gBAEA,MAAM,WAAW,IAAI;gBACrB,MAAM,gBAAgB;gBAEtB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,WAAW,oCAAoC,CAAC;YACrF,EAAE,OAAO,OAAO;gBACd,MAAM,WAAW,CAAC,2BAA2B,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,EAAE,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;gBACvJ,MAAM,MAAM,CAAC,IAAI,CAAC;gBAClB,QAAQ,KAAK,CAAC,CAAC,EAAE,EAAE,UAAU;YAC/B;QACF;QAEA,6CAA6C;QAC7C,MAAM,kBAAkB,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC;YACzC,KAAK;gBACH;oBAAE,UAAU,OAAO,GAAG;gBAAC;gBACvB;oBAAE,QAAQ,OAAO,MAAM;gBAAC;aACzB;QACH,GAAG,IAAI,CAAC;YAAE,QAAQ;YAAG,SAAS;QAAE;QAChC,MAAM,iBAAiB,GAAG,gBAAgB,MAAM;QAEhD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,gBAAgB,MAAM,CAAC,qBAAqB,EAAE,OAAO,KAAK,EAAE;QAE1G,+EAA+E;QAC/E,MAAM,YAAY,IAAI;QACtB,gBAAgB,OAAO,CAAC,CAAA;YACtB,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG;gBAC7B,UAAU,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE;YAC7B;YACA,UAAU,GAAG,CAAC,GAAG,MAAM,EAAG,IAAI,CAAC,GAAG,OAAO;QAC3C;QAEA,kEAAkE;QAClE,MAAM,gBAAgB,IAAI;QAC1B,IAAI,iBAAiB;YACnB,gBAAgB,OAAO,CAAC,OAAO,CAAC,CAAA;gBAC9B,cAAc,GAAG,CAAC,OAAO,YAAY,EAAE,OAAO,YAAY;YAC5D;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,IAAI,CAAC,8BAA8B,CAAC;QAExF,uEAAuE;QACvE,KAAK,MAAM,CAAC,QAAQ,iBAAiB,IAAI,cAAe;YACtD,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YAC5B,MAAM,uBAAuB,KAAK,KAAK,CAAC,AAAC,SAAS,MAAM,GAAG,mBAAoB;YAC/E,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,iBAAiB,WAAW,EAAE,qBAAqB,WAAW,CAAC;YAEtH,+CAA+C;YAC/C,MAAM,qBAA+B,EAAE;YACvC,mCAAmC;YACnC,IAAK,IAAI,IAAI,GAAG,KAAK,kBAAkB,IAAK;gBAC1C,IAAI,CAAC,SAAS,QAAQ,CAAC,IAAI;oBACzB,mBAAmB,IAAI,CAAC;gBAC1B;gBACF,+CAA+C;gBAC/C,IAAI,cAA8C;gBAClD,MAAM,oBAAoB,AAAC,mBAAmB,MAAM,GAAG,mBAAoB;gBAC3E,IAAI,oBAAoB,IAAI;oBAC1B,cAAc,YAAY,wBAAwB;gBACpD,OAAO,IAAI,oBAAoB,IAAI;oBACjC,cAAc,SAAS,wBAAwB;gBACjD;gBAEA,MAAM,eAAe,CAAC,OAAO,GAAG;oBAC9B,cAAc,SAAS,MAAM;oBAC7B,UAAU;oBACV,iBAAiB;oBACjB,SAAS,mBAAmB,MAAM,GAAG;oBACrC,sBAAsB;oBACtB,kBAAkB;gBACpB;gBAEA,wBAAwB;gBACxB,IAAI,mBAAmB,MAAM,GAAG,GAAG;oBACjC,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,UAAU,CAAC;oBAC3C,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC;oBAC/E,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,mBAAmB,MAAM,CAAC,EAAE,EAAE,mBAAmB,IAAI,CAAC,MAAM,CAAC,CAAC;oBACpG,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,qBAAqB,KAAK,EAAE,iBAAiB,SAAS,CAAC;oBACxF,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,YAAY,WAAW,IAAI;oBAE1D,yBAAyB;oBACzB,IAAI,mBAAmB,MAAM,GAAG,GAAG;wBACjC,MAAM,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC;4BAC5C,QAAQ;4BACR,cAAc,mBAAmB,MAAM,GAAG,IACtC,GAAG,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE,kBAAkB,CAAC,mBAAmB,MAAM,GAAG,EAAE,CAAC,EAAE,EAAE,mBAAmB,MAAM,CAAC,UAAU,CAAC,GACvH,mBAAmB,IAAI,CAAC;4BAC5B,UAAU;wBACZ;oBACF;gBACF,OAAO;oBACL,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,OAAO,YAAY,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC;gBAClG;YACF;YAEA,kCAAkC;YAClC,MAAM,gBAAgB,MAAM,IAAI,CAAC,UAAU,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YACtE,MAAM,sBAAgC,EAAE;YAExC,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,MAAM,YAAY,aAAa,CAAC,EAAE;gBAClC,MAAM,YAAY,aAAa,CAAC,cAAc,MAAM,GAAG,EAAE;gBAEzD,IAAK,IAAI,IAAI,WAAW,KAAK,WAAW,IAAK;oBAC3C,IAAI,CAAC,cAAc,QAAQ,CAAC,IAAI;wBAC9B,oBAAoB,IAAI,CAAC,CAAC,OAAO,EAAE,GAAG;oBACxC;gBACF;YACF;YAEA,yCAAyC;YACzC,MAAM,uBAAuB,OAAO,MAAM,CAAC,MAAM,eAAe,EAAE,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;YAC/F,MAAM,wBAAwB,OAAO,MAAM,CAAC,MAAM,eAAe,EAC9D,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,gBAAgB,IAAI,EAAE,YAAY,GAAG;YACpE,MAAM,8BAA8B,wBAAwB,IACxD,KAAK,KAAK,CAAC,AAAC,MAAM,iBAAiB,GAAG,wBAAyB,OAC/D;YAEJ,MAAM,mBAAmB,GAAG;gBAC1B,mBAAmB,UAAU,IAAI;gBACjC,sBAAsB;gBACtB,6BAA6B;gBAC7B,qBAAqB;gBACrB,gBAAgB,MAAM,mBAAmB,CAAC,cAAc;YAC1D;YAEA,qCAAqC;YACrC,IAAI,OAAO,aAAa,KAAK,MAAM,iBAAiB,EAAE;gBACpD,OAAO,aAAa,GAAG,MAAM,iBAAiB;gBAC9C,OAAO,SAAS,GAAG,IAAI;gBACvB,MAAM,OAAO,IAAI;gBACjB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,OAAO,aAAa,CAAC,GAAG,EAAE,MAAM,iBAAiB,EAAE;YACtG;YAEA,4CAA4C;YAC5C,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC;YAC1D,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,gBAAgB,aAAa,CAAC,QAAQ,EAAE,gBAAgB,YAAY,CAAC,QAAQ,CAAC;YACxH,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,MAAM,iBAAiB,EAAE;YACpE,QAAQ,GAAG,CAAC,CAAC,kDAAkD,CAAC;YAChE,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,MAAM,gBAAgB,EAAE;YAChE,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,MAAM,uBAAuB,EAAE;YAC/E,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,MAAM,mBAAmB,CAAC,iBAAiB,EAAE;YACxF,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,MAAM,mBAAmB,CAAC,oBAAoB,EAAE;YACxF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,MAAM,mBAAmB,CAAC,2BAA2B,CAAC,CAAC,CAAC;YACjG,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,MAAM,MAAM,CAAC,MAAM,EAAE;YAE5D,IAAI,MAAM,mBAAmB,CAAC,mBAAmB,CAAC,MAAM,GAAG,GAAG;gBAC5D,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,MAAM,mBAAmB,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO;YAClG;YAEA,IAAI,MAAM,mBAAmB,CAAC,cAAc,CAAC,MAAM,GAAG,GAAG;gBACvD,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC;gBAC7C,MAAM,mBAAmB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAA;oBAC/C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,YAAY,CAAC,EAAE,EAAE,IAAI,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC;gBAC/F;YACF;YAEA,qBAAqB;YACrB,IAAI,oBAAoB;YACxB,IAAI,MAAM,mBAAmB,CAAC,2BAA2B,GAAG,IAAI,oBAAoB;YACpF,IAAI,MAAM,mBAAmB,CAAC,2BAA2B,GAAG,IAAI,oBAAoB;YACpF,IAAI,MAAM,mBAAmB,CAAC,2BAA2B,GAAG,IAAI,oBAAoB;YACpF,IAAI,MAAM,mBAAmB,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,aAAa,oBAAoB;YAEvG,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,mBAAmB;YAEpE,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa,IAAI,OAAO,WAAW;YACrC;QAEF;AAAE,iBAAK;IAOT;AAAA", "debugId": null}}]}