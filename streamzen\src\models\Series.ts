import mongoose, { Schema, Document } from 'mongoose';

export interface ISeries extends Document {
  imdbId: string;
  tmdbId?: string;
  title: string;
  startYear: number;
  endYear?: number;
  rating?: string; // MPAA rating
  imdbRating?: number;
  imdbVotes?: string;
  popularity?: number;
  popularityDelta?: number;
  posterUrl?: string;
  trailerUrl?: string;
  description?: string;
  genres?: string[];
  creator?: string;
  cast?: string[];
  language?: string;
  country?: string;
  totalSeasons?: number;
  status?: string; // 'ongoing', 'ended', 'cancelled'
  embedUrl: string;
  embedUrlTmdb?: string;
  vidsrcUrl?: string; // VidSrc embed URL
  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL
  createdAt: Date;
  updatedAt: Date;
}

const SeriesSchema: Schema = new Schema({
  imdbId: { 
    type: String, 
    required: true, 
    unique: true,
    index: true 
  },
  tmdbId: { 
    type: String, 
    index: true 
  },
  title: { 
    type: String, 
    required: true,
    index: true 
  },
  startYear: { 
    type: Number, 
    required: true,
    index: true 
  },
  endYear: { 
    type: Number,
    index: true 
  },
  rating: String,
  imdbRating: { 
    type: Number,
    index: true 
  },
  imdbVotes: String,
  popularity: { 
    type: Number,
    index: true 
  },
  popularityDelta: Number,
  posterUrl: String,
  trailerUrl: String,
  description: String,
  genres: [{ 
    type: String,
    index: true 
  }],
  creator: String,
  cast: [String],
  language: { 
    type: String,
    index: true 
  },
  country: {
    type: String,
    index: true
  },
  totalSeasons: Number,
  status: { 
    type: String,
    enum: ['ongoing', 'ended', 'cancelled'],
    index: true 
  },
  embedUrl: {
    type: String,
    required: true
  },
  embedUrlTmdb: String,
  vidsrcUrl: String, // VidSrc embed URL
  vidsrcTmdbUrl: String // VidSrc TMDB embed URL
}, {
  timestamps: true
});

// Compound indexes for better query performance
SeriesSchema.index({ startYear: -1, imdbRating: -1 });
SeriesSchema.index({ genres: 1, startYear: -1 });
SeriesSchema.index({ status: 1, startYear: -1 });
// Removed text index to avoid language override issues
SeriesSchema.index({ title: 1 });
SeriesSchema.index({ language: 1, country: 1 });

export default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);
