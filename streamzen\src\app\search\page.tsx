'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { Search, Filter, Grid, List, Star, Calendar, Play, Clock } from 'lucide-react';
import { useSearch, SearchResult } from '@/hooks/useSearch';
import SearchBar from '@/components/SearchBar';
import Image from 'next/image';
import Link from 'next/link';

const SearchContent: React.FC = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { search, isLoading, error } = useSearch();
  
  const [results, setResults] = useState<SearchResult[]>([]);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 24,
    total: 0,
    pages: 0
  });
  const [currentQuery, setCurrentQuery] = useState('');
  const [contentType, setContentType] = useState<'all' | 'movies' | 'series' | 'episodes'>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Get initial search query from URL
  useEffect(() => {
    const query = searchParams.get('q') || '';
    const type = (searchParams.get('type') as 'all' | 'movies' | 'series' | 'episodes') || 'all';
    const page = parseInt(searchParams.get('page') || '1');
    
    setCurrentQuery(query);
    setContentType(type);
    setPagination(prev => ({ ...prev, page }));
    
    if (query) {
      performSearch(query, type, page);
    }
  }, [searchParams]);

  const performSearch = async (query: string, type: typeof contentType = 'all', page: number = 1) => {
    const result = await search(query, type, page, pagination.limit);
    if (result) {
      setResults(result.results);
      setPagination(result.pagination);
      setCurrentQuery(result.query);
    }
  };

  const handleSearch = (query: string) => {
    const params = new URLSearchParams();
    params.set('q', query);
    params.set('type', contentType);
    params.set('page', '1');
    router.push(`/search?${params.toString()}`);
  };

  const handleTypeChange = (type: typeof contentType) => {
    setContentType(type);
    const params = new URLSearchParams();
    params.set('q', currentQuery);
    params.set('type', type);
    params.set('page', '1');
    router.push(`/search?${params.toString()}`);
  };

  const handlePageChange = (page: number) => {
    const params = new URLSearchParams();
    params.set('q', currentQuery);
    params.set('type', contentType);
    params.set('page', page.toString());
    router.push(`/search?${params.toString()}`);
  };

  const formatRating = (rating?: number) => {
    return rating ? rating.toFixed(1) : 'N/A';
  };

  const formatDate = (date?: string) => {
    if (!date) return '';
    return new Date(date).getFullYear().toString();
  };

  const getContentLink = (result: SearchResult) => {
    if (result.type === 'movie') {
      return `/watch/movie/${result.imdbId}`;
    } else if (result.type === 'series') {
      return `/watch/series/${result.imdbId}`;
    } else if (result.type === 'episode') {
      return `/watch/series/${result.seriesImdbId}?season=${result.season}&episode=${result.episode}`;
    }
    return '#';
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Header */}
      <div className="bg-gray-900/50 backdrop-blur-xl border-b border-gray-800/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-12 h-12 bg-gradient-to-br from-red-600 to-red-700 rounded-2xl flex items-center justify-center">
              <Search className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Search Results</h1>
              {currentQuery && (
                <p className="text-gray-400 mt-1">
                  {pagination.total} results for "{currentQuery}"
                </p>
              )}
            </div>
          </div>

          {/* Search Bar */}
          <div className="mb-6">
            <SearchBar
              placeholder="Search movies, series, episodes..."
              onSearch={handleSearch}
              className="max-w-2xl"
            />
          </div>

          {/* Filters */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Content Type Filter */}
              <div className="flex items-center space-x-2">
                <Filter className="w-5 h-5 text-gray-400" />
                <div className="flex bg-gray-800/50 rounded-xl p-1">
                  {[
                    { key: 'all', label: 'All' },
                    { key: 'movies', label: 'Movies' },
                    { key: 'series', label: 'Series' },
                    { key: 'episodes', label: 'Episodes' }
                  ].map(({ key, label }) => (
                    <button
                      key={key}
                      onClick={() => handleTypeChange(key as typeof contentType)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                        contentType === key
                          ? 'bg-red-600 text-white shadow-lg'
                          : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                      }`}
                    >
                      {label}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* View Mode Toggle */}
            <div className="flex bg-gray-800/50 rounded-xl p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  viewMode === 'grid'
                    ? 'bg-gray-700 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-all duration-200 ${
                  viewMode === 'list'
                    ? 'bg-gray-700 text-white'
                    : 'text-gray-400 hover:text-white'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {isLoading ? (
          <div className="flex items-center justify-center py-20">
            <div className="text-center">
              <div className="animate-spin w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full mx-auto mb-4"></div>
              <p className="text-gray-400">Searching...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center py-20">
            <div className="w-16 h-16 bg-red-600/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-red-400" />
            </div>
            <h3 className="text-white font-semibold text-lg mb-2">Search Error</h3>
            <p className="text-gray-400">{error}</p>
          </div>
        ) : results.length === 0 && currentQuery ? (
          <div className="text-center py-20">
            <div className="w-16 h-16 bg-gray-800/50 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <Search className="w-8 h-8 text-gray-400" />
            </div>
            <h3 className="text-white font-semibold text-lg mb-2">No Results Found</h3>
            <p className="text-gray-400 mb-4">
              No results found for "{currentQuery}". Try different keywords or check spelling.
            </p>
            <div className="text-sm text-gray-500">
              <p>Search tips:</p>
              <ul className="mt-2 space-y-1">
                <li>• Try broader keywords</li>
                <li>• Check for typos</li>
                <li>• Use different content type filters</li>
              </ul>
            </div>
          </div>
        ) : (
          <>
            {/* Results Grid/List */}
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
                {results.map((result) => (
                  <Link
                    key={`${result.type}-${result._id}`}
                    href={getContentLink(result)}
                    className="group"
                  >
                    <div className="bg-gray-900/40 backdrop-blur-sm rounded-2xl overflow-hidden border border-gray-800/50 hover:border-gray-700/50 transition-all duration-300 hover:scale-105">
                      {/* Poster */}
                      <div className="aspect-[2/3] bg-gray-800 relative overflow-hidden">
                        {(result.posterUrl || result.seriesPoster) ? (
                          <Image
                            src={result.posterUrl || result.seriesPoster || ''}
                            alt={result.title}
                            fill
                            className="object-cover group-hover:scale-110 transition-transform duration-300"
                            unoptimized
                          />
                        ) : (
                          <div className="w-full h-full flex items-center justify-center">
                            <Play className="w-12 h-12 text-gray-600" />
                          </div>
                        )}
                        
                        {/* Type Badge */}
                        <div className="absolute top-2 left-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded-lg ${
                            result.type === 'movie' ? 'bg-blue-600/80 text-white' :
                            result.type === 'series' ? 'bg-green-600/80 text-white' :
                            'bg-purple-600/80 text-white'
                          }`}>
                            {result.type === 'episode' ? 'EP' : result.type.toUpperCase()}
                          </span>
                        </div>

                        {/* Rating */}
                        {result.rating && (
                          <div className="absolute top-2 right-2 bg-black/60 backdrop-blur-sm rounded-lg px-2 py-1">
                            <div className="flex items-center space-x-1">
                              <Star className="w-3 h-3 text-yellow-500" />
                              <span className="text-white text-xs font-medium">
                                {formatRating(result.rating)}
                              </span>
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Info */}
                      <div className="p-3">
                        <h3 className="text-white font-medium text-sm line-clamp-2 mb-1">
                          {result.title}
                        </h3>
                        
                        {/* Episode Info */}
                        {result.type === 'episode' && (
                          <p className="text-gray-400 text-xs mb-1">
                            {result.seriesTitle} • S{result.season}E{result.episode}
                          </p>
                        )}

                        {/* Series Info */}
                        {result.type === 'series' && result.totalSeasons && (
                          <p className="text-gray-400 text-xs mb-1">
                            {result.totalSeasons} Season{result.totalSeasons > 1 ? 's' : ''}
                          </p>
                        )}

                        {/* Date */}
                        {(result.releaseDate || result.airDate) && (
                          <div className="flex items-center space-x-1 text-gray-500 text-xs">
                            <Calendar className="w-3 h-3" />
                            <span>{formatDate(result.releaseDate || result.airDate)}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            ) : (
              // List View
              <div className="space-y-4">
                {results.map((result) => (
                  <Link
                    key={`${result.type}-${result._id}`}
                    href={getContentLink(result)}
                    className="block"
                  >
                    <div className="bg-gray-900/40 backdrop-blur-sm rounded-2xl p-4 border border-gray-800/50 hover:border-gray-700/50 transition-all duration-300 hover:bg-gray-900/60">
                      <div className="flex items-center space-x-4">
                        {/* Poster */}
                        <div className="flex-shrink-0 w-16 h-20 bg-gray-800 rounded-lg overflow-hidden">
                          {(result.posterUrl || result.seriesPoster) ? (
                            <Image
                              src={result.posterUrl || result.seriesPoster || ''}
                              alt={result.title}
                              width={64}
                              height={80}
                              className="w-full h-full object-cover"
                              unoptimized
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center">
                              <Play className="w-6 h-6 text-gray-600" />
                            </div>
                          )}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="text-white font-medium truncate">
                              {result.title}
                            </h3>
                            <span className={`px-2 py-0.5 text-xs rounded-full ${
                              result.type === 'movie' ? 'bg-blue-600/20 text-blue-400' :
                              result.type === 'series' ? 'bg-green-600/20 text-green-400' :
                              'bg-purple-600/20 text-purple-400'
                            }`}>
                              {result.type === 'episode' ? 'EP' : result.type.toUpperCase()}
                            </span>
                          </div>

                          {/* Episode specific info */}
                          {result.type === 'episode' && (
                            <p className="text-gray-400 text-sm mb-1">
                              {result.seriesTitle} • Season {result.season}, Episode {result.episode}
                            </p>
                          )}

                          {/* Series specific info */}
                          {result.type === 'series' && result.totalSeasons && (
                            <p className="text-gray-400 text-sm mb-1">
                              {result.totalSeasons} Season{result.totalSeasons > 1 ? 's' : ''}
                            </p>
                          )}

                          {/* Rating and Date */}
                          <div className="flex items-center space-x-4 text-sm text-gray-500">
                            {result.rating && (
                              <div className="flex items-center space-x-1">
                                <Star className="w-4 h-4 text-yellow-500" />
                                <span>{formatRating(result.rating)}</span>
                              </div>
                            )}
                            {(result.releaseDate || result.airDate) && (
                              <div className="flex items-center space-x-1">
                                <Calendar className="w-4 h-4" />
                                <span>{formatDate(result.releaseDate || result.airDate)}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            )}

            {/* Pagination */}
            {pagination.pages > 1 && (
              <div className="flex items-center justify-center space-x-2 mt-12">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="px-4 py-2 bg-gray-800/50 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800/70 transition-colors"
                >
                  Previous
                </button>
                
                {Array.from({ length: Math.min(5, pagination.pages) }, (_, i) => {
                  const page = i + Math.max(1, pagination.page - 2);
                  if (page > pagination.pages) return null;
                  
                  return (
                    <button
                      key={page}
                      onClick={() => handlePageChange(page)}
                      className={`px-4 py-2 rounded-lg transition-colors ${
                        page === pagination.page
                          ? 'bg-red-600 text-white'
                          : 'bg-gray-800/50 text-white hover:bg-gray-800/70'
                      }`}
                    >
                      {page}
                    </button>
                  );
                })}
                
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                  className="px-4 py-2 bg-gray-800/50 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-800/70 transition-colors"
                >
                  Next
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

const SearchPage: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="animate-spin w-12 h-12 border-4 border-red-500 border-t-transparent rounded-full"></div>
      </div>
    }>
      <SearchContent />
    </Suspense>
  );
};

export default SearchPage;
