'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Play, Star, Calendar, Info, Plus } from 'lucide-react';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface ContentCardProps {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series' | 'episode';
  season?: number;
  episode?: number;
  seriesTitle?: string;
  seriesPosterUrl?: string; // For episodes to use series poster
  className?: string;
}

const ContentCard: React.FC<ContentCardProps> = ({
  id,
  imdbId,
  title,
  year,
  posterUrl,
  imdbRating,
  description,
  type,
  season,
  episode,
  seriesTitle,
  seriesPosterUrl,
  className
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const href = type === 'movie'
    ? `/watch/movie/${imdbId}`
    : type === 'series'
    ? `/watch/series/${imdbId}`
    : `/watch/series/${imdbId}?season=${season}&episode=${episode}`;

  const displayTitle = type === 'episode' ? seriesTitle : title;
  const subtitle = type === 'episode' ? `S${season}E${episode}: ${title}` : year ? `${year}` : '';

  // Use series poster for episodes, fallback to episode poster, then to placeholder
  const displayPosterUrl = type === 'episode' ? (seriesPosterUrl || posterUrl) : posterUrl;

  return (
    <Link href={href} className="block">
      <div
        className={cn(
          'group relative transition-all duration-500 ease-out cursor-pointer',
          'hover:transform hover:scale-105 hover:-translate-y-2',
          'hover:shadow-2xl hover:shadow-gray-900/50',
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Main Card Container */}
        <div className="relative aspect-[2/3] bg-gray-900 overflow-hidden rounded-2xl border border-gray-800/50 hover:border-gray-700/50 transition-all duration-500">
          {/* Poster Image */}
          <Image
            src={getImageUrl(displayPosterUrl)}
            alt={displayTitle || 'Content poster'}
            fill
            className={cn(
              'object-cover transition-all duration-500 ease-out',
              isHovered ? 'scale-110 brightness-75' : 'scale-100 brightness-100'
            )}
            sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
            priority={false}
          />

          {/* Enhanced Gradient Overlay */}
          <div className={cn(
            'absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent transition-all duration-500',
            isHovered ? 'opacity-100' : 'opacity-70'
          )} />

          {/* Premium Glow Effect */}
          <div className={cn(
            'absolute inset-0 rounded-2xl transition-all duration-500',
            isHovered ? 'shadow-[inset_0_0_40px_rgba(255,255,255,0.1)]' : 'shadow-none'
          )} />

          {/* Enhanced Rating Badge */}
          {imdbRating && (
            <div className="absolute top-4 right-4 z-10">
              <div className="glass-elevated px-3 py-2 rounded-xl flex items-center space-x-2 backdrop-blur-sm border border-gray-700/50">
                <Star size={14} className="text-yellow-500 fill-current" />
                <span className="text-white text-sm font-bold">{formatRating(imdbRating)}</span>
              </div>
            </div>
          )}

          {/* Enhanced Hover Play Button */}
          <div className={cn(
            'absolute inset-0 flex items-center justify-center transition-all duration-500 pointer-events-none',
            isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-75'
          )}>
            <div className="w-20 h-20 bg-gradient-to-br from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-2xl border-2 border-white/20 backdrop-blur-sm">
              <Play size={28} className="text-white fill-current ml-1" />
            </div>
          </div>

          {/* Enhanced Bottom Content Info */}
          <div className="absolute bottom-0 left-0 right-0 p-4 pointer-events-none">
            <h3 className="text-white font-bold text-base mb-2 line-clamp-2 leading-tight drop-shadow-lg">
              {displayTitle}
            </h3>

            {subtitle && (
              <p className="text-gray-300 text-sm font-medium drop-shadow-md">
                {subtitle}
              </p>
            )}
          </div>

          {/* Premium Border Glow */}
          <div className={cn(
            'absolute inset-0 rounded-2xl border-2 transition-all duration-500 pointer-events-none',
            isHovered ? 'border-gray-500/50 shadow-[0_0_30px_rgba(156,163,175,0.3)]' : 'border-transparent'
          )} />
        </div>
      </div>
    </Link>
  );
};

export default ContentCard;
