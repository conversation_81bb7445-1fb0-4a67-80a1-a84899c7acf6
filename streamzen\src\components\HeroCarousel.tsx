'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronLeft, ChevronRight, Play, Info, Star, Volume2, VolumeX } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';

interface HeroItem {
  id: string;
  imdbId: string;
  title: string;
  year?: number;
  posterUrl?: string;
  imdbRating?: number;
  description?: string;
  type: 'movie' | 'series';
}

interface HeroCarouselProps {
  items: HeroItem[];
}

const HeroCarousel: React.FC<HeroCarouselProps> = ({ items }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isMuted, setIsMuted] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying || items.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % items.length);
    }, 8000); // Longer duration for premium feel

    return () => clearInterval(interval);
  }, [isAutoPlaying, items.length]);

  // Preload next image
  useEffect(() => {
    if (items.length > 1) {
      const nextIndex = (currentIndex + 1) % items.length;
      const img = new window.Image();
      img.src = getImageUrl(items[nextIndex].posterUrl);
    }
  }, [currentIndex, items]);

  const goToPrevious = useCallback(() => {
    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  }, [items.length]);

  const goToNext = useCallback(() => {
    setCurrentIndex((prev) => (prev + 1) % items.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  }, [items.length]);

  const goToSlide = useCallback((index: number) => {
    setCurrentIndex(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.key === 'ArrowLeft') goToPrevious();
      if (e.key === 'ArrowRight') goToNext();
      if (e.key === ' ') {
        e.preventDefault();
        setIsAutoPlaying(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [goToPrevious, goToNext]);

  if (!items.length) {
    return (
      <div className="relative h-[85vh] bg-black flex items-center justify-center">
        <div className="text-center animate-fade-in">
          <div className="w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Play size={24} className="text-white/60" />
          </div>
          <p className="text-gray-400 text-lg">No featured content available</p>
        </div>
      </div>
    );
  }

  const currentItem = items[currentIndex];

  return (
    <div
      className="relative h-[85vh] overflow-hidden bg-black"
      onMouseEnter={() => setIsAutoPlaying(false)}
      onMouseLeave={() => setIsAutoPlaying(true)}
    >
      <AnimatePresence mode="wait">
        <motion.div
          key={currentIndex}
          initial={{ opacity: 0, scale: 1.05 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          transition={{
            duration: 1.2,
            ease: [0.16, 1, 0.3, 1]
          }}
          className="absolute inset-0"
          onAnimationComplete={() => setIsLoaded(true)}
        >
          {/* Background Image with Parallax Effect */}
          <div className="absolute inset-0 overflow-hidden">
            <motion.div
              initial={{ scale: 1.1 }}
              animate={{ scale: 1 }}
              transition={{ duration: 8, ease: "linear" }}
              className="w-full h-full"
            >
              <Image
                src={getImageUrl(currentItem.posterUrl)}
                alt={currentItem.title}
                fill
                className="object-cover object-center"
                priority
                sizes="100vw"
                quality={95}
              />
            </motion.div>

            {/* Apple TV + Netflix Style Overlays */}
            <div className="absolute inset-0 bg-gradient-to-r from-black/95 via-black/60 to-black/30" />
            <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-transparent to-black/20" />
            <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/80" />
          </div>

          {/* Premium Content Layout */}
          <div className="relative h-full flex items-center">
            <div className="max-w-[1920px] mx-auto px-6 lg:px-12 w-full">
              <div className="max-w-3xl">
                <motion.div
                  initial={{ y: 60, opacity: 0 }}
                  animate={{ y: 0, opacity: 1 }}
                  transition={{
                    delay: 0.4,
                    duration: 0.8,
                    ease: [0.16, 1, 0.3, 1]
                  }}
                  className="space-y-8"
                >
                  {/* StreamZen Type Badge */}
                  <div className="inline-flex items-center px-4 py-2 bg-black/60 backdrop-blur-sm rounded-full border border-red-500/30">
                    <div className="w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse" />
                    <span className="text-white text-sm font-semibold uppercase tracking-wider">
                      {currentItem.type === 'movie' ? 'Feature Film' : 'Original Series'}
                    </span>
                  </div>

                  {/* Hero Title */}
                  <div className="space-y-4">
                    <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-white leading-none tracking-tight">
                      {currentItem.title}
                    </h1>

                    {/* Meta Information */}
                    <div className="flex items-center space-x-6 text-lg">
                      {currentItem.year && (
                        <span className="text-gray-300 font-medium">{currentItem.year}</span>
                      )}
                      {currentItem.imdbRating && (
                        <div className="flex items-center space-x-2 glass px-3 py-1 rounded-full">
                          <Star size={16} className="text-yellow-400 fill-current" />
                          <span className="text-white font-bold">{formatRating(currentItem.imdbRating)}</span>
                        </div>
                      )}
                      <div className="glass px-3 py-1 rounded-full">
                        <span className="text-white font-medium">HD</span>
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  {currentItem.description && (
                    <p className="text-gray-200 text-xl leading-relaxed max-w-2xl font-light">
                      {truncateText(currentItem.description, 180)}
                    </p>
                  )}

                  {/* StreamZen Action Buttons */}
                  <div className="flex items-center space-x-4 pt-4">
                    <Link
                      href={`/watch/${currentItem.type}/${currentItem.imdbId}`}
                      className="focus-ring rounded-2xl"
                    >
                      <div className="flex items-center space-x-3 bg-red-600 hover:bg-red-500 text-white px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-2xl">
                        <Play size={24} fill="currentColor" />
                        <span>Play</span>
                      </div>
                    </Link>

                    <Link
                      href={`/${currentItem.type}s/${currentItem.imdbId}`}
                      className="focus-ring rounded-2xl"
                    >
                      <div className="flex items-center space-x-3 bg-black/60 backdrop-blur-sm border border-gray-600 px-8 py-4 rounded-2xl font-bold text-lg text-white hover:bg-black/80 transition-all duration-300 hover:scale-105">
                        <Info size={24} />
                        <span>More Info</span>
                      </div>
                    </Link>

                    <button
                      onClick={() => setIsMuted(!isMuted)}
                      className="glass-elevated p-4 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110 focus-ring"
                    >
                      {isMuted ? <VolumeX size={24} className="text-white" /> : <Volume2 size={24} className="text-white" />}
                    </button>
                  </div>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      {/* Premium Navigation Arrows */}
      {items.length > 1 && (
        <>
          <button
            onClick={goToPrevious}
            className="absolute left-8 top-1/2 -translate-y-1/2 w-14 h-14 glass-elevated rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 hover:scale-110 z-30 focus-ring opacity-0 hover:opacity-100 group-hover:opacity-100"
          >
            <ChevronLeft size={28} />
          </button>
          <button
            onClick={goToNext}
            className="absolute right-8 top-1/2 -translate-y-1/2 w-14 h-14 glass-elevated rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 hover:scale-110 z-30 focus-ring opacity-0 hover:opacity-100 group-hover:opacity-100"
          >
            <ChevronRight size={28} />
          </button>
        </>
      )}

      {/* Premium Progress Indicators */}
      {items.length > 1 && (
        <div className="absolute bottom-8 left-1/2 -translate-x-1/2 z-30">
          <div className="flex items-center space-x-3">
            {items.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className="group focus-ring rounded-full p-1"
              >
                <div className="relative">
                  {/* Background bar */}
                  <div className="w-12 h-1 bg-white/30 rounded-full overflow-hidden">
                    {/* Progress bar */}
                    <div
                      className={cn(
                        'h-full bg-white rounded-full transition-all duration-300',
                        index === currentIndex ? 'w-full' : 'w-0'
                      )}
                    />
                  </div>
                  {/* Hover indicator */}
                  <div className={cn(
                    'absolute inset-0 bg-white/50 rounded-full transition-opacity duration-200',
                    index === currentIndex ? 'opacity-0' : 'opacity-0 group-hover:opacity-100'
                  )} />
                </div>
              </button>
            ))}
          </div>

          {/* Auto-play indicator */}
          <div className="flex items-center justify-center mt-4">
            <div className={cn(
              'w-2 h-2 rounded-full transition-all duration-300',
              isAutoPlaying ? 'bg-green-400 animate-pulse' : 'bg-gray-500'
            )} />
          </div>
        </div>
      )}

      {/* Subtle Ambient Effect */}
      <div className="absolute inset-0 pointer-events-none">
        <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-white/5 blur-3xl" />
        <div className="absolute top-0 right-0 w-1/3 h-1/3 bg-white/5 blur-3xl" />
      </div>
    </div>
  );
};

export default HeroCarousel;
