module.exports = {

"[project]/src/models/SyncStatus.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/mongoose [external] (mongoose, cjs)");
;
const SyncStatusSchema = new __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["Schema"]({
    lastSyncTime: {
        type: Date,
        default: null
    },
    nextSyncTime: {
        type: Date,
        required: true,
        index: true
    },
    isRunning: {
        type: Boolean,
        default: false,
        index: true
    },
    lastSyncResults: {
        movies: {
            type: Number,
            default: 0
        },
        series: {
            type: Number,
            default: 0
        },
        episodes: {
            type: Number,
            default: 0
        }
    }
}, {
    timestamps: true
});
// Index for querying sync status
SyncStatusSchema.index({
    createdAt: -1
});
SyncStatusSchema.index({
    nextSyncTime: 1,
    isRunning: 1
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].models.SyncStatus || __TURBOPACK__imported__module__$5b$externals$5d2f$mongoose__$5b$external$5d$__$28$mongoose$2c$__cjs$29$__["default"].model('SyncStatus', SyncStatusSchema);
}}),

};

//# sourceMappingURL=src_models_SyncStatus_ts_4c3ccdce._.js.map