{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/mongodb.ts"], "sourcesContent": ["import mongoose from 'mongoose';\n\nconst MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/streamzen';\n\nif (!MONGODB_URI) {\n  throw new Error('Please define the MONGODB_URI environment variable inside .env.local');\n}\n\n/**\n * Global is used here to maintain a cached connection across hot reloads\n * in development. This prevents connections growing exponentially\n * during API Route usage.\n */\nlet cached = global.mongoose;\n\nif (!cached) {\n  cached = global.mongoose = { conn: null, promise: null };\n}\n\nasync function connectDB() {\n  if (cached.conn) {\n    return cached.conn;\n  }\n\n  if (!cached.promise) {\n    const opts = {\n      bufferCommands: false,\n    };\n\n    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {\n      return mongoose;\n    });\n  }\n\n  try {\n    cached.conn = await cached.promise;\n  } catch (e) {\n    cached.promise = null;\n    throw e;\n  }\n\n  return cached.conn;\n}\n\nexport default connectDB;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,QAAQ,GAAG,CAAC,WAAW,IAAI;AAE/C,uCAAkB;;AAElB;AAEA;;;;CAIC,GACD,IAAI,SAAS,OAAO,QAAQ;AAE5B,IAAI,CAAC,QAAQ;IACX,SAAS,OAAO,QAAQ,GAAG;QAAE,MAAM;QAAM,SAAS;IAAK;AACzD;AAEA,eAAe;IACb,IAAI,OAAO,IAAI,EAAE;QACf,OAAO,OAAO,IAAI;IACpB;IAEA,IAAI,CAAC,OAAO,OAAO,EAAE;QACnB,MAAM,OAAO;YACX,gBAAgB;QAClB;QAEA,OAAO,OAAO,GAAG,yGAAA,CAAA,UAAQ,CAAC,OAAO,CAAC,aAAa,MAAM,IAAI,CAAC,CAAC;YACzD,OAAO;QACT;IACF;IAEA,IAAI;QACF,OAAO,IAAI,GAAG,MAAM,OAAO,OAAO;IACpC,EAAE,OAAO,GAAG;QACV,OAAO,OAAO,GAAG;QACjB,MAAM;IACR;IAEA,OAAO,OAAO,IAAI;AACpB;uCAEe", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Movie.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IMovie extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  year: number;\n  rating?: string; // MPAA rating (R, PG-13, etc.)\n  runtime?: string; // e.g., \"2h 22m\"\n  imdbRating?: number;\n  imdbVotes?: string; // e.g., \"3.1M\"\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst MovieSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: { \n    type: String, \n    required: true,\n    index: true \n  },\n  year: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  rating: String,\n  runtime: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  trailerRuntime: String,\n  trailerLikes: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  director: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  }\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nMovieSchema.index({ year: -1, imdbRating: -1 });\nMovieSchema.index({ genres: 1, year: -1 });\n// Removed text index to avoid language override issues\nMovieSchema.index({ title: 1 });\nMovieSchema.index({ language: 1, country: 1 });\n\nexport default mongoose.models.Movie || mongoose.model<IMovie>('Movie', MovieSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAgCA,MAAM,cAAsB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACrC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,MAAM;QACJ,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;IACR,SAAS;IACT,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,gBAAgB;IAChB,cAAc;IACd,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,UAAU;IACV,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;AACF,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,YAAY,KAAK,CAAC;IAAE,MAAM,CAAC;IAAG,YAAY,CAAC;AAAE;AAC7C,YAAY,KAAK,CAAC;IAAE,QAAQ;IAAG,MAAM,CAAC;AAAE;AACxC,uDAAuD;AACvD,YAAY,KAAK,CAAC;IAAE,OAAO;AAAE;AAC7B,YAAY,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;uCAE7B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,KAAK,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAS,SAAS", "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Series.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface ISeries extends Document {\n  imdbId: string;\n  tmdbId?: string;\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string; // MPAA rating\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string; // 'ongoing', 'ended', 'cancelled'\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst SeriesSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true, \n    unique: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  title: { \n    type: String, \n    required: true,\n    index: true \n  },\n  startYear: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  endYear: { \n    type: Number,\n    index: true \n  },\n  rating: String,\n  imdbRating: { \n    type: Number,\n    index: true \n  },\n  imdbVotes: String,\n  popularity: { \n    type: Number,\n    index: true \n  },\n  popularityDelta: Number,\n  posterUrl: String,\n  trailerUrl: String,\n  description: String,\n  genres: [{ \n    type: String,\n    index: true \n  }],\n  creator: String,\n  cast: [String],\n  language: { \n    type: String,\n    index: true \n  },\n  country: {\n    type: String,\n    index: true\n  },\n  totalSeasons: Number,\n  status: { \n    type: String,\n    enum: ['ongoing', 'ended', 'cancelled'],\n    index: true \n  },\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String // VidSrc TMDB embed URL\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nSeriesSchema.index({ startYear: -1, imdbRating: -1 });\nSeriesSchema.index({ genres: 1, startYear: -1 });\nSeriesSchema.index({ status: 1, startYear: -1 });\n// Removed text index to avoid language override issues\nSeriesSchema.index({ title: 1 });\nSeriesSchema.index({ language: 1, country: 1 });\n\nexport default mongoose.models.Series || mongoose.model<ISeries>('Series', SeriesSchema);\n"], "names": [], "mappings": ";;;AAAA;;AA+BA,MAAM,eAAuB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACtC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,QAAQ;QACR,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;IACR,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,WAAW;IACX,YAAY;QACV,MAAM;QACN,OAAO;IACT;IACA,iBAAiB;IACjB,WAAW;IACX,YAAY;IACZ,aAAa;IACb,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;IACF,SAAS;IACT,MAAM;QAAC;KAAO;IACd,UAAU;QACR,MAAM;QACN,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,cAAc;IACd,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAS;SAAY;QACvC,OAAO;IACT;IACA,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe,OAAO,wBAAwB;AAChD,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,aAAa,KAAK,CAAC;IAAE,WAAW,CAAC;IAAG,YAAY,CAAC;AAAE;AACnD,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,aAAa,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW,CAAC;AAAE;AAC9C,uDAAuD;AACvD,aAAa,KAAK,CAAC;IAAE,OAAO;AAAE;AAC9B,aAAa,KAAK,CAAC;IAAE,UAAU;IAAG,SAAS;AAAE;uCAE9B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAU,UAAU", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Episode.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IEpisode extends Document {\n  imdbId: string; // Series IMDb ID\n  tmdbId?: string; // Series TMDB ID\n  seriesTitle: string;\n  season: number;\n  episode: number;\n  episodeTitle?: string;\n  airDate?: Date;\n  runtime?: string;\n  imdbRating?: number;\n  description?: string;\n  embedUrl: string;\n  embedUrlTmdb?: string;\n  vidsrcUrl?: string; // VidSrc embed URL\n  vidsrcTmdbUrl?: string; // VidSrc TMDB embed URL\n  quality?: string;\n  genres?: string[]; // Genres inherited from series\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nconst EpisodeSchema: Schema = new Schema({\n  imdbId: { \n    type: String, \n    required: true,\n    index: true \n  },\n  tmdbId: { \n    type: String, \n    index: true \n  },\n  seriesTitle: { \n    type: String, \n    required: true,\n    index: true \n  },\n  season: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episode: { \n    type: Number, \n    required: true,\n    index: true \n  },\n  episodeTitle: String,\n  airDate: { \n    type: Date,\n    index: true \n  },\n  runtime: String,\n  imdbRating: Number,\n  description: String,\n  embedUrl: {\n    type: String,\n    required: true\n  },\n  embedUrlTmdb: String,\n  vidsrcUrl: String, // VidSrc embed URL\n  vidsrcTmdbUrl: String, // VidSrc TMDB embed URL\n  quality: {\n    type: String,\n    index: true\n  },\n  genres: [{\n    type: String,\n    index: true\n  }]\n}, {\n  timestamps: true\n});\n\n// Compound indexes for better query performance\nEpisodeSchema.index({ imdbId: 1, season: 1, episode: 1 }, { unique: true });\nEpisodeSchema.index({ airDate: -1 });\nEpisodeSchema.index({ seriesTitle: 1, season: 1, episode: 1 });\nEpisodeSchema.index({ createdAt: -1 }); // For latest episodes\n\nexport default mongoose.models.Episode || mongoose.model<IEpisode>('Episode', EpisodeSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAuBA,MAAM,gBAAwB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACvC,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,QAAQ;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,SAAS;QACP,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,cAAc;IACd,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,SAAS;IACT,YAAY;IACZ,aAAa;IACb,UAAU;QACR,MAAM;QACN,UAAU;IACZ;IACA,cAAc;IACd,WAAW;IACX,eAAe;IACf,SAAS;QACP,MAAM;QACN,OAAO;IACT;IACA,QAAQ;QAAC;YACP,MAAM;YACN,OAAO;QACT;KAAE;AACJ,GAAG;IACD,YAAY;AACd;AAEA,gDAAgD;AAChD,cAAc,KAAK,CAAC;IAAE,QAAQ;IAAG,QAAQ;IAAG,SAAS;AAAE,GAAG;IAAE,QAAQ;AAAK;AACzE,cAAc,KAAK,CAAC;IAAE,SAAS,CAAC;AAAE;AAClC,cAAc,KAAK,CAAC;IAAE,aAAa;IAAG,QAAQ;IAAG,SAAS;AAAE;AAC5D,cAAc,KAAK,CAAC;IAAE,WAAW,CAAC;AAAE,IAAI,sBAAsB;uCAE/C,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAW,WAAW", "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/models/Request.ts"], "sourcesContent": ["import mongoose, { Schema, Document } from 'mongoose';\n\nexport interface IRequest extends Document {\n  imdbIds: string[];\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  processedCount: number;\n  totalCount: number;\n  failedIds: string[];\n  errorMessages: string[];\n  submittedBy?: string; // IP address or user identifier\n  contentType: 'auto' | 'movie' | 'series'; // Content type preference\n  createdAt: Date;\n  updatedAt: Date;\n  completedAt?: Date;\n}\n\nconst RequestSchema: Schema = new Schema({\n  imdbIds: [{ \n    type: String, \n    required: true \n  }],\n  status: { \n    type: String, \n    enum: ['pending', 'processing', 'completed', 'failed'],\n    default: 'pending',\n    index: true \n  },\n  processedCount: { \n    type: Number, \n    default: 0 \n  },\n  totalCount: { \n    type: Number, \n    required: true \n  },\n  failedIds: [String],\n  errorMessages: [String],\n  submittedBy: {\n    type: String,\n    index: true\n  },\n  contentType: {\n    type: String,\n    enum: ['auto', 'movie', 'series'],\n    default: 'auto'\n  }\n}, {\n  timestamps: true\n});\n\n// Index for querying pending requests\nRequestSchema.index({ status: 1, createdAt: 1 });\n\nexport default mongoose.models.Request || mongoose.model<IRequest>('Request', RequestSchema);\n"], "names": [], "mappings": ";;;AAAA;;AAgBA,MAAM,gBAAwB,IAAI,yGAAA,CAAA,SAAM,CAAC;IACvC,SAAS;QAAC;YACR,MAAM;YACN,UAAU;QACZ;KAAE;IACF,QAAQ;QACN,MAAM;QACN,MAAM;YAAC;YAAW;YAAc;YAAa;SAAS;QACtD,SAAS;QACT,OAAO;IACT;IACA,gBAAgB;QACd,MAAM;QACN,SAAS;IACX;IACA,YAAY;QACV,MAAM;QACN,UAAU;IACZ;IACA,WAAW;QAAC;KAAO;IACnB,eAAe;QAAC;KAAO;IACvB,aAAa;QACX,MAAM;QACN,OAAO;IACT;IACA,aAAa;QACX,MAAM;QACN,MAAM;YAAC;YAAQ;YAAS;SAAS;QACjC,SAAS;IACX;AACF,GAAG;IACD,YAAY;AACd;AAEA,sCAAsC;AACtC,cAAc,KAAK,CAAC;IAAE,QAAQ;IAAG,WAAW;AAAE;uCAE/B,yGAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,yGAAA,CAAA,UAAQ,CAAC,KAAK,CAAW,WAAW", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/scraper.ts"], "sourcesContent": ["import axios from 'axios';\nimport * as cheerio from 'cheerio';\n\nexport interface ScrapedMovieData {\n  title: string;\n  year: number;\n  rating?: string;\n  runtime?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  backdropUrl?: string;\n  trailerUrl?: string;\n  trailerRuntime?: string;\n  trailerLikes?: string;\n  description?: string;\n  genres?: string[];\n  director?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n}\n\nexport interface ScrapedSeriesData {\n  title: string;\n  startYear: number;\n  endYear?: number;\n  rating?: string;\n  imdbRating?: number;\n  imdbVotes?: string;\n  popularity?: number;\n  popularityDelta?: number;\n  posterUrl?: string;\n  backdropUrl?: string;\n  trailerUrl?: string;\n  description?: string;\n  genres?: string[];\n  creator?: string;\n  cast?: string[];\n  language?: string;\n  country?: string;\n  totalSeasons?: number;\n  status?: string;\n}\n\nclass IMDbScraper {\n  private static instance: IMDbScraper;\n  private requestCount = 0;\n  private lastRequestTime = 0;\n  private readonly RATE_LIMIT = 30; // requests per minute\n  private readonly MIN_DELAY = 2000; // 2 seconds between requests\n\n  static getInstance(): IMDbScraper {\n    if (!IMDbScraper.instance) {\n      IMDbScraper.instance = new IMDbScraper();\n    }\n    return IMDbScraper.instance;\n  }\n\n  private async rateLimit(): Promise<void> {\n    const now = Date.now();\n    const timeSinceLastRequest = now - this.lastRequestTime;\n\n    if (timeSinceLastRequest < this.MIN_DELAY) {\n      const delay = this.MIN_DELAY - timeSinceLastRequest;\n      // Add random jitter to avoid detection patterns\n      const jitter = Math.random() * 1000; // 0-1000ms random delay\n      await new Promise(resolve => setTimeout(resolve, delay + jitter));\n    }\n\n    this.lastRequestTime = Date.now();\n    this.requestCount++;\n  }\n\n  private getRandomUserAgent(): string {\n    const userAgents = [\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',\n      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:120.0) Gecko/20100101 Firefox/120.0',\n      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15'\n    ];\n    return userAgents[Math.floor(Math.random() * userAgents.length)];\n  }\n\n  private async fetchPage(imdbId: string): Promise<cheerio.CheerioAPI> {\n    await this.rateLimit();\n\n    const url = `https://www.imdb.com/title/${imdbId}/`;\n    const headers = {\n      'User-Agent': this.getRandomUserAgent(),\n      'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',\n      'Accept-Language': 'en-US,en;q=0.5',\n      'Accept-Encoding': 'gzip, deflate, br',\n      'Connection': 'keep-alive',\n      'Upgrade-Insecure-Requests': '1',\n      'Sec-Fetch-Dest': 'document',\n      'Sec-Fetch-Mode': 'navigate',\n      'Sec-Fetch-Site': 'none',\n      'Cache-Control': 'max-age=0',\n    };\n\n    try {\n      const response = await axios.get(url, { headers, timeout: 30000 });\n      return cheerio.load(response.data);\n    } catch (error) {\n      console.error(`Error fetching IMDb page for ${imdbId}:`, error);\n      throw new Error(`Failed to fetch IMDb page: ${error.message}`);\n    }\n  }\n\n  private extractBasicInfo($: cheerio.CheerioAPI): { title: string; year: number; type: 'movie' | 'series' } {\n    const titleElement = $('h1[data-testid=\"hero__pageTitle\"] span[data-testid=\"hero__primary-text\"]');\n    const title = titleElement.text().trim();\n    \n    if (!title) {\n      throw new Error('Could not extract title from IMDb page');\n    }\n\n    // Extract year from the release info\n    const yearElement = $('ul.ipc-inline-list a[href*=\"/releaseinfo/\"]');\n    const yearText = yearElement.text().trim();\n    const year = parseInt(yearText) || new Date().getFullYear();\n\n    // Determine if it's a movie or series\n    const typeIndicators = $('ul.ipc-inline-list li').text().toLowerCase();\n    const isMovie = !typeIndicators.includes('tv series') && !typeIndicators.includes('tv mini series');\n    \n    return {\n      title,\n      year,\n      type: isMovie ? 'movie' : 'series'\n    };\n  }\n\n  private extractRating($: cheerio.CheerioAPI): string | undefined {\n    const ratingElement = $('ul.ipc-inline-list a[href*=\"/parentalguide/\"]');\n    return ratingElement.text().trim() || undefined;\n  }\n\n  private extractRuntime($: cheerio.CheerioAPI): string | undefined {\n    const runtimeElements = $('ul.ipc-inline-list li');\n    for (let i = 0; i < runtimeElements.length; i++) {\n      const text = $(runtimeElements[i]).text().trim();\n      if (text.includes('h') || text.includes('min')) {\n        return text;\n      }\n    }\n    return undefined;\n  }\n\n  private extractIMDbRating($: cheerio.CheerioAPI): { rating?: number; votes?: string } {\n    const ratingElement = $('div[data-testid=\"hero-rating-bar__aggregate-rating__score\"] span');\n    const rating = parseFloat(ratingElement.text().trim()) || undefined;\n    \n    const votesElement = $('div.sc-d541859f-3');\n    const votes = votesElement.text().trim() || undefined;\n    \n    return { rating, votes };\n  }\n\n  private extractPopularity($: cheerio.CheerioAPI): { popularity?: number; delta?: number } {\n    const popularityElement = $('div[data-testid=\"hero-rating-bar__popularity__score\"]');\n    const popularity = parseInt(popularityElement.text().trim()) || undefined;\n    \n    const deltaElement = $('div[data-testid=\"hero-rating-bar__popularity__delta\"]');\n    const deltaText = deltaElement.text().trim();\n    const delta = deltaText ? parseInt(deltaText.replace(/[^\\d-]/g, '')) : undefined;\n    \n    return { popularity, delta };\n  }\n\n  private extractPosterUrl($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for poster image\n    const selectors = [\n      'div[data-testid=\"hero-media__poster\"] img',\n      '.ipc-image[data-testid=\"hero-media__poster\"]',\n      '.poster img',\n      '.ipc-media img',\n      'img[class*=\"poster\"]',\n      'a[class*=\"ipc-lockup-overlay\"] img'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      const src = element.attr('src');\n      if (src && src.includes('media-amazon.com')) {\n        // Clean up the URL to get high quality image\n        return src.replace(/\\._.*?_\\./, '._V1_FMjpg_UX1000_.').replace(/\\._.*?\\./, '._V1_FMjpg_UX1000_.');\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractBackdropUrl($: cheerio.CheerioAPI): string | undefined {\n    // Try to extract backdrop/hero image\n    const selectors = [\n      '.hero-media__slate-overlay img',\n      '.slate img',\n      '.hero__background img',\n      'div[data-testid=\"hero-media\"] img'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      const src = element.attr('src');\n      if (src && src.includes('media-amazon.com')) {\n        return src.replace(/\\._.*?_\\./, '._V1_FMjpg_UX1920_.');\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractTrailerInfo($: cheerio.CheerioAPI): { url?: string; runtime?: string; likes?: string } {\n    const trailerElement = $('a[data-testid=\"video-player-slate-overlay\"]');\n    const url = trailerElement.attr('href') || undefined;\n    \n    const runtimeElement = $('span[data-testid=\"video-player-slate-runtime\"]');\n    const runtime = runtimeElement.text().trim() || undefined;\n    \n    const likesElement = $('span.ipc-reaction-summary__label');\n    const likes = likesElement.text().trim() || undefined;\n    \n    return { url, runtime, likes };\n  }\n\n  private extractDescription($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for description\n    const selectors = [\n      'div[data-testid=\"hero-media__slate\"] img',\n      'span[data-testid=\"plot-xl\"]',\n      'span[data-testid=\"plot-l\"]',\n      'span[data-testid=\"plot\"]'\n    ];\n\n    for (const selector of selectors) {\n      const element = $(selector);\n      if (selector.includes('img')) {\n        const alt = element.attr('alt');\n        if (alt && alt.length > 20) return alt;\n      } else {\n        const text = element.text().trim();\n        if (text && text.length > 10) return text;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractGenres($: cheerio.CheerioAPI): string[] {\n    const genres: string[] = [];\n\n    // Updated genre selectors based on current IMDb structure\n    const genreSelectors = [\n      // Current IMDb hero section genres (most common)\n      '[data-testid=\"genres\"] .ipc-chip .ipc-chip__text',\n      '[data-testid=\"genres\"] .ipc-chip__text',\n      '[data-testid=\"genres\"] a .ipc-chip__text',\n\n      // Hero section alternative formats\n      '.ipc-chip-list--baseAlt .ipc-chip .ipc-chip__text',\n      '.GenresAndPlot__GenreChip .ipc-chip__text',\n\n      // Storyline section (your provided structure - keep as fallback)\n      'li[data-testid=\"storyline-genres\"] .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"storyline-genres\"] a[href*=\"genres=\"]',\n\n      // General genre link selectors (broader search)\n      'a[href*=\"/search/title/?genres=\"] span',\n      'a[href*=\"genres=\"] span',\n      'a[href*=\"genres=\"]',\n\n      // Schema.org microdata\n      '[itemprop=\"genre\"]',\n      'span[itemprop=\"genre\"]',\n\n      // Fallback selectors\n      '.see-more.inline.canwrap a[href*=\"genres=\"]',\n      '.titlePageSprite.star-box-giga-star + div a[href*=\"genres=\"]',\n\n      // Very broad fallback - any link with genre in URL\n      'a[href*=\"explore=genres\"]'\n    ];\n\n    console.log(`🔍 Starting genre extraction...`);\n\n    for (let i = 0; i < genreSelectors.length; i++) {\n      const selector = genreSelectors[i];\n      const elements = $(selector);\n      console.log(`🔍 Selector ${i + 1}/${genreSelectors.length}: \"${selector}\" found ${elements.length} elements`);\n\n      if (elements.length > 0) {\n        elements.each((_, element) => {\n          const genre = $(element).text().trim();\n          console.log(`📝 Found genre text: \"${genre}\"`);\n\n          // Clean up genre text and validate\n          if (genre && genre.length > 0 && genre.length < 50 && !genres.includes(genre)) {\n            // Skip common non-genre text\n            const skipTexts = ['Genres', 'Genre', 'See all', 'More', 'All', '...'];\n            if (!skipTexts.includes(genre)) {\n              genres.push(genre);\n              console.log(`✅ Added genre: \"${genre}\"`);\n            }\n          }\n        });\n\n        if (genres.length > 0) {\n          console.log(`✅ Successfully extracted ${genres.length} genres: [${genres.join(', ')}]`);\n          break; // Use the first selector that finds results\n        }\n      }\n    }\n\n    if (genres.length === 0) {\n      console.log('⚠️ No genres found with any selector');\n      // Debug: Let's see what's actually in the storyline section\n      const storylineSection = $('li[data-testid=\"storyline-genres\"]');\n      if (storylineSection.length > 0) {\n        console.log('📋 Storyline section HTML:', storylineSection.html());\n      } else {\n        console.log('❌ No storyline-genres section found');\n      }\n    }\n\n    return genres;\n  }\n\n  private extractCast($: cheerio.CheerioAPI): string[] {\n    const cast: string[] = [];\n\n    // Try multiple selectors for cast\n    const castSelectors = [\n      'section[data-testid=\"title-cast\"] a[data-testid=\"title-cast-item__actor\"]',\n      '.cast_list .primary_photo + td a',\n      '.titleCast .primary_photo + td a',\n      'div[data-testid=\"title-cast-item\"] a[href*=\"/name/\"]'\n    ];\n\n    for (const selector of castSelectors) {\n      const elements = $(selector);\n      if (elements.length > 0) {\n        elements.each((_, element) => {\n          const actorName = $(element).text().trim();\n          if (actorName && !cast.includes(actorName) && cast.length < 10) { // Limit to top 10 cast members\n            cast.push(actorName);\n          }\n        });\n        break; // Use the first selector that finds results\n      }\n    }\n\n    return cast;\n  }\n\n  private extractDirector($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for director\n    const directorSelectors = [\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Director\") .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Directors\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"/name/\"][href*=\"ref_=tt_ov_dr\"]',\n      '.credit_summary_item:contains(\"Director\") a'\n    ];\n\n    for (const selector of directorSelectors) {\n      const element = $(selector).first();\n      const director = element.text().trim();\n      if (director) {\n        return director;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractCreator($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for creator (for TV series)\n    const creatorSelectors = [\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Creator\") .ipc-metadata-list-item__list-content-item',\n      'li[data-testid=\"title-pc-principal-credit\"]:contains(\"Creators\") .ipc-metadata-list-item__list-content-item',\n      '.credit_summary_item:contains(\"Creator\") a',\n      '.credit_summary_item:contains(\"Created by\") a'\n    ];\n\n    for (const selector of creatorSelectors) {\n      const element = $(selector).first();\n      const creator = element.text().trim();\n      if (creator) {\n        return creator;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractLanguage($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for language\n    const languageSelectors = [\n      'li[data-testid=\"title-details-languages\"] .ipc-metadata-list-item__list-content-item',\n      'div[data-testid=\"title-details-section\"] li:contains(\"Language\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"primary_language=\"]',\n      '.txt-block:contains(\"Language\") a'\n    ];\n\n    for (const selector of languageSelectors) {\n      const element = $(selector).first();\n      const language = element.text().trim();\n      if (language) {\n        return language;\n      }\n    }\n\n    return undefined;\n  }\n\n  private extractCountry($: cheerio.CheerioAPI): string | undefined {\n    // Try multiple selectors for country\n    const countrySelectors = [\n      'li[data-testid=\"title-details-origin\"] .ipc-metadata-list-item__list-content-item',\n      'div[data-testid=\"title-details-section\"] li:contains(\"Country\") .ipc-metadata-list-item__list-content-item',\n      'a[href*=\"country_of_origin=\"]',\n      '.txt-block:contains(\"Country\") a'\n    ];\n\n    for (const selector of countrySelectors) {\n      const element = $(selector).first();\n      const country = element.text().trim();\n      if (country) {\n        return country;\n      }\n    }\n\n    return undefined;\n  }\n\n  async scrapeMovie(imdbId: string): Promise<ScrapedMovieData> {\n    const $ = await this.fetchPage(imdbId);\n    const basicInfo = this.extractBasicInfo($);\n\n    if (basicInfo.type !== 'movie') {\n      throw new Error('IMDb ID does not correspond to a movie');\n    }\n\n    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);\n    const { popularity, delta: popularityDelta } = this.extractPopularity($);\n    const { url: trailerUrl, runtime: trailerRuntime, likes: trailerLikes } = this.extractTrailerInfo($);\n\n    return {\n      title: basicInfo.title,\n      year: basicInfo.year,\n      rating: this.extractRating($),\n      runtime: this.extractRuntime($),\n      imdbRating,\n      imdbVotes,\n      popularity,\n      popularityDelta,\n      posterUrl: this.extractPosterUrl($),\n      backdropUrl: this.extractBackdropUrl($),\n      trailerUrl,\n      trailerRuntime,\n      trailerLikes,\n      description: this.extractDescription($),\n      genres: this.extractGenres($),\n      director: this.extractDirector($),\n      cast: this.extractCast($),\n      language: this.extractLanguage($),\n      country: this.extractCountry($),\n    };\n  }\n\n  async scrapeSeries(imdbId: string): Promise<ScrapedSeriesData> {\n    const $ = await this.fetchPage(imdbId);\n    const basicInfo = this.extractBasicInfo($);\n\n    if (basicInfo.type !== 'series') {\n      throw new Error('IMDb ID does not correspond to a TV series');\n    }\n\n    const { rating: imdbRating, votes: imdbVotes } = this.extractIMDbRating($);\n    const { popularity, delta: popularityDelta } = this.extractPopularity($);\n    const { url: trailerUrl } = this.extractTrailerInfo($);\n\n    return {\n      title: basicInfo.title,\n      startYear: basicInfo.year,\n      endYear: undefined, // TODO: Extract end year\n      rating: this.extractRating($),\n      imdbRating,\n      imdbVotes,\n      popularity,\n      popularityDelta,\n      posterUrl: this.extractPosterUrl($),\n      backdropUrl: this.extractBackdropUrl($),\n      trailerUrl,\n      description: this.extractDescription($),\n      genres: this.extractGenres($),\n      creator: this.extractCreator($),\n      cast: this.extractCast($),\n      language: this.extractLanguage($),\n      country: this.extractCountry($),\n      totalSeasons: undefined, // TODO: Extract total seasons\n      status: 'ongoing', // TODO: Determine status\n    };\n  }\n}\n\nexport default IMDbScraper;\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AA8CA,MAAM;IACJ,OAAe,SAAsB;IAC7B,eAAe,EAAE;IACjB,kBAAkB,EAAE;IACX,aAAa,GAAG;IAChB,YAAY,KAAK;IAElC,OAAO,cAA2B;QAChC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,MAAc,YAA2B;QACvC,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,uBAAuB,MAAM,IAAI,CAAC,eAAe;QAEvD,IAAI,uBAAuB,IAAI,CAAC,SAAS,EAAE;YACzC,MAAM,QAAQ,IAAI,CAAC,SAAS,GAAG;YAC/B,gDAAgD;YAChD,MAAM,SAAS,KAAK,MAAM,KAAK,MAAM,wBAAwB;YAC7D,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,QAAQ;QAC3D;QAEA,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG;QAC/B,IAAI,CAAC,YAAY;IACnB;IAEQ,qBAA6B;QACnC,MAAM,aAAa;YACjB;YACA;YACA;YACA;YACA;SACD;QACD,OAAO,UAAU,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,WAAW,MAAM,EAAE;IAClE;IAEA,MAAc,UAAU,MAAc,EAA+B;QACnE,MAAM,IAAI,CAAC,SAAS;QAEpB,MAAM,MAAM,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;QACnD,MAAM,UAAU;YACd,cAAc,IAAI,CAAC,kBAAkB;YACrC,UAAU;YACV,mBAAmB;YACnB,mBAAmB;YACnB,cAAc;YACd,6BAA6B;YAC7B,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,iBAAiB;QACnB;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAC,KAAK;gBAAE;gBAAS,SAAS;YAAM;YAChE,OAAO,CAAA,GAAA,yJAAA,CAAA,OAAY,AAAD,EAAE,SAAS,IAAI;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC,EAAE;YACzD,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,MAAM,OAAO,EAAE;QAC/D;IACF;IAEQ,iBAAiB,CAAqB,EAA6D;QACzG,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI;QAEtC,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,qCAAqC;QACrC,MAAM,cAAc,EAAE;QACtB,MAAM,WAAW,YAAY,IAAI,GAAG,IAAI;QACxC,MAAM,OAAO,SAAS,aAAa,IAAI,OAAO,WAAW;QAEzD,sCAAsC;QACtC,MAAM,iBAAiB,EAAE,yBAAyB,IAAI,GAAG,WAAW;QACpE,MAAM,UAAU,CAAC,eAAe,QAAQ,CAAC,gBAAgB,CAAC,eAAe,QAAQ,CAAC;QAElF,OAAO;YACL;YACA;YACA,MAAM,UAAU,UAAU;QAC5B;IACF;IAEQ,cAAc,CAAqB,EAAsB;QAC/D,MAAM,gBAAgB,EAAE;QACxB,OAAO,cAAc,IAAI,GAAG,IAAI,MAAM;IACxC;IAEQ,eAAe,CAAqB,EAAsB;QAChE,MAAM,kBAAkB,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,MAAM,OAAO,EAAE,eAAe,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI;YAC9C,IAAI,KAAK,QAAQ,CAAC,QAAQ,KAAK,QAAQ,CAAC,QAAQ;gBAC9C,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEQ,kBAAkB,CAAqB,EAAuC;QACpF,MAAM,gBAAgB,EAAE;QACxB,MAAM,SAAS,WAAW,cAAc,IAAI,GAAG,IAAI,OAAO;QAE1D,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI,MAAM;QAE5C,OAAO;YAAE;YAAQ;QAAM;IACzB;IAEQ,kBAAkB,CAAqB,EAA2C;QACxF,MAAM,oBAAoB,EAAE;QAC5B,MAAM,aAAa,SAAS,kBAAkB,IAAI,GAAG,IAAI,OAAO;QAEhE,MAAM,eAAe,EAAE;QACvB,MAAM,YAAY,aAAa,IAAI,GAAG,IAAI;QAC1C,MAAM,QAAQ,YAAY,SAAS,UAAU,OAAO,CAAC,WAAW,OAAO;QAEvE,OAAO;YAAE;YAAY;QAAM;IAC7B;IAEQ,iBAAiB,CAAqB,EAAsB;QAClE,0CAA0C;QAC1C,MAAM,YAAY;YAChB;YACA;YACA;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,MAAM,MAAM,QAAQ,IAAI,CAAC;YACzB,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB;gBAC3C,6CAA6C;gBAC7C,OAAO,IAAI,OAAO,CAAC,aAAa,uBAAuB,OAAO,CAAC,YAAY;YAC7E;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,CAAqB,EAAsB;QACpE,qCAAqC;QACrC,MAAM,YAAY;YAChB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,MAAM,MAAM,QAAQ,IAAI,CAAC;YACzB,IAAI,OAAO,IAAI,QAAQ,CAAC,qBAAqB;gBAC3C,OAAO,IAAI,OAAO,CAAC,aAAa;YAClC;QACF;QAEA,OAAO;IACT;IAEQ,mBAAmB,CAAqB,EAAsD;QACpG,MAAM,iBAAiB,EAAE;QACzB,MAAM,MAAM,eAAe,IAAI,CAAC,WAAW;QAE3C,MAAM,iBAAiB,EAAE;QACzB,MAAM,UAAU,eAAe,IAAI,GAAG,IAAI,MAAM;QAEhD,MAAM,eAAe,EAAE;QACvB,MAAM,QAAQ,aAAa,IAAI,GAAG,IAAI,MAAM;QAE5C,OAAO;YAAE;YAAK;YAAS;QAAM;IAC/B;IAEQ,mBAAmB,CAAqB,EAAsB;QACpE,yCAAyC;QACzC,MAAM,YAAY;YAChB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,UAAW;YAChC,MAAM,UAAU,EAAE;YAClB,IAAI,SAAS,QAAQ,CAAC,QAAQ;gBAC5B,MAAM,MAAM,QAAQ,IAAI,CAAC;gBACzB,IAAI,OAAO,IAAI,MAAM,GAAG,IAAI,OAAO;YACrC,OAAO;gBACL,MAAM,OAAO,QAAQ,IAAI,GAAG,IAAI;gBAChC,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI,OAAO;YACvC;QACF;QAEA,OAAO;IACT;IAEQ,cAAc,CAAqB,EAAY;QACrD,MAAM,SAAmB,EAAE;QAE3B,0DAA0D;QAC1D,MAAM,iBAAiB;YACrB,iDAAiD;YACjD;YACA;YACA;YAEA,mCAAmC;YACnC;YACA;YAEA,iEAAiE;YACjE;YACA;YAEA,gDAAgD;YAChD;YACA;YACA;YAEA,uBAAuB;YACvB;YACA;YAEA,qBAAqB;YACrB;YACA;YAEA,mDAAmD;YACnD;SACD;QAED,QAAQ,GAAG,CAAC,CAAC,+BAA+B,CAAC;QAE7C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC9C,MAAM,WAAW,cAAc,CAAC,EAAE;YAClC,MAAM,WAAW,EAAE;YACnB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,EAAE,eAAe,MAAM,CAAC,GAAG,EAAE,SAAS,QAAQ,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;YAE5G,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,MAAM,QAAQ,EAAE,SAAS,IAAI,GAAG,IAAI;oBACpC,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,MAAM,CAAC,CAAC;oBAE7C,mCAAmC;oBACnC,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,QAAQ,CAAC,QAAQ;wBAC7E,6BAA6B;wBAC7B,MAAM,YAAY;4BAAC;4BAAU;4BAAS;4BAAW;4BAAQ;4BAAO;yBAAM;wBACtE,IAAI,CAAC,UAAU,QAAQ,CAAC,QAAQ;4BAC9B,OAAO,IAAI,CAAC;4BACZ,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;wBACzC;oBACF;gBACF;gBAEA,IAAI,OAAO,MAAM,GAAG,GAAG;oBACrB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,OAAO,MAAM,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC;oBACtF,OAAO,4CAA4C;gBACrD;YACF;QACF;QAEA,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,QAAQ,GAAG,CAAC;YACZ,4DAA4D;YAC5D,MAAM,mBAAmB,EAAE;YAC3B,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,QAAQ,GAAG,CAAC,8BAA8B,iBAAiB,IAAI;YACjE,OAAO;gBACL,QAAQ,GAAG,CAAC;YACd;QACF;QAEA,OAAO;IACT;IAEQ,YAAY,CAAqB,EAAY;QACnD,MAAM,OAAiB,EAAE;QAEzB,kCAAkC;QAClC,MAAM,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,cAAe;YACpC,MAAM,WAAW,EAAE;YACnB,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,SAAS,IAAI,CAAC,CAAC,GAAG;oBAChB,MAAM,YAAY,EAAE,SAAS,IAAI,GAAG,IAAI;oBACxC,IAAI,aAAa,CAAC,KAAK,QAAQ,CAAC,cAAc,KAAK,MAAM,GAAG,IAAI;wBAC9D,KAAK,IAAI,CAAC;oBACZ;gBACF;gBACA,OAAO,4CAA4C;YACrD;QACF;QAEA,OAAO;IACT;IAEQ,gBAAgB,CAAqB,EAAsB;QACjE,sCAAsC;QACtC,MAAM,oBAAoB;YACxB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,kBAAmB;YACxC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,WAAW,QAAQ,IAAI,GAAG,IAAI;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,eAAe,CAAqB,EAAsB;QAChE,qDAAqD;QACrD,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI;YACnC,IAAI,SAAS;gBACX,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,gBAAgB,CAAqB,EAAsB;QACjE,sCAAsC;QACtC,MAAM,oBAAoB;YACxB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,kBAAmB;YACxC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,WAAW,QAAQ,IAAI,GAAG,IAAI;YACpC,IAAI,UAAU;gBACZ,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEQ,eAAe,CAAqB,EAAsB;QAChE,qCAAqC;QACrC,MAAM,mBAAmB;YACvB;YACA;YACA;YACA;SACD;QAED,KAAK,MAAM,YAAY,iBAAkB;YACvC,MAAM,UAAU,EAAE,UAAU,KAAK;YACjC,MAAM,UAAU,QAAQ,IAAI,GAAG,IAAI;YACnC,IAAI,SAAS;gBACX,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,YAAY,MAAc,EAA6B;QAC3D,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;QAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAExC,IAAI,UAAU,IAAI,KAAK,SAAS;YAC9B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,QAAQ,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxE,MAAM,EAAE,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtE,MAAM,EAAE,KAAK,UAAU,EAAE,SAAS,cAAc,EAAE,OAAO,YAAY,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAElG,OAAO;YACL,OAAO,UAAU,KAAK;YACtB,MAAM,UAAU,IAAI;YACpB,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B;YACA;YACA;YACA;YACA,WAAW,IAAI,CAAC,gBAAgB,CAAC;YACjC,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC;YACA;YACA;YACA,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,SAAS,IAAI,CAAC,cAAc,CAAC;QAC/B;IACF;IAEA,MAAM,aAAa,MAAc,EAA8B;QAC7D,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,CAAC;QAC/B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QAExC,IAAI,UAAU,IAAI,KAAK,UAAU;YAC/B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,EAAE,QAAQ,UAAU,EAAE,OAAO,SAAS,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACxE,MAAM,EAAE,UAAU,EAAE,OAAO,eAAe,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACtE,MAAM,EAAE,KAAK,UAAU,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAEpD,OAAO;YACL,OAAO,UAAU,KAAK;YACtB,WAAW,UAAU,IAAI;YACzB,SAAS;YACT,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B;YACA;YACA;YACA;YACA,WAAW,IAAI,CAAC,gBAAgB,CAAC;YACjC,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC;YACA,aAAa,IAAI,CAAC,kBAAkB,CAAC;YACrC,QAAQ,IAAI,CAAC,aAAa,CAAC;YAC3B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,MAAM,IAAI,CAAC,WAAW,CAAC;YACvB,UAAU,IAAI,CAAC,eAAe,CAAC;YAC/B,SAAS,IAAI,CAAC,cAAc,CAAC;YAC7B,cAAc;YACd,QAAQ;QACV;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1177, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/vidsrc.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst VIDSRC_BASE_URL = process.env.VIDSRC_BASE_URL || 'https://vidsrc.xyz';\n\n// Multiple streaming sources for better reliability\nexport const STREAMING_SOURCES = {\n  vidsrc_xyz: {\n    name: 'VidSrc XYZ',\n    baseUrl: 'https://vidsrc.xyz',\n    quality: 'HD',\n    priority: 1\n  },\n  vidsrc_cc_v2: {\n    name: 'VidSrc CC v2',\n    baseUrl: 'https://vidsrc.cc/v2',\n    quality: 'HD',\n    priority: 2\n  },\n  vidsrc_cc_v3: {\n    name: 'VidSrc CC v3',\n    baseUrl: 'https://vidsrc.cc/v3',\n    quality: 'HD',\n    priority: 3\n  },\n  vidsrc_me: {\n    name: 'VidSrc ME',\n    baseUrl: 'https://vidsrc.me',\n    quality: 'HD',\n    priority: 4\n  },\n  superembed: {\n    name: 'SuperEmbed',\n    baseUrl: 'https://multiembed.mov',\n    quality: 'HD',\n    priority: 5\n  },\n  superembed_vip: {\n    name: 'SuperEmbed VIP',\n    baseUrl: 'https://multiembed.mov/directstream.php',\n    quality: 'Premium HD',\n    priority: 6\n  }\n};\n\nexport interface VidSrcMovieData {\n  imdb_id: string;\n  tmdb_id?: string;\n  title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\nexport interface VidSrcSeriesData {\n  imdb_id: string;\n  tmdb_id?: string;\n  show_title: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n}\n\nexport interface VidSrcEpisodeData {\n  imdb_id: string;\n  tmdb_id?: string;\n  show_title: string;\n  season: string;\n  episode: string;\n  embed_url: string;\n  embed_url_tmdb?: string;\n  quality?: string;\n}\n\nexport interface VidSrcLatestResponse<T> {\n  result: T[];\n}\n\nclass VidSrcAPI {\n  private static instance: VidSrcAPI;\n\n  static getInstance(): VidSrcAPI {\n    if (!VidSrcAPI.instance) {\n      VidSrcAPI.instance = new VidSrcAPI();\n    }\n    return VidSrcAPI.instance;\n  }\n\n  /**\n   * Generate movie embed URLs for all sources\n   */\n  generateAllMovieEmbedUrls(imdbId: string, tmdbId?: string): Array<{\n    source: string;\n    name: string;\n    url: string;\n    quality: string;\n    priority: number;\n  }> {\n    const cleanImdbId = imdbId.replace('tt', '');\n    const urls = [];\n\n    // VidSrc XYZ\n    urls.push({\n      source: 'vidsrc_xyz',\n      name: STREAMING_SOURCES.vidsrc_xyz.name,\n      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/movie?imdb=${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_xyz.quality,\n      priority: STREAMING_SOURCES.vidsrc_xyz.priority\n    });\n\n    // VidSrc CC v2\n    urls.push({\n      source: 'vidsrc_cc_v2',\n      name: STREAMING_SOURCES.vidsrc_cc_v2.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority\n    });\n\n    // VidSrc CC v3\n    urls.push({\n      source: 'vidsrc_cc_v3',\n      name: STREAMING_SOURCES.vidsrc_cc_v3.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/movie/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority\n    });\n\n    // VidSrc ME\n    urls.push({\n      source: 'vidsrc_me',\n      name: STREAMING_SOURCES.vidsrc_me.name,\n      url: `${STREAMING_SOURCES.vidsrc_me.baseUrl}/embed/${imdbId}`,\n      quality: STREAMING_SOURCES.vidsrc_me.quality,\n      priority: STREAMING_SOURCES.vidsrc_me.priority\n    });\n\n    // SuperEmbed\n    urls.push({\n      source: 'superembed',\n      name: STREAMING_SOURCES.superembed.name,\n      url: `${STREAMING_SOURCES.superembed.baseUrl}/?video_id=${imdbId}`,\n      quality: STREAMING_SOURCES.superembed.quality,\n      priority: STREAMING_SOURCES.superembed.priority\n    });\n\n    // SuperEmbed VIP\n    urls.push({\n      source: 'superembed_vip',\n      name: STREAMING_SOURCES.superembed_vip.name,\n      url: `${STREAMING_SOURCES.superembed_vip.baseUrl}?video_id=${imdbId}`,\n      quality: STREAMING_SOURCES.superembed_vip.quality,\n      priority: STREAMING_SOURCES.superembed_vip.priority\n    });\n\n    return urls.sort((a, b) => a.priority - b.priority);\n  }\n\n  /**\n   * Generate movie embed URL (legacy method for backward compatibility)\n   */\n  generateMovieEmbedUrl(imdbId: string, options?: {\n    tmdbId?: string;\n    subUrl?: string;\n    dsLang?: string;\n    autoplay?: boolean;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/movie`;\n    const params = new URLSearchParams();\n\n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n\n    if (options?.subUrl) {\n      params.append('sub_url', encodeURIComponent(options.subUrl));\n    }\n\n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n\n    if (options?.autoplay !== undefined) {\n      params.append('autoplay', options.autoplay ? '1' : '0');\n    }\n\n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Generate episode embed URLs for all sources\n   */\n  generateAllEpisodeEmbedUrls(imdbId: string, season: number, episode: number, tmdbId?: string): Array<{\n    source: string;\n    name: string;\n    url: string;\n    quality: string;\n    priority: number;\n  }> {\n    const cleanImdbId = imdbId.replace('tt', '');\n    const urls = [];\n\n    // VidSrc XYZ\n    urls.push({\n      source: 'vidsrc_xyz',\n      name: STREAMING_SOURCES.vidsrc_xyz.name,\n      url: `${STREAMING_SOURCES.vidsrc_xyz.baseUrl}/embed/tv?imdb=${imdbId}&season=${season}&episode=${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_xyz.quality,\n      priority: STREAMING_SOURCES.vidsrc_xyz.priority\n    });\n\n    // VidSrc CC v2\n    urls.push({\n      source: 'vidsrc_cc_v2',\n      name: STREAMING_SOURCES.vidsrc_cc_v2.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v2.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v2.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v2.priority\n    });\n\n    // VidSrc CC v3\n    urls.push({\n      source: 'vidsrc_cc_v3',\n      name: STREAMING_SOURCES.vidsrc_cc_v3.name,\n      url: `${STREAMING_SOURCES.vidsrc_cc_v3.baseUrl}/embed/tv/${imdbId}/${season}/${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_cc_v3.quality,\n      priority: STREAMING_SOURCES.vidsrc_cc_v3.priority\n    });\n\n    // VidSrc ME\n    urls.push({\n      source: 'vidsrc_me',\n      name: STREAMING_SOURCES.vidsrc_me.name,\n      url: `${STREAMING_SOURCES.vidsrc_me.baseUrl}/embed/${imdbId}/${season}-${episode}`,\n      quality: STREAMING_SOURCES.vidsrc_me.quality,\n      priority: STREAMING_SOURCES.vidsrc_me.priority\n    });\n\n    // SuperEmbed\n    urls.push({\n      source: 'superembed',\n      name: STREAMING_SOURCES.superembed.name,\n      url: `${STREAMING_SOURCES.superembed.baseUrl}/?video_id=${imdbId}&s=${season}&e=${episode}`,\n      quality: STREAMING_SOURCES.superembed.quality,\n      priority: STREAMING_SOURCES.superembed.priority\n    });\n\n    // SuperEmbed VIP\n    urls.push({\n      source: 'superembed_vip',\n      name: STREAMING_SOURCES.superembed_vip.name,\n      url: `${STREAMING_SOURCES.superembed_vip.baseUrl}?video_id=${imdbId}&s=${season}&e=${episode}`,\n      quality: STREAMING_SOURCES.superembed_vip.quality,\n      priority: STREAMING_SOURCES.superembed_vip.priority\n    });\n\n    return urls.sort((a, b) => a.priority - b.priority);\n  }\n\n  /**\n   * Generate series embed URL (legacy method for backward compatibility)\n   */\n  generateSeriesEmbedUrl(imdbId: string, options?: {\n    tmdbId?: string;\n    dsLang?: string;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;\n    const params = new URLSearchParams();\n\n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n\n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n\n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Generate episode embed URL\n   */\n  generateEpisodeEmbedUrl(imdbId: string, season: number, episode: number, options?: {\n    tmdbId?: string;\n    subUrl?: string;\n    dsLang?: string;\n    autoplay?: boolean;\n    autonext?: boolean;\n  }): string {\n    const baseUrl = `${VIDSRC_BASE_URL}/embed/tv`;\n    const params = new URLSearchParams();\n    \n    if (options?.tmdbId) {\n      params.append('tmdb', options.tmdbId);\n    } else {\n      params.append('imdb', imdbId);\n    }\n    \n    params.append('season', season.toString());\n    params.append('episode', episode.toString());\n    \n    if (options?.subUrl) {\n      params.append('sub_url', encodeURIComponent(options.subUrl));\n    }\n    \n    if (options?.dsLang) {\n      params.append('ds_lang', options.dsLang);\n    }\n    \n    if (options?.autoplay !== undefined) {\n      params.append('autoplay', options.autoplay ? '1' : '0');\n    }\n    \n    if (options?.autonext !== undefined) {\n      params.append('autonext', options.autonext ? '1' : '0');\n    }\n    \n    return `${baseUrl}?${params.toString()}`;\n  }\n\n  /**\n   * Fetch latest movies from VidSrc\n   */\n  async getLatestMovies(page: number = 1): Promise<VidSrcMovieData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/movies/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcMovieData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest movies from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Fetch latest TV shows from VidSrc\n   */\n  async getLatestSeries(page: number = 1): Promise<VidSrcSeriesData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/tvshows/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcSeriesData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest series from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Fetch latest episodes from VidSrc\n   */\n  async getLatestEpisodes(page: number = 1): Promise<VidSrcEpisodeData[]> {\n    try {\n      const url = `${VIDSRC_BASE_URL}/episodes/latest/page-${page}.json`;\n      const response = await axios.get<VidSrcLatestResponse<VidSrcEpisodeData>>(url);\n      return response.data.result || [];\n    } catch (error) {\n      console.error(`Error fetching latest episodes from VidSrc (page ${page}):`, error);\n      return [];\n    }\n  }\n\n  /**\n   * SIMPLE: Get episodes for a specific series from VidSrc (for embed URLs only)\n   * This is now used only to get streaming links, not for episode discovery\n   */\n  async getSeriesEpisodes(imdbId: string): Promise<Array<{\n    season: number;\n    episode: number;\n    embed_url: string;\n    embed_url_tmdb?: string;\n  }>> {\n    try {\n      console.log(`🔍 ADVANCED: Comprehensive episode search for series: ${imdbId}`);\n\n      const allEpisodes = new Map<string, {\n        season: number;\n        episode: number;\n        embed_url: string;\n        embed_url_tmdb?: string;\n      }>();\n\n      // Strategy 1: Search through ALL latest episodes (not just 15 pages)\n      console.log(`📡 Strategy 1: Scanning latest episodes across all pages...`);\n      let foundEpisodesInLatest = 0;\n      for (let page = 1; page <= 50; page++) { // Increased to 50 pages for comprehensive search\n        try {\n          const episodes = await this.getLatestEpisodes(page);\n          if (episodes.length === 0) break; // No more episodes\n\n          const seriesEpisodes = episodes.filter(episode => episode.imdb_id === imdbId);\n\n          seriesEpisodes.forEach(episode => {\n            const key = `S${episode.season}E${episode.episode}`;\n            if (!allEpisodes.has(key)) {\n              allEpisodes.set(key, {\n                season: episode.season,\n                episode: episode.episode,\n                embed_url: episode.embed_url,\n                embed_url_tmdb: episode.embed_url_tmdb\n              });\n              foundEpisodesInLatest++;\n            }\n          });\n\n          if (seriesEpisodes.length > 0) {\n            console.log(`📺 Page ${page}: Found ${seriesEpisodes.length} episodes`);\n          }\n\n          // If no episodes found in last 10 pages, likely reached the end\n          if (page > 10 && seriesEpisodes.length === 0) {\n            let emptyPages = 0;\n            for (let checkPage = page - 9; checkPage <= page; checkPage++) {\n              const checkEpisodes = await this.getLatestEpisodes(checkPage);\n              if (checkEpisodes.filter(ep => ep.imdb_id === imdbId).length === 0) {\n                emptyPages++;\n              }\n            }\n            if (emptyPages >= 8) break; // Stop if 8/10 recent pages are empty\n          }\n        } catch (error) {\n          console.error(`Error fetching episodes page ${page}:`, error);\n        }\n      }\n\n      console.log(`✅ Strategy 1 Complete: Found ${foundEpisodesInLatest} episodes in latest pages`);\n\n      // Strategy 2: Search through series-specific pages (if available)\n      console.log(`📡 Strategy 2: Searching series-specific endpoints...`);\n      let foundEpisodesInSeries = 0;\n      for (let page = 1; page <= 20; page++) {\n        try {\n          const seriesEpisodes = await this.getLatestSeries(page);\n          const matchingSeries = seriesEpisodes.filter(series => series.imdb_id === imdbId);\n\n          if (matchingSeries.length > 0) {\n            console.log(`📺 Series page ${page}: Found matching series, checking for episode data`);\n            // If series data includes episode information, extract it\n            // This is a placeholder for potential series-specific episode data\n          }\n        } catch (error) {\n          // Series endpoint might not exist, continue\n        }\n      }\n\n      // Strategy 3: Systematic season-by-season verification\n      console.log(`📡 Strategy 3: Systematic season verification...`);\n      const episodesBySeason = new Map<number, number[]>();\n\n      // Group found episodes by season\n      allEpisodes.forEach((episode, key) => {\n        if (!episodesBySeason.has(episode.season)) {\n          episodesBySeason.set(episode.season, []);\n        }\n        episodesBySeason.get(episode.season)!.push(episode.episode);\n      });\n\n      // Analyze each season for gaps and missing episodes\n      for (const [season, episodes] of episodesBySeason) {\n        episodes.sort((a, b) => a - b);\n        const minEp = Math.min(...episodes);\n        const maxEp = Math.max(...episodes);\n        const missingEpisodes: number[] = [];\n\n        for (let ep = 1; ep <= maxEp; ep++) {\n          if (!episodes.includes(ep)) {\n            missingEpisodes.push(ep);\n          }\n        }\n\n        if (missingEpisodes.length > 0) {\n          console.log(`⚠️ Season ${season}: Missing episodes ${missingEpisodes.join(', ')} (Have: ${episodes.join(', ')})`);\n\n          // Strategy 3a: Search for missing episodes specifically\n          console.log(`🔍 Searching for missing episodes in Season ${season}...`);\n          // This would involve more targeted searches if VidSrc had episode-specific endpoints\n        }\n      }\n\n      // Convert Map back to array and sort\n      const uniqueEpisodes = Array.from(allEpisodes.values()).sort((a, b) => {\n        if (a.season !== b.season) return a.season - b.season;\n        return a.episode - b.episode;\n      });\n\n      // Final analysis\n      const totalSeasons = episodesBySeason.size;\n      const totalEpisodes = uniqueEpisodes.length;\n      const seasonRanges = Array.from(episodesBySeason.keys()).sort((a, b) => a - b);\n      const minSeason = seasonRanges[0] || 0;\n      const maxSeason = seasonRanges[seasonRanges.length - 1] || 0;\n\n      console.log(`🎯 COMPREHENSIVE SEARCH COMPLETE:`);\n      console.log(`   📺 Total Episodes Found: ${totalEpisodes}`);\n      console.log(`   🗂️ Seasons Found: ${totalSeasons} (S${minSeason}-S${maxSeason})`);\n      console.log(`   📊 Episodes per Season:`);\n\n      episodesBySeason.forEach((episodes, season) => {\n        episodes.sort((a, b) => a - b);\n        const gaps = [];\n        const maxEp = Math.max(...episodes);\n        for (let ep = 1; ep <= maxEp; ep++) {\n          if (!episodes.includes(ep)) gaps.push(ep);\n        }\n        console.log(`      S${season}: ${episodes.length} episodes (${episodes.join(', ')})${gaps.length > 0 ? ` - Missing: ${gaps.join(', ')}` : ' - Complete'}`);\n      });\n\n      return uniqueEpisodes;\n\n    } catch (error) {\n      console.error(`Error in comprehensive episode search for series ${imdbId}:`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Sync latest content from VidSrc to our database\n   */\n  async syncLatestContent(pages: number = 5): Promise<{\n    movies: VidSrcMovieData[];\n    series: VidSrcSeriesData[];\n    episodes: VidSrcEpisodeData[];\n  }> {\n    const movies: VidSrcMovieData[] = [];\n    const series: VidSrcSeriesData[] = [];\n    const episodes: VidSrcEpisodeData[] = [];\n\n    // Fetch multiple pages in parallel\n    const moviePromises = Array.from({ length: pages }, (_, i) => this.getLatestMovies(i + 1));\n    const seriesPromises = Array.from({ length: pages }, (_, i) => this.getLatestSeries(i + 1));\n    const episodePromises = Array.from({ length: pages }, (_, i) => this.getLatestEpisodes(i + 1));\n\n    try {\n      const [movieResults, seriesResults, episodeResults] = await Promise.all([\n        Promise.all(moviePromises),\n        Promise.all(seriesPromises),\n        Promise.all(episodePromises)\n      ]);\n\n      // Flatten results\n      movieResults.forEach(pageMovies => movies.push(...pageMovies));\n      seriesResults.forEach(pageSeries => series.push(...pageSeries));\n      episodeResults.forEach(pageEpisodes => episodes.push(...pageEpisodes));\n\n      return { movies, series, episodes };\n    } catch (error) {\n      console.error('Error syncing latest content from VidSrc:', error);\n      return { movies, series, episodes };\n    }\n  }\n}\n\nexport default VidSrcAPI;\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB,QAAQ,GAAG,CAAC,eAAe,IAAI;AAGhD,MAAM,oBAAoB;IAC/B,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,cAAc;QACZ,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,WAAW;QACT,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,YAAY;QACV,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;IACA,gBAAgB;QACd,MAAM;QACN,SAAS;QACT,SAAS;QACT,UAAU;IACZ;AACF;AAkCA,MAAM;IACJ,OAAe,SAAoB;IAEnC,OAAO,cAAyB;QAC9B,IAAI,CAAC,UAAU,QAAQ,EAAE;YACvB,UAAU,QAAQ,GAAG,IAAI;QAC3B;QACA,OAAO,UAAU,QAAQ;IAC3B;IAEA;;GAEC,GACD,0BAA0B,MAAc,EAAE,MAAe,EAMtD;QACD,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM;QACzC,MAAM,OAAO,EAAE;QAEf,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,kBAAkB,EAAE,QAAQ;YACzE,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACtE,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ;YACtE,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,YAAY;QACZ,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,SAAS,CAAC,IAAI;YACtC,KAAK,GAAG,kBAAkB,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ;YAC7D,SAAS,kBAAkB,SAAS,CAAC,OAAO;YAC5C,UAAU,kBAAkB,SAAS,CAAC,QAAQ;QAChD;QAEA,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,QAAQ;YAClE,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,iBAAiB;QACjB,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,cAAc,CAAC,IAAI;YAC3C,KAAK,GAAG,kBAAkB,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,QAAQ;YACrE,SAAS,kBAAkB,cAAc,CAAC,OAAO;YACjD,UAAU,kBAAkB,cAAc,CAAC,QAAQ;QACrD;QAEA,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACpD;IAEA;;GAEC,GACD,sBAAsB,MAAc,EAAE,OAKrC,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,YAAY,CAAC;QAChD,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,mBAAmB,QAAQ,MAAM;QAC5D;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,4BAA4B,MAAc,EAAE,MAAc,EAAE,OAAe,EAAE,MAAe,EAMzF;QACD,MAAM,cAAc,OAAO,OAAO,CAAC,MAAM;QACzC,MAAM,OAAO,EAAE;QAEf,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,eAAe,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,SAAS;YAC1G,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACxF,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,eAAe;QACf,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,YAAY,CAAC,IAAI;YACzC,KAAK,GAAG,kBAAkB,YAAY,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YACxF,SAAS,kBAAkB,YAAY,CAAC,OAAO;YAC/C,UAAU,kBAAkB,YAAY,CAAC,QAAQ;QACnD;QAEA,YAAY;QACZ,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,SAAS,CAAC,IAAI;YACtC,KAAK,GAAG,kBAAkB,SAAS,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,SAAS;YAClF,SAAS,kBAAkB,SAAS,CAAC,OAAO;YAC5C,UAAU,kBAAkB,SAAS,CAAC,QAAQ;QAChD;QAEA,aAAa;QACb,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,UAAU,CAAC,IAAI;YACvC,KAAK,GAAG,kBAAkB,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,EAAE,SAAS;YAC3F,SAAS,kBAAkB,UAAU,CAAC,OAAO;YAC7C,UAAU,kBAAkB,UAAU,CAAC,QAAQ;QACjD;QAEA,iBAAiB;QACjB,KAAK,IAAI,CAAC;YACR,QAAQ;YACR,MAAM,kBAAkB,cAAc,CAAC,IAAI;YAC3C,KAAK,GAAG,kBAAkB,cAAc,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,GAAG,EAAE,OAAO,GAAG,EAAE,SAAS;YAC9F,SAAS,kBAAkB,cAAc,CAAC,OAAO;YACjD,UAAU,kBAAkB,cAAc,CAAC,QAAQ;QACrD;QAEA,OAAO,KAAK,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACpD;IAEA;;GAEC,GACD,uBAAuB,MAAc,EAAE,OAGtC,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,SAAS,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,wBAAwB,MAAc,EAAE,MAAc,EAAE,OAAe,EAAE,OAMxE,EAAU;QACT,MAAM,UAAU,GAAG,gBAAgB,SAAS,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,QAAQ,QAAQ,MAAM;QACtC,OAAO;YACL,OAAO,MAAM,CAAC,QAAQ;QACxB;QAEA,OAAO,MAAM,CAAC,UAAU,OAAO,QAAQ;QACvC,OAAO,MAAM,CAAC,WAAW,QAAQ,QAAQ;QAEzC,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,mBAAmB,QAAQ,MAAM;QAC5D;QAEA,IAAI,SAAS,QAAQ;YACnB,OAAO,MAAM,CAAC,WAAW,QAAQ,MAAM;QACzC;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,IAAI,SAAS,aAAa,WAAW;YACnC,OAAO,MAAM,CAAC,YAAY,QAAQ,QAAQ,GAAG,MAAM;QACrD;QAEA,OAAO,GAAG,QAAQ,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC1C;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,CAAC,EAA8B;QAClE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,oBAAoB,EAAE,KAAK,KAAK,CAAC;YAChE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAwC;YACxE,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,gBAAgB,OAAe,CAAC,EAA+B;QACnE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,qBAAqB,EAAE,KAAK,KAAK,CAAC;YACjE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAAyC;YACzE,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,KAAK,EAAE,CAAC,EAAE;YAC1E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,OAAe,CAAC,EAAgC;QACtE,IAAI;YACF,MAAM,MAAM,GAAG,gBAAgB,sBAAsB,EAAE,KAAK,KAAK,CAAC;YAClE,MAAM,WAAW,MAAM,uIAAA,CAAA,UAAK,CAAC,GAAG,CAA0C;YAC1E,OAAO,SAAS,IAAI,CAAC,MAAM,IAAI,EAAE;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,KAAK,EAAE,CAAC,EAAE;YAC5E,OAAO,EAAE;QACX;IACF;IAEA;;;GAGC,GACD,MAAM,kBAAkB,MAAc,EAKlC;QACF,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sDAAsD,EAAE,QAAQ;YAE7E,MAAM,cAAc,IAAI;YAOxB,qEAAqE;YACrE,QAAQ,GAAG,CAAC,CAAC,2DAA2D,CAAC;YACzE,IAAI,wBAAwB;YAC5B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;gBACrC,IAAI;oBACF,MAAM,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC;oBAC9C,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO,mBAAmB;oBAErD,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;oBAEtE,eAAe,OAAO,CAAC,CAAA;wBACrB,MAAM,MAAM,CAAC,CAAC,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,EAAE;wBACnD,IAAI,CAAC,YAAY,GAAG,CAAC,MAAM;4BACzB,YAAY,GAAG,CAAC,KAAK;gCACnB,QAAQ,QAAQ,MAAM;gCACtB,SAAS,QAAQ,OAAO;gCACxB,WAAW,QAAQ,SAAS;gCAC5B,gBAAgB,QAAQ,cAAc;4BACxC;4BACA;wBACF;oBACF;oBAEA,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,QAAQ,EAAE,eAAe,MAAM,CAAC,SAAS,CAAC;oBACxE;oBAEA,gEAAgE;oBAChE,IAAI,OAAO,MAAM,eAAe,MAAM,KAAK,GAAG;wBAC5C,IAAI,aAAa;wBACjB,IAAK,IAAI,YAAY,OAAO,GAAG,aAAa,MAAM,YAAa;4BAC7D,MAAM,gBAAgB,MAAM,IAAI,CAAC,iBAAiB,CAAC;4BACnD,IAAI,cAAc,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK,QAAQ,MAAM,KAAK,GAAG;gCAClE;4BACF;wBACF;wBACA,IAAI,cAAc,GAAG,OAAO,sCAAsC;oBACpE;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC,EAAE;gBACzD;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,sBAAsB,yBAAyB,CAAC;YAE5F,kEAAkE;YAClE,QAAQ,GAAG,CAAC,CAAC,qDAAqD,CAAC;YACnE,IAAI,wBAAwB;YAC5B,IAAK,IAAI,OAAO,GAAG,QAAQ,IAAI,OAAQ;gBACrC,IAAI;oBACF,MAAM,iBAAiB,MAAM,IAAI,CAAC,eAAe,CAAC;oBAClD,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,SAAU,OAAO,OAAO,KAAK;oBAE1E,IAAI,eAAe,MAAM,GAAG,GAAG;wBAC7B,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK,kDAAkD,CAAC;oBACtF,0DAA0D;oBAC1D,mEAAmE;oBACrE;gBACF,EAAE,OAAO,OAAO;gBACd,4CAA4C;gBAC9C;YACF;YAEA,uDAAuD;YACvD,QAAQ,GAAG,CAAC,CAAC,gDAAgD,CAAC;YAC9D,MAAM,mBAAmB,IAAI;YAE7B,iCAAiC;YACjC,YAAY,OAAO,CAAC,CAAC,SAAS;gBAC5B,IAAI,CAAC,iBAAiB,GAAG,CAAC,QAAQ,MAAM,GAAG;oBACzC,iBAAiB,GAAG,CAAC,QAAQ,MAAM,EAAE,EAAE;gBACzC;gBACA,iBAAiB,GAAG,CAAC,QAAQ,MAAM,EAAG,IAAI,CAAC,QAAQ,OAAO;YAC5D;YAEA,oDAAoD;YACpD,KAAK,MAAM,CAAC,QAAQ,SAAS,IAAI,iBAAkB;gBACjD,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;gBAC5B,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,MAAM,kBAA4B,EAAE;gBAEpC,IAAK,IAAI,KAAK,GAAG,MAAM,OAAO,KAAM;oBAClC,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK;wBAC1B,gBAAgB,IAAI,CAAC;oBACvB;gBACF;gBAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;oBAC9B,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,mBAAmB,EAAE,gBAAgB,IAAI,CAAC,MAAM,QAAQ,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC;oBAEhH,wDAAwD;oBACxD,QAAQ,GAAG,CAAC,CAAC,4CAA4C,EAAE,OAAO,GAAG,CAAC;gBACtE,qFAAqF;gBACvF;YACF;YAEA,qCAAqC;YACrC,MAAM,iBAAiB,MAAM,IAAI,CAAC,YAAY,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG;gBAC/D,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;gBACrD,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO;YAC9B;YAEA,iBAAiB;YACjB,MAAM,eAAe,iBAAiB,IAAI;YAC1C,MAAM,gBAAgB,eAAe,MAAM;YAC3C,MAAM,eAAe,MAAM,IAAI,CAAC,iBAAiB,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YAC5E,MAAM,YAAY,YAAY,CAAC,EAAE,IAAI;YACrC,MAAM,YAAY,YAAY,CAAC,aAAa,MAAM,GAAG,EAAE,IAAI;YAE3D,QAAQ,GAAG,CAAC,CAAC,iCAAiC,CAAC;YAC/C,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,eAAe;YAC1D,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,aAAa,GAAG,EAAE,UAAU,EAAE,EAAE,UAAU,CAAC,CAAC;YACjF,QAAQ,GAAG,CAAC,CAAC,0BAA0B,CAAC;YAExC,iBAAiB,OAAO,CAAC,CAAC,UAAU;gBAClC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;gBAC5B,MAAM,OAAO,EAAE;gBACf,MAAM,QAAQ,KAAK,GAAG,IAAI;gBAC1B,IAAK,IAAI,KAAK,GAAG,MAAM,OAAO,KAAM;oBAClC,IAAI,CAAC,SAAS,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC;gBACxC;gBACA,QAAQ,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,EAAE,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE,KAAK,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,KAAK,IAAI,CAAC,OAAO,GAAG,eAAe;YAC3J;YAEA,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iDAAiD,EAAE,OAAO,CAAC,CAAC,EAAE;YAC7E,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,MAAM,kBAAkB,QAAgB,CAAC,EAItC;QACD,MAAM,SAA4B,EAAE;QACpC,MAAM,SAA6B,EAAE;QACrC,MAAM,WAAgC,EAAE;QAExC,mCAAmC;QACnC,MAAM,gBAAgB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI;QACvF,MAAM,iBAAiB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI;QACxF,MAAM,kBAAkB,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAM,GAAG,CAAC,GAAG,IAAM,IAAI,CAAC,iBAAiB,CAAC,IAAI;QAE3F,IAAI;YACF,MAAM,CAAC,cAAc,eAAe,eAAe,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACtE,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC;aACb;YAED,kBAAkB;YAClB,aAAa,OAAO,CAAC,CAAA,aAAc,OAAO,IAAI,IAAI;YAClD,cAAc,OAAO,CAAC,CAAA,aAAc,OAAO,IAAI,IAAI;YACnD,eAAe,OAAO,CAAC,CAAA,eAAgB,SAAS,IAAI,IAAI;YAExD,OAAO;gBAAE;gBAAQ;gBAAQ;YAAS;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,OAAO;gBAAE;gBAAQ;gBAAQ;YAAS;QACpC;IACF;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/lib/contentService.ts"], "sourcesContent": ["import connectDB from './mongodb';\nimport Movie, { IMovie } from '../models/Movie';\nimport Series, { ISeries } from '../models/Series';\nimport Episode, { IEpisode } from '../models/Episode';\nimport Request, { IRequest } from '../models/Request';\nimport IMDbScraper from './scraper';\nimport VidSrcAPI from './vidsrc';\n\nexport interface ContentFilters {\n  genre?: string;\n  year?: number;\n  language?: string;\n  country?: string;\n  rating?: string;\n  quality?: string;\n  sortBy?: 'title' | 'year' | 'imdbRating' | 'popularity' | 'createdAt';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n  search?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    pages: number;\n  };\n}\n\nexport interface GenreCount {\n  genre: string;\n  count: number;\n}\n\nexport interface LanguageCount {\n  language: string;\n  count: number;\n}\n\nexport interface CountryCount {\n  country: string;\n  count: number;\n}\n\nexport interface FilterOptions {\n  genres: GenreCount[];\n  languages: LanguageCount[];\n  countries: CountryCount[];\n  years: number[];\n  ratings: string[];\n  qualities: string[];\n}\n\nclass ContentService {\n  private static instance: ContentService;\n  private scraper: IMDbScraper;\n  private vidsrc: VidSrcAPI;\n\n  constructor() {\n    this.scraper = IMDbScraper.getInstance();\n    this.vidsrc = VidSrcAPI.getInstance();\n  }\n\n  static getInstance(): ContentService {\n    if (!ContentService.instance) {\n      ContentService.instance = new ContentService();\n    }\n    return ContentService.instance;\n  }\n\n  async getMovies(filters: ContentFilters = {}): Promise<PaginatedResponse<IMovie>> {\n    await connectDB();\n\n    const {\n      genre,\n      year,\n      language,\n      country,\n      rating,\n      quality,\n      sortBy = 'createdAt',\n      sortOrder = 'desc',\n      page = 1,\n      limit = 20,\n      search\n    } = filters;\n\n    // Build query\n    const query: any = {};\n\n    if (genre) query.genres = { $in: [genre] };\n    if (year) query.year = year;\n    if (language) query.language = language;\n    if (country) query.country = country;\n    if (rating) query.rating = rating;\n    if (quality) query.quality = quality;\n    if (search) {\n      // Use regex search instead of text search to avoid language override issues\n      query.$or = [\n        { title: { $regex: search, $options: 'i' } },\n        { description: { $regex: search, $options: 'i' } }\n      ];\n    }\n\n    // Build sort\n    const sort: any = {};\n    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;\n\n    const skip = (page - 1) * limit;\n\n    const [data, total] = await Promise.all([\n      Movie.find(query)\n        .sort(sort)\n        .skip(skip)\n        .limit(limit)\n        .lean(),\n      Movie.countDocuments(query)\n    ]);\n\n    return {\n      data,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    };\n  }\n\n  async getSeries(filters: ContentFilters = {}): Promise<PaginatedResponse<ISeries>> {\n    await connectDB();\n\n    const {\n      genre,\n      year,\n      language,\n      country,\n      rating,\n      sortBy = 'createdAt',\n      sortOrder = 'desc',\n      page = 1,\n      limit = 20,\n      search\n    } = filters;\n\n    const query: any = {};\n\n    if (genre) query.genres = { $in: [genre] };\n    if (year) query.startYear = year;\n    if (language) query.language = language;\n    if (country) query.country = country;\n    if (rating) query.rating = rating;\n    if (search) {\n      // Use regex search instead of text search to avoid language override issues\n      query.$or = [\n        { title: { $regex: search, $options: 'i' } },\n        { description: { $regex: search, $options: 'i' } }\n      ];\n    }\n\n    const sort: any = {};\n    sort[sortBy === 'year' ? 'startYear' : sortBy] = sortOrder === 'asc' ? 1 : -1;\n\n    const skip = (page - 1) * limit;\n\n    const [data, total] = await Promise.all([\n      Series.find(query)\n        .sort(sort)\n        .skip(skip)\n        .limit(limit)\n        .lean(),\n      Series.countDocuments(query)\n    ]);\n\n    return {\n      data,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    };\n  }\n\n  async getEpisodes(filters: ContentFilters = {}): Promise<PaginatedResponse<IEpisode>> {\n    await connectDB();\n\n    const {\n      genre,\n      quality,\n      sortBy = 'createdAt',\n      sortOrder = 'desc',\n      page = 1,\n      limit = 20,\n      search\n    } = filters;\n\n\n\n    const query: any = {};\n\n    if (genre) query.genres = { $in: [genre] };\n    if (quality) query.quality = quality;\n    if (search) {\n      query.seriesTitle = { $regex: search, $options: 'i' };\n    }\n\n    const sort: any = {};\n    sort[sortBy] = sortOrder === 'asc' ? 1 : -1;\n\n    const skip = (page - 1) * limit;\n\n    // Now that episodes have genres, we can use simple queries!\n    let data, total;\n\n    if (sortBy === 'createdAt' && sortOrder === 'desc') {\n      // For latest episodes, get the latest episode from each series for diversity\n      const aggregationPipeline = [\n        { $match: query },\n        {\n          $sort: {\n            imdbId: 1,      // Group by series\n            season: -1,     // Latest season first\n            episode: -1,    // Latest episode first\n            createdAt: -1   // Latest created first\n          }\n        },\n        {\n          $group: {\n            _id: '$imdbId', // Group by series (imdbId)\n            latestEpisode: { $first: '$$ROOT' } // Get the latest episode from each series\n          }\n        },\n        {\n          $replaceRoot: { newRoot: '$latestEpisode' } // Replace root with the episode document\n        },\n        {\n          $sort: { createdAt: -1 } // Sort series by their latest episode creation date\n        },\n        { $skip: skip },\n        { $limit: limit }\n      ];\n\n      // Use aggregation for latest episodes (for diversity)\n      [data, total] = await Promise.all([\n        Episode.aggregate(aggregationPipeline),\n        Episode.countDocuments(query)\n      ]);\n    } else {\n      // Use simple query for other sorting\n      [data, total] = await Promise.all([\n        Episode.find(query).sort(sort).skip(skip).limit(limit),\n        Episode.countDocuments(query)\n      ]);\n    }\n\n    // Add series poster URLs for episodes without posters\n    const enrichedData = await Promise.all(\n      data.map(async (episode: any) => {\n        if ((!episode.posterUrl || episode.posterUrl === '') && episode.imdbId) {\n          const series = await this.getSeriesByImdbId(episode.imdbId);\n          if (series?.posterUrl) {\n            episode.posterUrl = series.posterUrl;\n            episode.seriesPosterUrl = series.posterUrl;\n          }\n        }\n        return episode;\n      })\n    );\n\n    return {\n      data: enrichedData,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    };\n  }\n\n  async getMovieById(id: string): Promise<IMovie | null> {\n    await connectDB();\n    return Movie.findById(id).lean();\n  }\n\n  async getMovieByImdbId(imdbId: string): Promise<IMovie | null> {\n    await connectDB();\n    return Movie.findOne({ imdbId }).lean();\n  }\n\n  async getSeriesById(id: string): Promise<ISeries | null> {\n    await connectDB();\n    return Series.findById(id).lean();\n  }\n\n  async getSeriesByImdbId(imdbId: string): Promise<ISeries | null> {\n    await connectDB();\n    return Series.findOne({ imdbId }).lean();\n  }\n\n  async getSeriesEpisodes(imdbId: string, season?: number): Promise<IEpisode[]> {\n    await connectDB();\n    const query: any = { imdbId };\n    if (season) query.season = season;\n\n    // Use aggregation to join with series data and get poster URLs\n    const existingEpisodes = await Episode.aggregate([\n      { $match: query },\n      { $sort: { season: 1, episode: 1 } },\n      {\n        $lookup: {\n          from: 'series',\n          localField: 'imdbId',\n          foreignField: 'imdbId',\n          as: 'seriesData'\n        }\n      },\n      {\n        $addFields: {\n          seriesPosterUrl: { $arrayElemAt: ['$seriesData.posterUrl', 0] },\n          posterUrl: {\n            $cond: {\n              if: { $ne: ['$posterUrl', ''] },\n              then: '$posterUrl',\n              else: { $arrayElemAt: ['$seriesData.posterUrl', 0] }\n            }\n          }\n        }\n      },\n      {\n        $project: {\n          seriesData: 0 // Remove the joined series data to keep response clean\n        }\n      }\n    ]);\n\n    // Auto-populate missing episodes\n    const populatedEpisodes = await this.autoPopulateMissingEpisodes(imdbId, existingEpisodes, season);\n\n    return populatedEpisodes.sort((a, b) => {\n      if (a.season !== b.season) return a.season - b.season;\n      return a.episode - b.episode;\n    });\n  }\n\n  private async autoPopulateMissingEpisodes(imdbId: string, existingEpisodes: IEpisode[], targetSeason?: number): Promise<IEpisode[]> {\n    const episodeMap = new Map<string, IEpisode>();\n\n    // Add existing episodes to map\n    existingEpisodes.forEach(ep => {\n      episodeMap.set(`${ep.season}-${ep.episode}`, ep);\n    });\n\n    // Get series info for poster\n    const series = await this.getSeriesByImdbId(imdbId);\n    const seriesPosterUrl = series?.posterUrl || '';\n\n    // Group by season and find missing episodes\n    const seasonGroups = new Map<number, number[]>();\n    existingEpisodes.forEach(ep => {\n      if (!seasonGroups.has(ep.season)) {\n        seasonGroups.set(ep.season, []);\n      }\n      seasonGroups.get(ep.season)!.push(ep.episode);\n    });\n\n    // For each season, fill in missing episodes\n    for (const [seasonNum, episodes] of seasonGroups) {\n      if (targetSeason && seasonNum !== targetSeason) continue;\n\n      const maxEpisode = Math.max(...episodes);\n\n      // Create missing episodes from 1 to maxEpisode\n      for (let epNum = 1; epNum <= maxEpisode; epNum++) {\n        const key = `${seasonNum}-${epNum}`;\n        if (!episodeMap.has(key)) {\n          // Create placeholder episode\n          const placeholderEpisode: IEpisode = {\n            _id: new Date().getTime().toString() + Math.random().toString(36),\n            imdbId,\n            season: seasonNum,\n            episode: epNum,\n            episodeTitle: `Episode ${epNum}`,\n            description: `Episode ${epNum} of Season ${seasonNum}`,\n            posterUrl: seriesPosterUrl,\n            seriesPosterUrl: seriesPosterUrl,\n            runtime: '45 min',\n            imdbRating: 0,\n            airDate: new Date().toISOString(),\n            createdAt: new Date(),\n            updatedAt: new Date()\n          };\n\n          episodeMap.set(key, placeholderEpisode);\n        }\n      }\n    }\n\n    return Array.from(episodeMap.values());\n  }\n\n  async processImdbId(imdbId: string): Promise<{ success: boolean; type?: string; error?: string }> {\n    try {\n      await connectDB();\n\n      // Check if already exists\n      const existingMovie = await Movie.findOne({ imdbId });\n      const existingSeries = await Series.findOne({ imdbId });\n      \n      if (existingMovie || existingSeries) {\n        return { success: true, type: existingMovie ? 'movie' : 'series' };\n      }\n\n      // Try to scrape as movie first\n      try {\n        const movieData = await this.scraper.scrapeMovie(imdbId);\n        const embedUrl = this.vidsrc.generateMovieEmbedUrl(imdbId);\n        \n        await Movie.create({\n          imdbId,\n          ...movieData,\n          embedUrl\n        });\n        \n        return { success: true, type: 'movie' };\n      } catch (movieError) {\n        // If movie scraping fails, try as series\n        try {\n          const seriesData = await this.scraper.scrapeSeries(imdbId);\n          const embedUrl = this.vidsrc.generateSeriesEmbedUrl(imdbId);\n          \n          await Series.create({\n            imdbId,\n            ...seriesData,\n            embedUrl\n          });\n          \n          return { success: true, type: 'series' };\n        } catch (seriesError) {\n          throw new Error(`Failed to scrape as both movie and series: ${movieError.message}, ${seriesError.message}`);\n        }\n      }\n    } catch (error) {\n      console.error(`Error processing IMDb ID ${imdbId}:`, error);\n      return { success: false, error: error.message };\n    }\n  }\n\n  async createBulkRequest(imdbIds: string[], submittedBy?: string, contentType: 'auto' | 'movie' | 'series' = 'auto'): Promise<IRequest> {\n    await connectDB();\n\n    const request = await Request.create({\n      imdbIds,\n      totalCount: imdbIds.length,\n      submittedBy,\n      contentType\n    });\n\n    // Process in background (don't await)\n    this.processBulkRequest(request._id.toString()).catch(console.error);\n\n    return request;\n  }\n\n  private async processBulkRequest(requestId: string): Promise<void> {\n    await connectDB();\n    \n    const request = await Request.findById(requestId);\n    if (!request) return;\n\n    await Request.findByIdAndUpdate(requestId, { status: 'processing' });\n\n    let processedCount = 0;\n    const failedIds: string[] = [];\n    const errorMessages: string[] = [];\n\n    for (const imdbId of request.imdbIds) {\n      try {\n        const result = await this.processImdbId(imdbId);\n        if (result.success) {\n          processedCount++;\n        } else {\n          failedIds.push(imdbId);\n          errorMessages.push(result.error || 'Unknown error');\n        }\n      } catch (error) {\n        failedIds.push(imdbId);\n        errorMessages.push(error.message);\n      }\n\n      // Update progress\n      await Request.findByIdAndUpdate(requestId, {\n        processedCount,\n        failedIds,\n        errorMessages\n      });\n\n      // Rate limiting\n      await new Promise(resolve => setTimeout(resolve, 2000));\n    }\n\n    // Mark as completed\n    await Request.findByIdAndUpdate(requestId, {\n      status: processedCount === request.imdbIds.length ? 'completed' : 'failed',\n      completedAt: new Date()\n    });\n  }\n\n  async getRequestStatus(requestId: string): Promise<IRequest | null> {\n    await connectDB();\n    return Request.findById(requestId).lean();\n  }\n\n  async syncLatestContent(): Promise<{ success: boolean; counts: { movies: number; series: number; episodes: number } }> {\n    try {\n      const { movies, series, episodes } = await this.vidsrc.syncLatestContent();\n      \n      let movieCount = 0;\n      let seriesCount = 0;\n      let episodeCount = 0;\n\n      // Process movies\n      for (const movie of movies) {\n        const result = await this.processImdbId(movie.imdb_id);\n        if (result.success && result.type === 'movie') movieCount++;\n      }\n\n      // Process series\n      for (const show of series) {\n        const result = await this.processImdbId(show.imdb_id);\n        if (result.success && result.type === 'series') seriesCount++;\n      }\n\n      // Process episodes\n      await connectDB();\n      for (const episode of episodes) {\n        try {\n          const embedUrl = this.vidsrc.generateEpisodeEmbedUrl(\n            episode.imdb_id,\n            parseInt(episode.season),\n            parseInt(episode.episode)\n          );\n\n          // Get genres from series for new episodes\n          const genres = await this.populateEpisodeGenres(episode.imdb_id);\n\n          await Episode.findOneAndUpdate(\n            {\n              imdbId: episode.imdb_id,\n              season: parseInt(episode.season),\n              episode: parseInt(episode.episode)\n            },\n            {\n              imdbId: episode.imdb_id,\n              seriesTitle: episode.show_title,\n              season: parseInt(episode.season),\n              episode: parseInt(episode.episode),\n              embedUrl,\n              quality: episode.quality,\n              genres: genres // Populate genres from series\n            },\n            { upsert: true }\n          );\n\n          episodeCount++;\n        } catch (error) {\n          console.error(`Error processing episode ${episode.imdb_id} S${episode.season}E${episode.episode}:`, error);\n        }\n      }\n\n      return {\n        success: true,\n        counts: { movies: movieCount, series: seriesCount, episodes: episodeCount }\n      };\n    } catch (error) {\n      console.error('Error syncing latest content:', error);\n      return {\n        success: false,\n        counts: { movies: 0, series: 0, episodes: 0 }\n      };\n    }\n  }\n\n  async addEpisode(episodeData: Partial<IEpisode>): Promise<IEpisode> {\n    await connectDB();\n\n    // Check if series exists, if not create it\n    if (episodeData.imdbId) {\n      await this.ensureSeriesExists(episodeData.imdbId, episodeData);\n\n      // Populate genres from series if not already provided\n      if (!episodeData.genres) {\n        episodeData.genres = await this.populateEpisodeGenres(episodeData.imdbId);\n      }\n    }\n\n    const episode = new Episode(episodeData);\n    return episode.save();\n  }\n\n  private async ensureSeriesExists(imdbId: string, episodeData: Partial<IEpisode>): Promise<void> {\n    const existingSeries = await this.getSeriesByImdbId(imdbId);\n\n    if (!existingSeries) {\n      // Create series from episode data\n      const seriesData: Partial<ISeries> = {\n        imdbId,\n        title: episodeData.seriesTitle || `Series ${imdbId}`,\n        description: episodeData.description || `Series for ${episodeData.seriesTitle || imdbId}`,\n        posterUrl: episodeData.seriesPosterUrl || episodeData.posterUrl || '',\n        backdropUrl: episodeData.seriesPosterUrl || episodeData.posterUrl || '',\n        releaseDate: episodeData.airDate || new Date().toISOString(),\n        genres: ['Drama'], // Default genre\n        imdbRating: episodeData.imdbRating || 0,\n        runtime: episodeData.runtime || '45 min',\n        status: 'Ongoing',\n        totalSeasons: episodeData.season || 1,\n        totalEpisodes: 1,\n        language: 'English',\n        country: 'US',\n        network: 'Unknown',\n        createdAt: new Date(),\n        updatedAt: new Date()\n      };\n\n      const series = new Series(seriesData);\n      await series.save();\n      console.log(`Auto-created series: ${seriesData.title} (${imdbId})`);\n    }\n  }\n\n  async getMovieFilterOptions(): Promise<FilterOptions> {\n    await connectDB();\n\n    // Get genre counts using aggregation\n    const genreCounts = await Movie.aggregate([\n      { $unwind: '$genres' },\n      { $group: { _id: '$genres', count: { $sum: 1 } } },\n      { $sort: { count: -1 } },\n      { $project: { genre: '$_id', count: 1, _id: 0 } }\n    ]);\n\n    // Get language counts\n    const languageCounts = await Movie.aggregate([\n      { $match: { language: { $exists: true, $ne: null, $ne: '' } } },\n      { $group: { _id: '$language', count: { $sum: 1 } } },\n      { $sort: { count: -1 } },\n      { $project: { language: '$_id', count: 1, _id: 0 } }\n    ]);\n\n    // Get country counts\n    const countryCounts = await Movie.aggregate([\n      { $match: { country: { $exists: true, $ne: null, $ne: '' } } },\n      { $group: { _id: '$country', count: { $sum: 1 } } },\n      { $sort: { count: -1 } },\n      { $project: { country: '$_id', count: 1, _id: 0 } }\n    ]);\n\n    // Get unique years\n    const years = await Movie.distinct('year', { year: { $exists: true, $ne: null } });\n\n    // Get unique ratings\n    const ratings = await Movie.distinct('rating', { rating: { $exists: true, $ne: null } });\n\n    // Get unique qualities\n    const qualities = await Movie.distinct('quality', { quality: { $exists: true, $ne: null } });\n\n    return {\n      genres: genreCounts,\n      languages: languageCounts,\n      countries: countryCounts,\n      years: years.filter(Boolean).sort((a, b) => b - a),\n      ratings: ratings.filter(Boolean).sort(),\n      qualities: qualities.filter(Boolean).sort()\n    };\n  }\n\n  async getSeriesFilterOptions(): Promise<FilterOptions> {\n    await connectDB();\n\n    // Get genre counts using aggregation\n    const genreCounts = await Series.aggregate([\n      { $unwind: '$genres' },\n      { $group: { _id: '$genres', count: { $sum: 1 } } },\n      { $sort: { count: -1 } },\n      { $project: { genre: '$_id', count: 1, _id: 0 } }\n    ]);\n\n    // Get language counts\n    const languageCounts = await Series.aggregate([\n      { $match: { language: { $exists: true, $ne: null, $ne: '' } } },\n      { $group: { _id: '$language', count: { $sum: 1 } } },\n      { $sort: { count: -1 } },\n      { $project: { language: '$_id', count: 1, _id: 0 } }\n    ]);\n\n    // Get country counts\n    const countryCounts = await Series.aggregate([\n      { $match: { country: { $exists: true, $ne: null, $ne: '' } } },\n      { $group: { _id: '$country', count: { $sum: 1 } } },\n      { $sort: { count: -1 } },\n      { $project: { country: '$_id', count: 1, _id: 0 } }\n    ]);\n\n    // Get unique years (using startYear for series)\n    const years = await Series.distinct('startYear', { startYear: { $exists: true, $ne: null } });\n\n    // Get unique ratings\n    const ratings = await Series.distinct('rating', { rating: { $exists: true, $ne: null } });\n\n    return {\n      genres: genreCounts,\n      languages: languageCounts,\n      countries: countryCounts,\n      years: years.filter(Boolean).sort((a, b) => b - a),\n      ratings: ratings.filter(Boolean).sort(),\n      qualities: [] // Series don't have quality field\n    };\n  }\n\n  async getEpisodeFilterOptions(): Promise<FilterOptions> {\n    await connectDB();\n\n    // Now episodes have their own genres field - much simpler!\n    const genreCounts = await Episode.aggregate([\n      { $unwind: '$genres' },\n      { $group: { _id: '$genres', count: { $sum: 1 } } },\n      { $sort: { count: -1 } },\n      { $project: { genre: '$_id', count: 1, _id: 0 } }\n    ]);\n\n    // Get unique qualities from episodes\n    const qualities = await Episode.distinct('quality', { quality: { $exists: true, $ne: null } });\n\n    return {\n      genres: genreCounts,\n      languages: [], // Episodes don't have language filtering\n      countries: [], // Episodes don't have country filtering\n      years: [], // Episodes don't have year filtering\n      ratings: [], // Episodes don't have rating filtering\n      qualities: qualities.filter(Boolean).sort()\n    };\n  }\n\n  /**\n   * Helper function to populate episode genres from its series\n   * This should be called whenever a new episode is created\n   */\n  async populateEpisodeGenres(imdbId: string): Promise<string[]> {\n    await connectDB();\n\n    try {\n      const series = await Series.findOne({ imdbId }, { genres: 1 });\n      return series?.genres || [];\n    } catch (error) {\n      console.error(`Error fetching genres for series ${imdbId}:`, error);\n      return [];\n    }\n  }\n\n  /**\n   * Utility function to update existing episodes without genres\n   * This can be run manually if needed to fix episodes that were created before genre population\n   */\n  async updateEpisodesWithoutGenres(): Promise<{ updated: number; errors: number }> {\n    await connectDB();\n\n    let updated = 0;\n    let errors = 0;\n\n    try {\n      // Find episodes without genres\n      const episodesWithoutGenres = await Episode.find(\n        { $or: [{ genres: { $exists: false } }, { genres: { $size: 0 } }] },\n        { imdbId: 1, seriesTitle: 1, season: 1, episode: 1 }\n      ).limit(1000); // Process in batches\n\n      console.log(`🔄 Found ${episodesWithoutGenres.length} episodes without genres`);\n\n      // Group by imdbId to minimize series lookups\n      const episodesByImdb = episodesWithoutGenres.reduce((acc, ep) => {\n        if (!acc[ep.imdbId]) acc[ep.imdbId] = [];\n        acc[ep.imdbId].push(ep);\n        return acc;\n      }, {} as Record<string, any[]>);\n\n      // Update episodes by series\n      for (const [imdbId, episodes] of Object.entries(episodesByImdb)) {\n        try {\n          const genres = await this.populateEpisodeGenres(imdbId);\n\n          if (genres.length > 0) {\n            const episodeIds = episodes.map(ep => ep._id);\n            const result = await Episode.updateMany(\n              { _id: { $in: episodeIds } },\n              { $set: { genres: genres } }\n            );\n\n            updated += result.modifiedCount;\n            console.log(`✅ Updated ${result.modifiedCount} episodes for series ${imdbId} with genres: ${genres.join(', ')}`);\n          }\n        } catch (error) {\n          console.error(`❌ Error updating episodes for series ${imdbId}:`, error);\n          errors++;\n        }\n      }\n\n      console.log(`🎉 Genre update completed: ${updated} episodes updated, ${errors} errors`);\n\n    } catch (error) {\n      console.error('❌ Error in updateEpisodesWithoutGenres:', error);\n      errors++;\n    }\n\n    return { updated, errors };\n  }\n\n}\n\nexport default ContentService;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAkDA,MAAM;IACJ,OAAe,SAAyB;IAChC,QAAqB;IACrB,OAAkB;IAE1B,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,uHAAA,CAAA,UAAW,CAAC,WAAW;QACtC,IAAI,CAAC,MAAM,GAAG,sHAAA,CAAA,UAAS,CAAC,WAAW;IACrC;IAEA,OAAO,cAA8B;QACnC,IAAI,CAAC,eAAe,QAAQ,EAAE;YAC5B,eAAe,QAAQ,GAAG,IAAI;QAChC;QACA,OAAO,eAAe,QAAQ;IAChC;IAEA,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAsC;QAChF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,MAAM,EACN,OAAO,EACP,SAAS,WAAW,EACpB,YAAY,MAAM,EAClB,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,MAAM,EACP,GAAG;QAEJ,cAAc;QACd,MAAM,QAAa,CAAC;QAEpB,IAAI,OAAO,MAAM,MAAM,GAAG;YAAE,KAAK;gBAAC;aAAM;QAAC;QACzC,IAAI,MAAM,MAAM,IAAI,GAAG;QACvB,IAAI,UAAU,MAAM,QAAQ,GAAG;QAC/B,IAAI,SAAS,MAAM,OAAO,GAAG;QAC7B,IAAI,QAAQ,MAAM,MAAM,GAAG;QAC3B,IAAI,SAAS,MAAM,OAAO,GAAG;QAC7B,IAAI,QAAQ;YACV,4EAA4E;YAC5E,MAAM,GAAG,GAAG;gBACV;oBAAE,OAAO;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC3C;oBAAE,aAAa;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;aAClD;QACH;QAEA,aAAa;QACb,MAAM,OAAY,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,IAAI,CAAC;QAE1C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACtC,wHAAA,CAAA,UAAK,CAAC,IAAI,CAAC,OACR,IAAI,CAAC,MACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;YACP,wHAAA,CAAA,UAAK,CAAC,cAAc,CAAC;SACtB;QAED,OAAO;YACL;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF;IAEA,MAAM,UAAU,UAA0B,CAAC,CAAC,EAAuC;QACjF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EACJ,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,MAAM,EACN,SAAS,WAAW,EACpB,YAAY,MAAM,EAClB,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,MAAM,EACP,GAAG;QAEJ,MAAM,QAAa,CAAC;QAEpB,IAAI,OAAO,MAAM,MAAM,GAAG;YAAE,KAAK;gBAAC;aAAM;QAAC;QACzC,IAAI,MAAM,MAAM,SAAS,GAAG;QAC5B,IAAI,UAAU,MAAM,QAAQ,GAAG;QAC/B,IAAI,SAAS,MAAM,OAAO,GAAG;QAC7B,IAAI,QAAQ,MAAM,MAAM,GAAG;QAC3B,IAAI,QAAQ;YACV,4EAA4E;YAC5E,MAAM,GAAG,GAAG;gBACV;oBAAE,OAAO;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;gBAC3C;oBAAE,aAAa;wBAAE,QAAQ;wBAAQ,UAAU;oBAAI;gBAAE;aAClD;QACH;QAEA,MAAM,OAAY,CAAC;QACnB,IAAI,CAAC,WAAW,SAAS,cAAc,OAAO,GAAG,cAAc,QAAQ,IAAI,CAAC;QAE5E,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,CAAC,MAAM,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACtC,yHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,OACT,IAAI,CAAC,MACL,IAAI,CAAC,MACL,KAAK,CAAC,OACN,IAAI;YACP,yHAAA,CAAA,UAAM,CAAC,cAAc,CAAC;SACvB;QAED,OAAO;YACL;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF;IAEA,MAAM,YAAY,UAA0B,CAAC,CAAC,EAAwC;QACpF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,EACJ,KAAK,EACL,OAAO,EACP,SAAS,WAAW,EACpB,YAAY,MAAM,EAClB,OAAO,CAAC,EACR,QAAQ,EAAE,EACV,MAAM,EACP,GAAG;QAIJ,MAAM,QAAa,CAAC;QAEpB,IAAI,OAAO,MAAM,MAAM,GAAG;YAAE,KAAK;gBAAC;aAAM;QAAC;QACzC,IAAI,SAAS,MAAM,OAAO,GAAG;QAC7B,IAAI,QAAQ;YACV,MAAM,WAAW,GAAG;gBAAE,QAAQ;gBAAQ,UAAU;YAAI;QACtD;QAEA,MAAM,OAAY,CAAC;QACnB,IAAI,CAAC,OAAO,GAAG,cAAc,QAAQ,IAAI,CAAC;QAE1C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,4DAA4D;QAC5D,IAAI,MAAM;QAEV,IAAI,WAAW,eAAe,cAAc,QAAQ;YAClD,6EAA6E;YAC7E,MAAM,sBAAsB;gBAC1B;oBAAE,QAAQ;gBAAM;gBAChB;oBACE,OAAO;wBACL,QAAQ;wBACR,QAAQ,CAAC;wBACT,SAAS,CAAC;wBACV,WAAW,CAAC,EAAI,uBAAuB;oBACzC;gBACF;gBACA;oBACE,QAAQ;wBACN,KAAK;wBACL,eAAe;4BAAE,QAAQ;wBAAS,EAAE,0CAA0C;oBAChF;gBACF;gBACA;oBACE,cAAc;wBAAE,SAAS;oBAAiB,EAAE,yCAAyC;gBACvF;gBACA;oBACE,OAAO;wBAAE,WAAW,CAAC;oBAAE,EAAE,oDAAoD;gBAC/E;gBACA;oBAAE,OAAO;gBAAK;gBACd;oBAAE,QAAQ;gBAAM;aACjB;YAED,sDAAsD;YACtD,CAAC,MAAM,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChC,0HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;gBAClB,0HAAA,CAAA,UAAO,CAAC,cAAc,CAAC;aACxB;QACH,OAAO;YACL,qCAAqC;YACrC,CAAC,MAAM,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAChC,0HAAA,CAAA,UAAO,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC;gBAChD,0HAAA,CAAA,UAAO,CAAC,cAAc,CAAC;aACxB;QACH;QAEA,sDAAsD;QACtD,MAAM,eAAe,MAAM,QAAQ,GAAG,CACpC,KAAK,GAAG,CAAC,OAAO;YACd,IAAI,CAAC,CAAC,QAAQ,SAAS,IAAI,QAAQ,SAAS,KAAK,EAAE,KAAK,QAAQ,MAAM,EAAE;gBACtE,MAAM,SAAS,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,MAAM;gBAC1D,IAAI,QAAQ,WAAW;oBACrB,QAAQ,SAAS,GAAG,OAAO,SAAS;oBACpC,QAAQ,eAAe,GAAG,OAAO,SAAS;gBAC5C;YACF;YACA,OAAO;QACT;QAGF,OAAO;YACL,MAAM;YACN,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF;IAEA,MAAM,aAAa,EAAU,EAA0B;QACrD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,OAAO,wHAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,IAAI,IAAI;IAChC;IAEA,MAAM,iBAAiB,MAAc,EAA0B;QAC7D,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,OAAO,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;YAAE;QAAO,GAAG,IAAI;IACvC;IAEA,MAAM,cAAc,EAAU,EAA2B;QACvD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,OAAO,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,IAAI,IAAI;IACjC;IAEA,MAAM,kBAAkB,MAAc,EAA2B;QAC/D,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,OAAO,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;YAAE;QAAO,GAAG,IAAI;IACxC;IAEA,MAAM,kBAAkB,MAAc,EAAE,MAAe,EAAuB;QAC5E,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,MAAM,QAAa;YAAE;QAAO;QAC5B,IAAI,QAAQ,MAAM,MAAM,GAAG;QAE3B,+DAA+D;QAC/D,MAAM,mBAAmB,MAAM,0HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;YAC/C;gBAAE,QAAQ;YAAM;YAChB;gBAAE,OAAO;oBAAE,QAAQ;oBAAG,SAAS;gBAAE;YAAE;YACnC;gBACE,SAAS;oBACP,MAAM;oBACN,YAAY;oBACZ,cAAc;oBACd,IAAI;gBACN;YACF;YACA;gBACE,YAAY;oBACV,iBAAiB;wBAAE,cAAc;4BAAC;4BAAyB;yBAAE;oBAAC;oBAC9D,WAAW;wBACT,OAAO;4BACL,IAAI;gCAAE,KAAK;oCAAC;oCAAc;iCAAG;4BAAC;4BAC9B,MAAM;4BACN,MAAM;gCAAE,cAAc;oCAAC;oCAAyB;iCAAE;4BAAC;wBACrD;oBACF;gBACF;YACF;YACA;gBACE,UAAU;oBACR,YAAY,EAAE,uDAAuD;gBACvE;YACF;SACD;QAED,iCAAiC;QACjC,MAAM,oBAAoB,MAAM,IAAI,CAAC,2BAA2B,CAAC,QAAQ,kBAAkB;QAE3F,OAAO,kBAAkB,IAAI,CAAC,CAAC,GAAG;YAChC,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;YACrD,OAAO,EAAE,OAAO,GAAG,EAAE,OAAO;QAC9B;IACF;IAEA,MAAc,4BAA4B,MAAc,EAAE,gBAA4B,EAAE,YAAqB,EAAuB;QAClI,MAAM,aAAa,IAAI;QAEvB,+BAA+B;QAC/B,iBAAiB,OAAO,CAAC,CAAA;YACvB,WAAW,GAAG,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE,GAAG,OAAO,EAAE,EAAE;QAC/C;QAEA,6BAA6B;QAC7B,MAAM,SAAS,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAC5C,MAAM,kBAAkB,QAAQ,aAAa;QAE7C,4CAA4C;QAC5C,MAAM,eAAe,IAAI;QACzB,iBAAiB,OAAO,CAAC,CAAA;YACvB,IAAI,CAAC,aAAa,GAAG,CAAC,GAAG,MAAM,GAAG;gBAChC,aAAa,GAAG,CAAC,GAAG,MAAM,EAAE,EAAE;YAChC;YACA,aAAa,GAAG,CAAC,GAAG,MAAM,EAAG,IAAI,CAAC,GAAG,OAAO;QAC9C;QAEA,4CAA4C;QAC5C,KAAK,MAAM,CAAC,WAAW,SAAS,IAAI,aAAc;YAChD,IAAI,gBAAgB,cAAc,cAAc;YAEhD,MAAM,aAAa,KAAK,GAAG,IAAI;YAE/B,+CAA+C;YAC/C,IAAK,IAAI,QAAQ,GAAG,SAAS,YAAY,QAAS;gBAChD,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,OAAO;gBACnC,IAAI,CAAC,WAAW,GAAG,CAAC,MAAM;oBACxB,6BAA6B;oBAC7B,MAAM,qBAA+B;wBACnC,KAAK,IAAI,OAAO,OAAO,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC;wBAC9D;wBACA,QAAQ;wBACR,SAAS;wBACT,cAAc,CAAC,QAAQ,EAAE,OAAO;wBAChC,aAAa,CAAC,QAAQ,EAAE,MAAM,WAAW,EAAE,WAAW;wBACtD,WAAW;wBACX,iBAAiB;wBACjB,SAAS;wBACT,YAAY;wBACZ,SAAS,IAAI,OAAO,WAAW;wBAC/B,WAAW,IAAI;wBACf,WAAW,IAAI;oBACjB;oBAEA,WAAW,GAAG,CAAC,KAAK;gBACtB;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,WAAW,MAAM;IACrC;IAEA,MAAM,cAAc,MAAc,EAAgE;QAChG,IAAI;YACF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YAEd,0BAA0B;YAC1B,MAAM,gBAAgB,MAAM,wHAAA,CAAA,UAAK,CAAC,OAAO,CAAC;gBAAE;YAAO;YACnD,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;gBAAE;YAAO;YAErD,IAAI,iBAAiB,gBAAgB;gBACnC,OAAO;oBAAE,SAAS;oBAAM,MAAM,gBAAgB,UAAU;gBAAS;YACnE;YAEA,+BAA+B;YAC/B,IAAI;gBACF,MAAM,YAAY,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;gBACjD,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC;gBAEnD,MAAM,wHAAA,CAAA,UAAK,CAAC,MAAM,CAAC;oBACjB;oBACA,GAAG,SAAS;oBACZ;gBACF;gBAEA,OAAO;oBAAE,SAAS;oBAAM,MAAM;gBAAQ;YACxC,EAAE,OAAO,YAAY;gBACnB,yCAAyC;gBACzC,IAAI;oBACF,MAAM,aAAa,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;oBACnD,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC;oBAEpD,MAAM,yHAAA,CAAA,UAAM,CAAC,MAAM,CAAC;wBAClB;wBACA,GAAG,UAAU;wBACb;oBACF;oBAEA,OAAO;wBAAE,SAAS;wBAAM,MAAM;oBAAS;gBACzC,EAAE,OAAO,aAAa;oBACpB,MAAM,IAAI,MAAM,CAAC,2CAA2C,EAAE,WAAW,OAAO,CAAC,EAAE,EAAE,YAAY,OAAO,EAAE;gBAC5G;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAC,EAAE;YACrD,OAAO;gBAAE,SAAS;gBAAO,OAAO,MAAM,OAAO;YAAC;QAChD;IACF;IAEA,MAAM,kBAAkB,OAAiB,EAAE,WAAoB,EAAE,cAA2C,MAAM,EAAqB;QACrI,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,UAAU,MAAM,0HAAA,CAAA,UAAO,CAAC,MAAM,CAAC;YACnC;YACA,YAAY,QAAQ,MAAM;YAC1B;YACA;QACF;QAEA,sCAAsC;QACtC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,KAAK;QAEnE,OAAO;IACT;IAEA,MAAc,mBAAmB,SAAiB,EAAiB;QACjE,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,MAAM,UAAU,MAAM,0HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC;QACvC,IAAI,CAAC,SAAS;QAEd,MAAM,0HAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,WAAW;YAAE,QAAQ;QAAa;QAElE,IAAI,iBAAiB;QACrB,MAAM,YAAsB,EAAE;QAC9B,MAAM,gBAA0B,EAAE;QAElC,KAAK,MAAM,UAAU,QAAQ,OAAO,CAAE;YACpC,IAAI;gBACF,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC;gBACxC,IAAI,OAAO,OAAO,EAAE;oBAClB;gBACF,OAAO;oBACL,UAAU,IAAI,CAAC;oBACf,cAAc,IAAI,CAAC,OAAO,KAAK,IAAI;gBACrC;YACF,EAAE,OAAO,OAAO;gBACd,UAAU,IAAI,CAAC;gBACf,cAAc,IAAI,CAAC,MAAM,OAAO;YAClC;YAEA,kBAAkB;YAClB,MAAM,0HAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,WAAW;gBACzC;gBACA;gBACA;YACF;YAEA,gBAAgB;YAChB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QACnD;QAEA,oBAAoB;QACpB,MAAM,0HAAA,CAAA,UAAO,CAAC,iBAAiB,CAAC,WAAW;YACzC,QAAQ,mBAAmB,QAAQ,OAAO,CAAC,MAAM,GAAG,cAAc;YAClE,aAAa,IAAI;QACnB;IACF;IAEA,MAAM,iBAAiB,SAAiB,EAA4B;QAClE,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QACd,OAAO,0HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,WAAW,IAAI;IACzC;IAEA,MAAM,oBAAiH;QACrH,IAAI;YACF,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,iBAAiB;YAExE,IAAI,aAAa;YACjB,IAAI,cAAc;YAClB,IAAI,eAAe;YAEnB,iBAAiB;YACjB,KAAK,MAAM,SAAS,OAAQ;gBAC1B,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,OAAO;gBACrD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,KAAK,SAAS;YACjD;YAEA,iBAAiB;YACjB,KAAK,MAAM,QAAQ,OAAQ;gBACzB,MAAM,SAAS,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,OAAO;gBACpD,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,KAAK,UAAU;YAClD;YAEA,mBAAmB;YACnB,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;YACd,KAAK,MAAM,WAAW,SAAU;gBAC9B,IAAI;oBACF,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,uBAAuB,CAClD,QAAQ,OAAO,EACf,SAAS,QAAQ,MAAM,GACvB,SAAS,QAAQ,OAAO;oBAG1B,0CAA0C;oBAC1C,MAAM,SAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,OAAO;oBAE/D,MAAM,0HAAA,CAAA,UAAO,CAAC,gBAAgB,CAC5B;wBACE,QAAQ,QAAQ,OAAO;wBACvB,QAAQ,SAAS,QAAQ,MAAM;wBAC/B,SAAS,SAAS,QAAQ,OAAO;oBACnC,GACA;wBACE,QAAQ,QAAQ,OAAO;wBACvB,aAAa,QAAQ,UAAU;wBAC/B,QAAQ,SAAS,QAAQ,MAAM;wBAC/B,SAAS,SAAS,QAAQ,OAAO;wBACjC;wBACA,SAAS,QAAQ,OAAO;wBACxB,QAAQ,OAAO,8BAA8B;oBAC/C,GACA;wBAAE,QAAQ;oBAAK;oBAGjB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,yBAAyB,EAAE,QAAQ,OAAO,CAAC,EAAE,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,CAAC,CAAC,EAAE;gBACtG;YACF;YAEA,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAE,QAAQ;oBAAY,QAAQ;oBAAa,UAAU;gBAAa;YAC5E;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAE,QAAQ;oBAAG,QAAQ;oBAAG,UAAU;gBAAE;YAC9C;QACF;IACF;IAEA,MAAM,WAAW,WAA8B,EAAqB;QAClE,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,2CAA2C;QAC3C,IAAI,YAAY,MAAM,EAAE;YACtB,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,MAAM,EAAE;YAElD,sDAAsD;YACtD,IAAI,CAAC,YAAY,MAAM,EAAE;gBACvB,YAAY,MAAM,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,YAAY,MAAM;YAC1E;QACF;QAEA,MAAM,UAAU,IAAI,0HAAA,CAAA,UAAO,CAAC;QAC5B,OAAO,QAAQ,IAAI;IACrB;IAEA,MAAc,mBAAmB,MAAc,EAAE,WAA8B,EAAiB;QAC9F,MAAM,iBAAiB,MAAM,IAAI,CAAC,iBAAiB,CAAC;QAEpD,IAAI,CAAC,gBAAgB;YACnB,kCAAkC;YAClC,MAAM,aAA+B;gBACnC;gBACA,OAAO,YAAY,WAAW,IAAI,CAAC,OAAO,EAAE,QAAQ;gBACpD,aAAa,YAAY,WAAW,IAAI,CAAC,WAAW,EAAE,YAAY,WAAW,IAAI,QAAQ;gBACzF,WAAW,YAAY,eAAe,IAAI,YAAY,SAAS,IAAI;gBACnE,aAAa,YAAY,eAAe,IAAI,YAAY,SAAS,IAAI;gBACrE,aAAa,YAAY,OAAO,IAAI,IAAI,OAAO,WAAW;gBAC1D,QAAQ;oBAAC;iBAAQ;gBACjB,YAAY,YAAY,UAAU,IAAI;gBACtC,SAAS,YAAY,OAAO,IAAI;gBAChC,QAAQ;gBACR,cAAc,YAAY,MAAM,IAAI;gBACpC,eAAe;gBACf,UAAU;gBACV,SAAS;gBACT,SAAS;gBACT,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YAEA,MAAM,SAAS,IAAI,yHAAA,CAAA,UAAM,CAAC;YAC1B,MAAM,OAAO,IAAI;YACjB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,WAAW,KAAK,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QACpE;IACF;IAEA,MAAM,wBAAgD;QACpD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,qCAAqC;QACrC,MAAM,cAAc,MAAM,wHAAA,CAAA,UAAK,CAAC,SAAS,CAAC;YACxC;gBAAE,SAAS;YAAU;YACrB;gBAAE,QAAQ;oBAAE,KAAK;oBAAW,OAAO;wBAAE,MAAM;oBAAE;gBAAE;YAAE;YACjD;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;YACvB;gBAAE,UAAU;oBAAE,OAAO;oBAAQ,OAAO;oBAAG,KAAK;gBAAE;YAAE;SACjD;QAED,sBAAsB;QACtB,MAAM,iBAAiB,MAAM,wHAAA,CAAA,UAAK,CAAC,SAAS,CAAC;YAC3C;gBAAE,QAAQ;oBAAE,UAAU;wBAAE,SAAS;wBAAM,KAAK;wBAAM,KAAK;oBAAG;gBAAE;YAAE;YAC9D;gBAAE,QAAQ;oBAAE,KAAK;oBAAa,OAAO;wBAAE,MAAM;oBAAE;gBAAE;YAAE;YACnD;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;YACvB;gBAAE,UAAU;oBAAE,UAAU;oBAAQ,OAAO;oBAAG,KAAK;gBAAE;YAAE;SACpD;QAED,qBAAqB;QACrB,MAAM,gBAAgB,MAAM,wHAAA,CAAA,UAAK,CAAC,SAAS,CAAC;YAC1C;gBAAE,QAAQ;oBAAE,SAAS;wBAAE,SAAS;wBAAM,KAAK;wBAAM,KAAK;oBAAG;gBAAE;YAAE;YAC7D;gBAAE,QAAQ;oBAAE,KAAK;oBAAY,OAAO;wBAAE,MAAM;oBAAE;gBAAE;YAAE;YAClD;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;YACvB;gBAAE,UAAU;oBAAE,SAAS;oBAAQ,OAAO;oBAAG,KAAK;gBAAE;YAAE;SACnD;QAED,mBAAmB;QACnB,MAAM,QAAQ,MAAM,wHAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,QAAQ;YAAE,MAAM;gBAAE,SAAS;gBAAM,KAAK;YAAK;QAAE;QAEhF,qBAAqB;QACrB,MAAM,UAAU,MAAM,wHAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,UAAU;YAAE,QAAQ;gBAAE,SAAS;gBAAM,KAAK;YAAK;QAAE;QAEtF,uBAAuB;QACvB,MAAM,YAAY,MAAM,wHAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,WAAW;YAAE,SAAS;gBAAE,SAAS;gBAAM,KAAK;YAAK;QAAE;QAE1F,OAAO;YACL,QAAQ;YACR,WAAW;YACX,WAAW;YACX,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YAChD,SAAS,QAAQ,MAAM,CAAC,SAAS,IAAI;YACrC,WAAW,UAAU,MAAM,CAAC,SAAS,IAAI;QAC3C;IACF;IAEA,MAAM,yBAAiD;QACrD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,qCAAqC;QACrC,MAAM,cAAc,MAAM,yHAAA,CAAA,UAAM,CAAC,SAAS,CAAC;YACzC;gBAAE,SAAS;YAAU;YACrB;gBAAE,QAAQ;oBAAE,KAAK;oBAAW,OAAO;wBAAE,MAAM;oBAAE;gBAAE;YAAE;YACjD;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;YACvB;gBAAE,UAAU;oBAAE,OAAO;oBAAQ,OAAO;oBAAG,KAAK;gBAAE;YAAE;SACjD;QAED,sBAAsB;QACtB,MAAM,iBAAiB,MAAM,yHAAA,CAAA,UAAM,CAAC,SAAS,CAAC;YAC5C;gBAAE,QAAQ;oBAAE,UAAU;wBAAE,SAAS;wBAAM,KAAK;wBAAM,KAAK;oBAAG;gBAAE;YAAE;YAC9D;gBAAE,QAAQ;oBAAE,KAAK;oBAAa,OAAO;wBAAE,MAAM;oBAAE;gBAAE;YAAE;YACnD;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;YACvB;gBAAE,UAAU;oBAAE,UAAU;oBAAQ,OAAO;oBAAG,KAAK;gBAAE;YAAE;SACpD;QAED,qBAAqB;QACrB,MAAM,gBAAgB,MAAM,yHAAA,CAAA,UAAM,CAAC,SAAS,CAAC;YAC3C;gBAAE,QAAQ;oBAAE,SAAS;wBAAE,SAAS;wBAAM,KAAK;wBAAM,KAAK;oBAAG;gBAAE;YAAE;YAC7D;gBAAE,QAAQ;oBAAE,KAAK;oBAAY,OAAO;wBAAE,MAAM;oBAAE;gBAAE;YAAE;YAClD;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;YACvB;gBAAE,UAAU;oBAAE,SAAS;oBAAQ,OAAO;oBAAG,KAAK;gBAAE;YAAE;SACnD;QAED,gDAAgD;QAChD,MAAM,QAAQ,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,aAAa;YAAE,WAAW;gBAAE,SAAS;gBAAM,KAAK;YAAK;QAAE;QAE3F,qBAAqB;QACrB,MAAM,UAAU,MAAM,yHAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,UAAU;YAAE,QAAQ;gBAAE,SAAS;gBAAM,KAAK;YAAK;QAAE;QAEvF,OAAO;YACL,QAAQ;YACR,WAAW;YACX,WAAW;YACX,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YAChD,SAAS,QAAQ,MAAM,CAAC,SAAS,IAAI;YACrC,WAAW,EAAE,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,0BAAkD;QACtD,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,2DAA2D;QAC3D,MAAM,cAAc,MAAM,0HAAA,CAAA,UAAO,CAAC,SAAS,CAAC;YAC1C;gBAAE,SAAS;YAAU;YACrB;gBAAE,QAAQ;oBAAE,KAAK;oBAAW,OAAO;wBAAE,MAAM;oBAAE;gBAAE;YAAE;YACjD;gBAAE,OAAO;oBAAE,OAAO,CAAC;gBAAE;YAAE;YACvB;gBAAE,UAAU;oBAAE,OAAO;oBAAQ,OAAO;oBAAG,KAAK;gBAAE;YAAE;SACjD;QAED,qCAAqC;QACrC,MAAM,YAAY,MAAM,0HAAA,CAAA,UAAO,CAAC,QAAQ,CAAC,WAAW;YAAE,SAAS;gBAAE,SAAS;gBAAM,KAAK;YAAK;QAAE;QAE5F,OAAO;YACL,QAAQ;YACR,WAAW,EAAE;YACb,WAAW,EAAE;YACb,OAAO,EAAE;YACT,SAAS,EAAE;YACX,WAAW,UAAU,MAAM,CAAC,SAAS,IAAI;QAC3C;IACF;IAEA;;;GAGC,GACD,MAAM,sBAAsB,MAAc,EAAqB;QAC7D,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,IAAI;YACF,MAAM,SAAS,MAAM,yHAAA,CAAA,UAAM,CAAC,OAAO,CAAC;gBAAE;YAAO,GAAG;gBAAE,QAAQ;YAAE;YAC5D,OAAO,QAAQ,UAAU,EAAE;QAC7B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,OAAO,CAAC,CAAC,EAAE;YAC7D,OAAO,EAAE;QACX;IACF;IAEA;;;GAGC,GACD,MAAM,8BAA4E;QAChF,MAAM,CAAA,GAAA,uHAAA,CAAA,UAAS,AAAD;QAEd,IAAI,UAAU;QACd,IAAI,SAAS;QAEb,IAAI;YACF,+BAA+B;YAC/B,MAAM,wBAAwB,MAAM,0HAAA,CAAA,UAAO,CAAC,IAAI,CAC9C;gBAAE,KAAK;oBAAC;wBAAE,QAAQ;4BAAE,SAAS;wBAAM;oBAAE;oBAAG;wBAAE,QAAQ;4BAAE,OAAO;wBAAE;oBAAE;iBAAE;YAAC,GAClE;gBAAE,QAAQ;gBAAG,aAAa;gBAAG,QAAQ;gBAAG,SAAS;YAAE,GACnD,KAAK,CAAC,OAAO,qBAAqB;YAEpC,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,sBAAsB,MAAM,CAAC,wBAAwB,CAAC;YAE9E,6CAA6C;YAC7C,MAAM,iBAAiB,sBAAsB,MAAM,CAAC,CAAC,KAAK;gBACxD,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,EAAE;gBACxC,GAAG,CAAC,GAAG,MAAM,CAAC,CAAC,IAAI,CAAC;gBACpB,OAAO;YACT,GAAG,CAAC;YAEJ,4BAA4B;YAC5B,KAAK,MAAM,CAAC,QAAQ,SAAS,IAAI,OAAO,OAAO,CAAC,gBAAiB;gBAC/D,IAAI;oBACF,MAAM,SAAS,MAAM,IAAI,CAAC,qBAAqB,CAAC;oBAEhD,IAAI,OAAO,MAAM,GAAG,GAAG;wBACrB,MAAM,aAAa,SAAS,GAAG,CAAC,CAAA,KAAM,GAAG,GAAG;wBAC5C,MAAM,SAAS,MAAM,0HAAA,CAAA,UAAO,CAAC,UAAU,CACrC;4BAAE,KAAK;gCAAE,KAAK;4BAAW;wBAAE,GAC3B;4BAAE,MAAM;gCAAE,QAAQ;4BAAO;wBAAE;wBAG7B,WAAW,OAAO,aAAa;wBAC/B,QAAQ,GAAG,CAAC,CAAC,UAAU,EAAE,OAAO,aAAa,CAAC,qBAAqB,EAAE,OAAO,cAAc,EAAE,OAAO,IAAI,CAAC,OAAO;oBACjH;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,OAAO,CAAC,CAAC,EAAE;oBACjE;gBACF;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,QAAQ,mBAAmB,EAAE,OAAO,OAAO,CAAC;QAExF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD;QACF;QAEA,OAAO;YAAE;YAAS;QAAO;IAC3B;AAEF;uCAEe", "debugId": null}}, {"offset": {"line": 2548, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/app/api/series/%5Bid%5D/episodes/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport ContentService from '@/lib/contentService';\n\nconst contentService = ContentService.getInstance();\n\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ id: string }> }\n) {\n  try {\n    const { id } = await params;\n    const { searchParams } = new URL(request.url);\n    const season = searchParams.get('season') ? parseInt(searchParams.get('season')!) : undefined;\n    \n    const episodes = await contentService.getSeriesEpisodes(id, season);\n    \n    return NextResponse.json(episodes);\n  } catch (error) {\n    console.error('Error fetching episodes:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch episodes' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,iBAAiB,8HAAA,CAAA,UAAc,CAAC,WAAW;AAE1C,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAAuC;IAE/C,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,MAAM;QACrB,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,YAAY,SAAS,aAAa,GAAG,CAAC,aAAc;QAEpF,MAAM,WAAW,MAAM,eAAe,iBAAiB,CAAC,IAAI;QAE5D,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}