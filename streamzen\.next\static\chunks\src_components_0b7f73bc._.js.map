{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/HeroCarousel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight, Play, Info, Star, Volume2, VolumeX } from 'lucide-react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';\n\ninterface HeroItem {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series';\n}\n\ninterface HeroCarouselProps {\n  items: HeroItem[];\n}\n\nconst HeroCarousel: React.FC<HeroCarouselProps> = ({ items }) => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n  const [isMuted, setIsMuted] = useState(true);\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying || items.length <= 1) return;\n\n    const interval = setInterval(() => {\n      setCurrentIndex((prev) => (prev + 1) % items.length);\n    }, 8000); // Longer duration for premium feel\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying, items.length]);\n\n  // Preload next image\n  useEffect(() => {\n    if (items.length > 1) {\n      const nextIndex = (currentIndex + 1) % items.length;\n      const img = new window.Image();\n      img.src = getImageUrl(items[nextIndex].posterUrl);\n    }\n  }, [currentIndex, items]);\n\n  const goToPrevious = useCallback(() => {\n    setCurrentIndex((prev) => (prev - 1 + items.length) % items.length);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  }, [items.length]);\n\n  const goToNext = useCallback(() => {\n    setCurrentIndex((prev) => (prev + 1) % items.length);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  }, [items.length]);\n\n  const goToSlide = useCallback((index: number) => {\n    setCurrentIndex(index);\n    setIsAutoPlaying(false);\n    setTimeout(() => setIsAutoPlaying(true), 10000);\n  }, []);\n\n  // Keyboard navigation\n  useEffect(() => {\n    const handleKeyPress = (e: KeyboardEvent) => {\n      // Don't handle keyboard shortcuts if user is typing in an input field\n      const target = e.target as HTMLElement;\n      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {\n        return;\n      }\n\n      if (e.key === 'ArrowLeft') goToPrevious();\n      if (e.key === 'ArrowRight') goToNext();\n      if (e.key === ' ') {\n        e.preventDefault();\n        setIsAutoPlaying(prev => !prev);\n      }\n    };\n\n    window.addEventListener('keydown', handleKeyPress);\n    return () => window.removeEventListener('keydown', handleKeyPress);\n  }, [goToPrevious, goToNext]);\n\n  if (!items.length) {\n    return (\n      <div className=\"relative h-[85vh] bg-black flex items-center justify-center\">\n        <div className=\"text-center animate-fade-in\">\n          <div className=\"w-16 h-16 bg-white/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <Play size={24} className=\"text-white/60\" />\n          </div>\n          <p className=\"text-gray-400 text-lg\">No featured content available</p>\n        </div>\n      </div>\n    );\n  }\n\n  const currentItem = items[currentIndex];\n\n  return (\n    <div\n      className=\"relative h-[85vh] overflow-hidden bg-black\"\n      onMouseEnter={() => setIsAutoPlaying(false)}\n      onMouseLeave={() => setIsAutoPlaying(true)}\n    >\n      <AnimatePresence mode=\"wait\">\n        <motion.div\n          key={currentIndex}\n          initial={{ opacity: 0, scale: 1.05 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.95 }}\n          transition={{\n            duration: 1.2,\n            ease: [0.16, 1, 0.3, 1]\n          }}\n          className=\"absolute inset-0\"\n          onAnimationComplete={() => setIsLoaded(true)}\n        >\n          {/* Background Image with Parallax Effect */}\n          <div className=\"absolute inset-0 overflow-hidden\">\n            <motion.div\n              initial={{ scale: 1.1 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 8, ease: \"linear\" }}\n              className=\"w-full h-full\"\n            >\n              <Image\n                src={getImageUrl(currentItem.posterUrl)}\n                alt={currentItem.title}\n                fill\n                className=\"object-cover object-center\"\n                priority\n                sizes=\"100vw\"\n                quality={95}\n              />\n            </motion.div>\n\n            {/* Apple TV + Netflix Style Overlays */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-black/95 via-black/60 to-black/30\" />\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/90 via-transparent to-black/20\" />\n            <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/80\" />\n          </div>\n\n          {/* Premium Content Layout */}\n          <div className=\"relative h-full flex items-center\">\n            <div className=\"max-w-[1920px] mx-auto px-6 lg:px-12 w-full\">\n              <div className=\"max-w-3xl\">\n                <motion.div\n                  initial={{ y: 60, opacity: 0 }}\n                  animate={{ y: 0, opacity: 1 }}\n                  transition={{\n                    delay: 0.4,\n                    duration: 0.8,\n                    ease: [0.16, 1, 0.3, 1]\n                  }}\n                  className=\"space-y-8\"\n                >\n                  {/* StreamZen Type Badge */}\n                  <div className=\"inline-flex items-center px-4 py-2 bg-black/60 backdrop-blur-sm rounded-full border border-red-500/30\">\n                    <div className=\"w-2 h-2 bg-red-500 rounded-full mr-3 animate-pulse\" />\n                    <span className=\"text-white text-sm font-semibold uppercase tracking-wider\">\n                      {currentItem.type === 'movie' ? 'Feature Film' : 'Original Series'}\n                    </span>\n                  </div>\n\n                  {/* Hero Title */}\n                  <div className=\"space-y-4\">\n                    <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-black text-white leading-none tracking-tight\">\n                      {currentItem.title}\n                    </h1>\n\n                    {/* Meta Information */}\n                    <div className=\"flex items-center space-x-6 text-lg\">\n                      {currentItem.year && (\n                        <span className=\"text-gray-300 font-medium\">{currentItem.year}</span>\n                      )}\n                      {currentItem.imdbRating && (\n                        <div className=\"flex items-center space-x-2 glass px-3 py-1 rounded-full\">\n                          <Star size={16} className=\"text-yellow-400 fill-current\" />\n                          <span className=\"text-white font-bold\">{formatRating(currentItem.imdbRating)}</span>\n                        </div>\n                      )}\n                      <div className=\"glass px-3 py-1 rounded-full\">\n                        <span className=\"text-white font-medium\">HD</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Description */}\n                  {currentItem.description && (\n                    <p className=\"text-gray-200 text-xl leading-relaxed max-w-2xl font-light\">\n                      {truncateText(currentItem.description, 180)}\n                    </p>\n                  )}\n\n                  {/* StreamZen Action Buttons */}\n                  <div className=\"flex items-center space-x-4 pt-4\">\n                    <Link\n                      href={`/watch/${currentItem.type}/${currentItem.imdbId}`}\n                      className=\"focus-ring rounded-2xl\"\n                    >\n                      <div className=\"flex items-center space-x-3 bg-red-600 hover:bg-red-500 text-white px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 hover:scale-105 shadow-2xl\">\n                        <Play size={24} fill=\"currentColor\" />\n                        <span>Play</span>\n                      </div>\n                    </Link>\n\n                    <Link\n                      href={`/${currentItem.type}s/${currentItem.imdbId}`}\n                      className=\"focus-ring rounded-2xl\"\n                    >\n                      <div className=\"flex items-center space-x-3 bg-black/60 backdrop-blur-sm border border-gray-600 px-8 py-4 rounded-2xl font-bold text-lg text-white hover:bg-black/80 transition-all duration-300 hover:scale-105\">\n                        <Info size={24} />\n                        <span>More Info</span>\n                      </div>\n                    </Link>\n\n                    <button\n                      onClick={() => setIsMuted(!isMuted)}\n                      className=\"glass-elevated p-4 rounded-full hover:bg-white/20 transition-all duration-300 hover:scale-110 focus-ring\"\n                    >\n                      {isMuted ? <VolumeX size={24} className=\"text-white\" /> : <Volume2 size={24} className=\"text-white\" />}\n                    </button>\n                  </div>\n                </motion.div>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </AnimatePresence>\n\n      {/* Premium Navigation Arrows */}\n      {items.length > 1 && (\n        <>\n          <button\n            onClick={goToPrevious}\n            className=\"absolute left-8 top-1/2 -translate-y-1/2 w-14 h-14 glass-elevated rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 hover:scale-110 z-30 focus-ring opacity-0 hover:opacity-100 group-hover:opacity-100\"\n          >\n            <ChevronLeft size={28} />\n          </button>\n          <button\n            onClick={goToNext}\n            className=\"absolute right-8 top-1/2 -translate-y-1/2 w-14 h-14 glass-elevated rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-all duration-300 hover:scale-110 z-30 focus-ring opacity-0 hover:opacity-100 group-hover:opacity-100\"\n          >\n            <ChevronRight size={28} />\n          </button>\n        </>\n      )}\n\n      {/* Premium Progress Indicators */}\n      {items.length > 1 && (\n        <div className=\"absolute bottom-8 left-1/2 -translate-x-1/2 z-30\">\n          <div className=\"flex items-center space-x-3\">\n            {items.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className=\"group focus-ring rounded-full p-1\"\n              >\n                <div className=\"relative\">\n                  {/* Background bar */}\n                  <div className=\"w-12 h-1 bg-white/30 rounded-full overflow-hidden\">\n                    {/* Progress bar */}\n                    <div\n                      className={cn(\n                        'h-full bg-white rounded-full transition-all duration-300',\n                        index === currentIndex ? 'w-full' : 'w-0'\n                      )}\n                    />\n                  </div>\n                  {/* Hover indicator */}\n                  <div className={cn(\n                    'absolute inset-0 bg-white/50 rounded-full transition-opacity duration-200',\n                    index === currentIndex ? 'opacity-0' : 'opacity-0 group-hover:opacity-100'\n                  )} />\n                </div>\n              </button>\n            ))}\n          </div>\n\n          {/* Auto-play indicator */}\n          <div className=\"flex items-center justify-center mt-4\">\n            <div className={cn(\n              'w-2 h-2 rounded-full transition-all duration-300',\n              isAutoPlaying ? 'bg-green-400 animate-pulse' : 'bg-gray-500'\n            )} />\n          </div>\n        </div>\n      )}\n\n      {/* Subtle Ambient Effect */}\n      <div className=\"absolute inset-0 pointer-events-none\">\n        <div className=\"absolute bottom-0 left-0 w-1/3 h-1/3 bg-white/5 blur-3xl\" />\n        <div className=\"absolute top-0 right-0 w-1/3 h-1/3 bg-white/5 blur-3xl\" />\n      </div>\n    </div>\n  );\n};\n\nexport default HeroCarousel;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AAPA;;;;;;;AAwBA,MAAM,eAA4C,CAAC,EAAE,KAAK,EAAE;;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,iBAAiB,MAAM,MAAM,IAAI,GAAG;YAEzC,MAAM,WAAW;mDAAY;oBAC3B;2DAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;gBACrD;kDAAG,OAAO,mCAAmC;YAE7C;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;QAAe,MAAM,MAAM;KAAC;IAEhC,qBAAqB;IACrB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,MAAM,MAAM,GAAG,GAAG;gBACpB,MAAM,YAAY,CAAC,eAAe,CAAC,IAAI,MAAM,MAAM;gBACnD,MAAM,MAAM,IAAI,OAAO,KAAK;gBAC5B,IAAI,GAAG,GAAG,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,CAAC,UAAU,CAAC,SAAS;YAClD;QACF;iCAAG;QAAC;QAAc;KAAM;IAExB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAC/B;0DAAgB,CAAC,OAAS,CAAC,OAAO,IAAI,MAAM,MAAM,IAAI,MAAM,MAAM;;YAClE,iBAAiB;YACjB;0DAAW,IAAM,iBAAiB;yDAAO;QAC3C;iDAAG;QAAC,MAAM,MAAM;KAAC;IAEjB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CAAE;YAC3B;sDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;YACnD,iBAAiB;YACjB;sDAAW,IAAM,iBAAiB;qDAAO;QAC3C;6CAAG;QAAC,MAAM,MAAM;KAAC;IAEjB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;YAC7B,gBAAgB;YAChB,iBAAiB;YACjB;uDAAW,IAAM,iBAAiB;sDAAO;QAC3C;8CAAG,EAAE;IAEL,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;yDAAiB,CAAC;oBACtB,sEAAsE;oBACtE,MAAM,SAAS,EAAE,MAAM;oBACvB,IAAI,OAAO,OAAO,KAAK,WAAW,OAAO,OAAO,KAAK,cAAc,OAAO,iBAAiB,EAAE;wBAC3F;oBACF;oBAEA,IAAI,EAAE,GAAG,KAAK,aAAa;oBAC3B,IAAI,EAAE,GAAG,KAAK,cAAc;oBAC5B,IAAI,EAAE,GAAG,KAAK,KAAK;wBACjB,EAAE,cAAc;wBAChB;qEAAiB,CAAA,OAAQ,CAAC;;oBAC5B;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;0CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;iCAAG;QAAC;QAAc;KAAS;IAE3B,IAAI,CAAC,MAAM,MAAM,EAAE;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4BAAC,MAAM;4BAAI,WAAU;;;;;;;;;;;kCAE5B,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAI7C;IAEA,MAAM,cAAc,KAAK,CAAC,aAAa;IAEvC,qBACE,6LAAC;QACC,WAAU;QACV,cAAc,IAAM,iBAAiB;QACrC,cAAc,IAAM,iBAAiB;;0BAErC,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBACnC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAK;oBAChC,YAAY;wBACV,UAAU;wBACV,MAAM;4BAAC;4BAAM;4BAAG;4BAAK;yBAAE;oBACzB;oBACA,WAAU;oBACV,qBAAqB,IAAM,YAAY;;sCAGvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAI;oCACtB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,UAAU;wCAAG,MAAM;oCAAS;oCAC1C,WAAU;8CAEV,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,YAAY,SAAS;wCACtC,KAAK,YAAY,KAAK;wCACtB,IAAI;wCACJ,WAAU;wCACV,QAAQ;wCACR,OAAM;wCACN,SAAS;;;;;;;;;;;8CAKb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,SAAS;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAC5B,YAAY;4CACV,OAAO;4CACP,UAAU;4CACV,MAAM;gDAAC;gDAAM;gDAAG;gDAAK;6CAAE;wCACzB;wCACA,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEACb,YAAY,IAAI,KAAK,UAAU,iBAAiB;;;;;;;;;;;;0DAKrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,YAAY,KAAK;;;;;;kEAIpB,6LAAC;wDAAI,WAAU;;4DACZ,YAAY,IAAI,kBACf,6LAAC;gEAAK,WAAU;0EAA6B,YAAY,IAAI;;;;;;4DAE9D,YAAY,UAAU,kBACrB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,qMAAA,CAAA,OAAI;wEAAC,MAAM;wEAAI,WAAU;;;;;;kFAC1B,6LAAC;wEAAK,WAAU;kFAAwB,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,UAAU;;;;;;;;;;;;0EAG/E,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAyB;;;;;;;;;;;;;;;;;;;;;;;4CAM9C,YAAY,WAAW,kBACtB,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,YAAY,WAAW,EAAE;;;;;;0DAK3C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,OAAO,EAAE,YAAY,IAAI,CAAC,CAAC,EAAE,YAAY,MAAM,EAAE;wDACxD,WAAU;kEAEV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,MAAM;oEAAI,MAAK;;;;;;8EACrB,6LAAC;8EAAK;;;;;;;;;;;;;;;;;kEAIV,6LAAC,+JAAA,CAAA,UAAI;wDACH,MAAM,CAAC,CAAC,EAAE,YAAY,IAAI,CAAC,EAAE,EAAE,YAAY,MAAM,EAAE;wDACnD,WAAU;kEAEV,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,qMAAA,CAAA,OAAI;oEAAC,MAAM;;;;;;8EACZ,6LAAC;8EAAK;;;;;;;;;;;;;;;;;kEAIV,6LAAC;wDACC,SAAS,IAAM,WAAW,CAAC;wDAC3B,WAAU;kEAET,wBAAU,6LAAC,+MAAA,CAAA,UAAO;4DAAC,MAAM;4DAAI,WAAU;;;;;iFAAkB,6LAAC,+MAAA,CAAA,UAAO;4DAAC,MAAM;4DAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBAlH9F;;;;;;;;;;YA6HR,MAAM,MAAM,GAAG,mBACd;;kCACE,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;4BAAC,MAAM;;;;;;;;;;;kCAErB,6LAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;4BAAC,MAAM;;;;;;;;;;;;;YAMzB,MAAM,MAAM,GAAG,mBACd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,6LAAC;gCAEC,SAAS,IAAM,UAAU;gCACzB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDAEb,cAAA,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,UAAU,eAAe,WAAW;;;;;;;;;;;sDAK1C,6LAAC;4CAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6EACA,UAAU,eAAe,cAAc;;;;;;;;;;;;+BAlBtC;;;;;;;;;;kCA0BX,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,oDACA,gBAAgB,+BAA+B;;;;;;;;;;;;;;;;;0BAOvD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;AAIvB;GAtRM;KAAA;uCAwRS", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentCard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { Play, Star, Calendar, Info, Plus } from 'lucide-react';\nimport { cn, getImageUrl, formatRating, truncateText } from '@/lib/utils';\n\ninterface ContentCardProps {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series' | 'episode';\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n  seriesPosterUrl?: string; // For episodes to use series poster\n  className?: string;\n}\n\nconst ContentCard: React.FC<ContentCardProps> = ({\n  id,\n  imdbId,\n  title,\n  year,\n  posterUrl,\n  imdbRating,\n  description,\n  type,\n  season,\n  episode,\n  seriesTitle,\n  seriesPosterUrl,\n  className\n}) => {\n  const [isHovered, setIsHovered] = useState(false);\n\n  const href = type === 'movie'\n    ? `/watch/movie/${imdbId}`\n    : type === 'series'\n    ? `/watch/series/${imdbId}`\n    : `/watch/series/${imdbId}?season=${season}&episode=${episode}`;\n\n  const displayTitle = type === 'episode' ? seriesTitle : title;\n  const subtitle = type === 'episode' ? `S${season}E${episode}: ${title}` : year ? `${year}` : '';\n\n  // Use series poster for episodes, fallback to episode poster, then to placeholder\n  const displayPosterUrl = type === 'episode' ? (seriesPosterUrl || posterUrl) : posterUrl;\n\n  return (\n    <Link href={href} className=\"block\">\n      <div\n        className={cn(\n          'group relative transition-all duration-500 ease-out cursor-pointer',\n          'hover:transform hover:scale-105 hover:-translate-y-2',\n          'hover:shadow-2xl hover:shadow-gray-900/50',\n          className\n        )}\n        onMouseEnter={() => setIsHovered(true)}\n        onMouseLeave={() => setIsHovered(false)}\n      >\n        {/* Main Card Container */}\n        <div className=\"relative aspect-[2/3] bg-gray-900 overflow-hidden rounded-2xl border border-gray-800/50 hover:border-gray-700/50 transition-all duration-500\">\n          {/* Poster Image */}\n          <Image\n            src={getImageUrl(displayPosterUrl)}\n            alt={displayTitle || 'Content poster'}\n            fill\n            className={cn(\n              'object-cover transition-all duration-500 ease-out',\n              isHovered ? 'scale-110 brightness-75' : 'scale-100 brightness-100'\n            )}\n            sizes=\"(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw\"\n            priority={false}\n          />\n\n          {/* Enhanced Gradient Overlay */}\n          <div className={cn(\n            'absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent transition-all duration-500',\n            isHovered ? 'opacity-100' : 'opacity-70'\n          )} />\n\n          {/* Premium Glow Effect */}\n          <div className={cn(\n            'absolute inset-0 rounded-2xl transition-all duration-500',\n            isHovered ? 'shadow-[inset_0_0_40px_rgba(255,255,255,0.1)]' : 'shadow-none'\n          )} />\n\n          {/* Enhanced Rating Badge */}\n          {imdbRating && (\n            <div className=\"absolute top-4 right-4 z-10\">\n              <div className=\"glass-elevated px-3 py-2 rounded-xl flex items-center space-x-2 backdrop-blur-sm border border-gray-700/50\">\n                <Star size={14} className=\"text-yellow-500 fill-current\" />\n                <span className=\"text-white text-sm font-bold\">{formatRating(imdbRating)}</span>\n              </div>\n            </div>\n          )}\n\n          {/* Enhanced Hover Play Button */}\n          <div className={cn(\n            'absolute inset-0 flex items-center justify-center transition-all duration-500 pointer-events-none',\n            isHovered ? 'opacity-100 scale-100' : 'opacity-0 scale-75'\n          )}>\n            <div className=\"w-20 h-20 bg-gradient-to-br from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-2xl border-2 border-white/20 backdrop-blur-sm\">\n              <Play size={28} className=\"text-white fill-current ml-1\" />\n            </div>\n          </div>\n\n          {/* Enhanced Bottom Content Info */}\n          <div className=\"absolute bottom-0 left-0 right-0 p-4 pointer-events-none\">\n            <h3 className=\"text-white font-bold text-base mb-2 line-clamp-2 leading-tight drop-shadow-lg\">\n              {displayTitle}\n            </h3>\n\n            {subtitle && (\n              <p className=\"text-gray-300 text-sm font-medium drop-shadow-md\">\n                {subtitle}\n              </p>\n            )}\n          </div>\n\n          {/* Premium Border Glow */}\n          <div className={cn(\n            'absolute inset-0 rounded-2xl border-2 transition-all duration-500 pointer-events-none',\n            isHovered ? 'border-gray-500/50 shadow-[0_0_30px_rgba(156,163,175,0.3)]' : 'border-transparent'\n          )} />\n        </div>\n      </div>\n    </Link>\n  );\n};\n\nexport default ContentCard;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;;;AANA;;;;;;AAwBA,MAAM,cAA0C,CAAC,EAC/C,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,UAAU,EACV,WAAW,EACX,IAAI,EACJ,MAAM,EACN,OAAO,EACP,WAAW,EACX,eAAe,EACf,SAAS,EACV;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,SAAS,UAClB,CAAC,aAAa,EAAE,QAAQ,GACxB,SAAS,WACT,CAAC,cAAc,EAAE,QAAQ,GACzB,CAAC,cAAc,EAAE,OAAO,QAAQ,EAAE,OAAO,SAAS,EAAE,SAAS;IAEjE,MAAM,eAAe,SAAS,YAAY,cAAc;IACxD,MAAM,WAAW,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,QAAQ,EAAE,EAAE,OAAO,GAAG,OAAO,GAAG,MAAM,GAAG;IAE7F,kFAAkF;IAClF,MAAM,mBAAmB,SAAS,YAAa,mBAAmB,YAAa;IAE/E,qBACE,6LAAC,+JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAU;kBAC1B,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA,wDACA,6CACA;YAEF,cAAc,IAAM,aAAa;YACjC,cAAc,IAAM,aAAa;sBAGjC,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,gIAAA,CAAA,UAAK;wBACJ,KAAK,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;wBACjB,KAAK,gBAAgB;wBACrB,IAAI;wBACJ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA,YAAY,4BAA4B;wBAE1C,OAAM;wBACN,UAAU;;;;;;kCAIZ,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,2GACA,YAAY,gBAAgB;;;;;;kCAI9B,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,4DACA,YAAY,kDAAkD;;;;;;oBAI/D,4BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;oCAAI,WAAU;;;;;;8CAC1B,6LAAC;oCAAK,WAAU;8CAAgC,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE;;;;;;;;;;;;;;;;;kCAMnE,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,qGACA,YAAY,0BAA0B;kCAEtC,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;;;;;;kCAK9B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX;;;;;;4BAGF,0BACC,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAMP,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,yFACA,YAAY,+DAA+D;;;;;;;;;;;;;;;;;;;;;;AAMvF;GA9GM;KAAA;uCAgHS", "debugId": null}}, {"offset": {"line": 854, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ContentSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport ContentCard from './ContentCard';\n\ninterface ContentItem {\n  id: string;\n  imdbId: string;\n  title: string;\n  year?: number;\n  posterUrl?: string;\n  seriesPosterUrl?: string; // For episodes to use their series poster\n  imdbRating?: number;\n  description?: string;\n  type: 'movie' | 'series' | 'episode';\n  season?: number;\n  episode?: number;\n  seriesTitle?: string;\n}\n\ninterface ContentSectionProps {\n  title: string;\n  items: ContentItem[];\n  viewAllHref?: string;\n  className?: string;\n}\n\nconst ContentSection: React.FC<ContentSectionProps> = ({\n  title,\n  items,\n  viewAllHref,\n  className\n}) => {\n  const scrollContainerRef = useRef<HTMLDivElement>(null);\n  const [canScrollLeft, setCanScrollLeft] = useState(false);\n  const [canScrollRight, setCanScrollRight] = useState(true);\n  const [isHovered, setIsHovered] = useState(false);\n\n  const checkScrollButtons = () => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    setCanScrollLeft(container.scrollLeft > 0);\n    setCanScrollRight(\n      container.scrollLeft < container.scrollWidth - container.clientWidth - 1\n    );\n  };\n\n  useEffect(() => {\n    checkScrollButtons();\n    const container = scrollContainerRef.current;\n    if (container) {\n      container.addEventListener('scroll', checkScrollButtons);\n      return () => container.removeEventListener('scroll', checkScrollButtons);\n    }\n  }, [items]);\n\n  const scroll = (direction: 'left' | 'right') => {\n    if (!scrollContainerRef.current) return;\n\n    const container = scrollContainerRef.current;\n    const cardWidth = 280; // Approximate card width including gap\n    const scrollAmount = cardWidth * 4; // Scroll 4 cards at a time\n\n    container.scrollBy({\n      left: direction === 'left' ? -scrollAmount : scrollAmount,\n      behavior: 'smooth'\n    });\n  };\n\n  if (!items.length) {\n    return null;\n  }\n\n  return (\n    <section\n      className={cn('py-12 animate-fade-in', className)}\n      onMouseEnter={() => setIsHovered(true)}\n      onMouseLeave={() => setIsHovered(false)}\n    >\n      <div className=\"max-w-[1920px] mx-auto px-6 lg:px-12\">\n        {/* Section Header */}\n        <div className=\"flex items-center justify-between mb-8\">\n          <h2 className=\"text-3xl lg:text-4xl font-bold text-white tracking-tight\">\n            {title}\n          </h2>\n          {viewAllHref && (\n            <Link\n              href={viewAllHref}\n              className=\"flex items-center space-x-2 text-gray-400 hover:text-red-500 transition-all duration-300 group focus-ring rounded-lg px-3 py-2\"\n            >\n              <span className=\"font-medium\">View All</span>\n              <ArrowRight size={18} className=\"group-hover:translate-x-1 transition-transform duration-300\" />\n            </Link>\n          )}\n        </div>\n\n        {/* Content Container */}\n        <div className=\"relative group/section\">\n          {/* Left Navigation Button */}\n          <button\n            onClick={() => scroll('left')}\n            disabled={!canScrollLeft}\n            className={cn(\n              'absolute left-0 top-1/2 -translate-y-1/2 -translate-x-16 z-20',\n              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',\n              'transition-all duration-300 focus-ring shadow-2xl',\n              'hover:scale-110 hover:bg-red-600/20',\n              isHovered && canScrollLeft ? 'opacity-100 -translate-x-8' : 'opacity-0 -translate-x-16',\n              !canScrollLeft && 'cursor-not-allowed opacity-30'\n            )}\n          >\n            <ChevronLeft size={26} className=\"text-white\" />\n          </button>\n\n          {/* Right Navigation Button */}\n          <button\n            onClick={() => scroll('right')}\n            disabled={!canScrollRight}\n            className={cn(\n              'absolute right-0 top-1/2 -translate-y-1/2 translate-x-16 z-20',\n              'w-14 h-14 glass-elevated rounded-full flex items-center justify-center',\n              'transition-all duration-300 focus-ring shadow-2xl',\n              'hover:scale-110 hover:bg-red-600/20',\n              isHovered && canScrollRight ? 'opacity-100 translate-x-8' : 'opacity-0 translate-x-16',\n              !canScrollRight && 'cursor-not-allowed opacity-30'\n            )}\n          >\n            <ChevronRight size={26} className=\"text-white\" />\n          </button>\n\n          {/* Scrollable Content */}\n          <div\n            ref={scrollContainerRef}\n            className=\"flex space-x-6 overflow-x-auto scrollbar-hide pb-6 scroll-smooth\"\n            style={{ scrollSnapType: 'x mandatory' }}\n          >\n            {items.map((item, index) => (\n              <div\n                key={`${item.type}-${item.id}`}\n                className=\"flex-none w-64 lg:w-72 animate-slide-up\"\n                style={{\n                  scrollSnapAlign: 'start',\n                  animationDelay: `${index * 0.1}s`\n                }}\n              >\n                <ContentCard\n                  id={item.id}\n                  imdbId={item.imdbId}\n                  title={item.title}\n                  year={item.year}\n                  posterUrl={item.posterUrl}\n                  seriesPosterUrl={item.seriesPosterUrl}\n                  imdbRating={item.imdbRating}\n                  description={item.description}\n                  type={item.type}\n                  season={item.season}\n                  episode={item.episode}\n                  seriesTitle={item.seriesTitle}\n                />\n              </div>\n            ))}\n          </div>\n\n          {/* Premium Fade Gradients */}\n          <div className=\"absolute left-0 top-0 bottom-6 w-16 bg-gradient-to-r from-black via-black/80 to-transparent pointer-events-none z-10\" />\n          <div className=\"absolute right-0 top-0 bottom-6 w-16 bg-gradient-to-l from-black via-black/80 to-transparent pointer-events-none z-10\" />\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"flex justify-center mt-6\">\n          <div className=\"flex space-x-2\">\n            {Array.from({ length: Math.ceil(items.length / 4) }).map((_, index) => (\n              <div\n                key={index}\n                className=\"w-2 h-2 rounded-full bg-white/20 transition-all duration-300\"\n              />\n            ))}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ContentSection;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;AACA;;;AANA;;;;;;AA8BA,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,KAAK,EACL,WAAW,EACX,SAAS,EACV;;IACC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,qBAAqB;QACzB,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,iBAAiB,UAAU,UAAU,GAAG;QACxC,kBACE,UAAU,UAAU,GAAG,UAAU,WAAW,GAAG,UAAU,WAAW,GAAG;IAE3E;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;YACA,MAAM,YAAY,mBAAmB,OAAO;YAC5C,IAAI,WAAW;gBACb,UAAU,gBAAgB,CAAC,UAAU;gBACrC;gDAAO,IAAM,UAAU,mBAAmB,CAAC,UAAU;;YACvD;QACF;mCAAG;QAAC;KAAM;IAEV,MAAM,SAAS,CAAC;QACd,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,YAAY,mBAAmB,OAAO;QAC5C,MAAM,YAAY,KAAK,uCAAuC;QAC9D,MAAM,eAAe,YAAY,GAAG,2BAA2B;QAE/D,UAAU,QAAQ,CAAC;YACjB,MAAM,cAAc,SAAS,CAAC,eAAe;YAC7C,UAAU;QACZ;IACF;IAEA,IAAI,CAAC,MAAM,MAAM,EAAE;QACjB,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACvC,cAAc,IAAM,aAAa;QACjC,cAAc,IAAM,aAAa;kBAEjC,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF,6BACC,6LAAC,+JAAA,CAAA,UAAI;4BACH,MAAM;4BACN,WAAU;;8CAEV,6LAAC;oCAAK,WAAU;8CAAc;;;;;;8CAC9B,6LAAC,qNAAA,CAAA,aAAU;oCAAC,MAAM;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAMtC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,UAAU,CAAC;4BACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,0EACA,qDACA,uCACA,aAAa,gBAAgB,+BAA+B,6BAC5D,CAAC,iBAAiB;sCAGpB,cAAA,6LAAC,uNAAA,CAAA,cAAW;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;sCAInC,6LAAC;4BACC,SAAS,IAAM,OAAO;4BACtB,UAAU,CAAC;4BACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA,0EACA,qDACA,uCACA,aAAa,iBAAiB,8BAA8B,4BAC5D,CAAC,kBAAkB;sCAGrB,cAAA,6LAAC,yNAAA,CAAA,eAAY;gCAAC,MAAM;gCAAI,WAAU;;;;;;;;;;;sCAIpC,6LAAC;4BACC,KAAK;4BACL,WAAU;4BACV,OAAO;gCAAE,gBAAgB;4BAAc;sCAEtC,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oCAEC,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;oCACnC;8CAEA,cAAA,6LAAC,oIAAA,CAAA,UAAW;wCACV,IAAI,KAAK,EAAE;wCACX,QAAQ,KAAK,MAAM;wCACnB,OAAO,KAAK,KAAK;wCACjB,MAAM,KAAK,IAAI;wCACf,WAAW,KAAK,SAAS;wCACzB,iBAAiB,KAAK,eAAe;wCACrC,YAAY,KAAK,UAAU;wCAC3B,aAAa,KAAK,WAAW;wCAC7B,MAAM,KAAK,IAAI;wCACf,QAAQ,KAAK,MAAM;wCACnB,SAAS,KAAK,OAAO;wCACrB,aAAa,KAAK,WAAW;;;;;;mCAnB1B,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;sCA0BpC,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ,KAAK,IAAI,CAAC,MAAM,MAAM,GAAG;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC3D,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;;;;;;;;;;;;;;;;;AASrB;GA5JM;KAAA;uCA8JS", "debugId": null}}, {"offset": {"line": 1108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Downloads/New%20folder%20%2825%29/streamzen/src/components/ClientInitializeButton.tsx"], "sourcesContent": ["'use client';\n\nimport dynamic from 'next/dynamic';\n\nconst InitializeButton = dynamic(() => import('./InitializeButton'), {\n  ssr: false,\n  loading: () => (\n    <div className=\"animate-pulse bg-gray-700 h-12 w-48 rounded-lg mx-auto\"></div>\n  )\n});\n\nexport default InitializeButton;\n"], "names": [], "mappings": ";;;;AAEA;;AAFA;;;AAIA,MAAM,mBAAmB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,OAAE;;;;;;IAC/B,KAAK;IACL,SAAS,kBACP,6LAAC;YAAI,WAAU;;;;;;;;uCAIJ", "debugId": null}}]}