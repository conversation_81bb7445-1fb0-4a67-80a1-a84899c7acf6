const axios = require('axios');

async function debugBlackButler() {
  try {
    console.log('🔍 Debugging <PERSON> (tt1316554) episode data...');
    
    // Check what episodes exist in database
    console.log('\n📺 Current episodes in database:');
    const episodesResponse = await axios.get('http://localhost:3000/api/series/tt1316554/episodes');
    const allEpisodes = episodesResponse.data;
    
    console.log(`Total episodes: ${allEpisodes.length}`);
    
    // Group by season and show details
    const episodesBySeason = allEpisodes.reduce((acc, ep) => {
      if (!acc[ep.season]) acc[ep.season] = [];
      acc[ep.season].push(ep);
      return acc;
    }, {});
    
    console.log('\n📊 Episodes by season:');
    Object.keys(episodesBySeason).sort((a, b) => parseInt(a) - parseInt(b)).forEach(season => {
      const episodes = episodesBySeason[season].sort((a, b) => a.episode - b.episode);
      console.log(`\n🎬 Season ${season}: ${episodes.length} episodes`);
      episodes.forEach(ep => {
        console.log(`  S${ep.season}E${ep.episode}: "${ep.episodeTitle}" (ID: ${ep._id})`);
      });
    });
    
    // Check series info
    console.log('\n📺 Series information:');
    const seriesResponse = await axios.get('http://localhost:3000/api/series/tt1316554');
    const series = seriesResponse.data;
    console.log(`Title: ${series.title}`);
    console.log(`Total Seasons: ${series.totalSeasons}`);
    console.log(`Total Episodes: ${series.totalEpisodes}`);
    
    // The problem: There's likely a Season 5 Episode 13 that shouldn't exist
    const badEpisodes = allEpisodes.filter(ep => ep.season > 2);
    if (badEpisodes.length > 0) {
      console.log('\n❌ FOUND BAD DATA - Episodes with invalid seasons:');
      badEpisodes.forEach(ep => {
        console.log(`  🚨 S${ep.season}E${ep.episode}: "${ep.episodeTitle}" (ID: ${ep._id})`);
        console.log(`     This should not exist - Black Butler only has 2 seasons!`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

debugBlackButler();
