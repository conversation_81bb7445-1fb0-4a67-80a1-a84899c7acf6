'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Search, X, Clock, Star, Calendar, Play } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useSearchSuggestions, SearchSuggestion } from '@/hooks/useSearch';
import Image from 'next/image';

interface SearchBarProps {
  placeholder?: string;
  className?: string;
  showSuggestions?: boolean;
  onSearch?: (query: string) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search movies, series, episodes...",
  className = "",
  showSuggestions = true,
  onSearch
}) => {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const router = useRouter();
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { suggestions, isLoading } = useSearchSuggestions(query, 8);

  // Close suggestions when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSearch = (searchQuery: string = query) => {
    if (!searchQuery.trim()) return;
    
    setIsOpen(false);
    setSelectedIndex(-1);
    
    if (onSearch) {
      onSearch(searchQuery.trim());
    } else {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions || !isOpen) {
      if (e.key === 'Enter') {
        handleSearch();
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > -1 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && suggestions[selectedIndex]) {
          handleSuggestionClick(suggestions[selectedIndex]);
        } else {
          handleSearch();
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setIsOpen(false);
    setSelectedIndex(-1);
    
    // Navigate based on content type
    if (suggestion.type === 'movie') {
      router.push(`/watch/movie/${suggestion.imdbId}`);
    } else if (suggestion.type === 'series') {
      router.push(`/watch/series/${suggestion.imdbId}`);
    } else if (suggestion.type === 'episode') {
      router.push(`/watch/series/${suggestion.seriesImdbId}?season=${suggestion.season}&episode=${suggestion.episode}`);
    }
  };

  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  const formatRating = (rating?: number) => {
    return rating ? rating.toFixed(1) : 'N/A';
  };

  const formatDate = (date?: string) => {
    if (!date) return '';
    return new Date(date).getFullYear().toString();
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* Search Input */}
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
          <Search className="h-5 w-5 text-gray-400" />
        </div>
        
        <input
          ref={inputRef}
          type="text"
          value={query}
          onChange={(e) => {
            setQuery(e.target.value);
            setIsOpen(showSuggestions && e.target.value.trim().length >= 2);
            setSelectedIndex(-1);
          }}
          onFocus={() => {
            if (showSuggestions && query.trim().length >= 2) {
              setIsOpen(true);
            }
          }}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="w-full h-12 pl-12 pr-12 bg-gray-800/50 border border-gray-700 rounded-2xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 transition-all duration-200"
        />
        
        {query && (
          <button
            onClick={clearSearch}
            className="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-white transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        )}
      </div>

      {/* Search Suggestions */}
      {showSuggestions && isOpen && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-2xl z-50 max-h-96 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-gray-400">
              <div className="animate-spin w-6 h-6 border-2 border-red-500 border-t-transparent rounded-full mx-auto"></div>
              <p className="mt-2 text-sm">Searching...</p>
            </div>
          ) : suggestions.length > 0 ? (
            <div className="py-2">
              {suggestions.map((suggestion, index) => (
                <button
                  key={`${suggestion.type}-${suggestion._id}`}
                  onClick={() => handleSuggestionClick(suggestion)}
                  className={`w-full px-4 py-3 flex items-center space-x-3 hover:bg-gray-800/50 transition-colors text-left ${
                    selectedIndex === index ? 'bg-gray-800/50' : ''
                  }`}
                >
                  {/* Poster/Thumbnail */}
                  <div className="flex-shrink-0 w-12 h-16 bg-gray-800 rounded-lg overflow-hidden">
                    {(suggestion.posterUrl || suggestion.seriesPoster) ? (
                      <Image
                        src={suggestion.posterUrl || suggestion.seriesPoster || ''}
                        alt={suggestion.title}
                        width={48}
                        height={64}
                        className="w-full h-full object-cover"
                        unoptimized
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Play className="w-6 h-6 text-gray-600" />
                      </div>
                    )}
                  </div>

                  {/* Content Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-white font-medium truncate">
                        {suggestion.title}
                      </h4>
                      <span className={`px-2 py-0.5 text-xs rounded-full ${
                        suggestion.type === 'movie' ? 'bg-blue-600/20 text-blue-400' :
                        suggestion.type === 'series' ? 'bg-green-600/20 text-green-400' :
                        'bg-purple-600/20 text-purple-400'
                      }`}>
                        {suggestion.type === 'episode' ? 'EP' : suggestion.type.toUpperCase()}
                      </span>
                    </div>

                    {/* Episode specific info */}
                    {suggestion.type === 'episode' && (
                      <p className="text-gray-400 text-sm truncate mb-1">
                        {suggestion.seriesTitle} • S{suggestion.season}E{suggestion.episode}
                      </p>
                    )}

                    {/* Series specific info */}
                    {suggestion.type === 'series' && suggestion.totalSeasons && (
                      <p className="text-gray-400 text-sm mb-1">
                        {suggestion.totalSeasons} Season{suggestion.totalSeasons > 1 ? 's' : ''}
                      </p>
                    )}

                    {/* Rating and Date */}
                    <div className="flex items-center space-x-3 text-xs text-gray-500">
                      {suggestion.rating && (
                        <div className="flex items-center space-x-1">
                          <Star className="w-3 h-3 text-yellow-500" />
                          <span>{formatRating(suggestion.rating)}</span>
                        </div>
                      )}
                      {(suggestion.releaseDate || suggestion.airDate) && (
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>{formatDate(suggestion.releaseDate || suggestion.airDate)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
              
              {/* View All Results */}
              <div className="border-t border-gray-700/50 mt-2 pt-2">
                <button
                  onClick={() => handleSearch()}
                  className="w-full px-4 py-2 text-center text-red-400 hover:text-red-300 text-sm font-medium transition-colors"
                >
                  View all results for "{query}"
                </button>
              </div>
            </div>
          ) : query.trim().length >= 2 ? (
            <div className="p-4 text-center text-gray-400">
              <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No results found for "{query}"</p>
              <p className="text-xs mt-1">Try different keywords or check spelling</p>
            </div>
          ) : null}
        </div>
      )}
    </div>
  );
};

export default SearchBar;
